// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXContainerItemProxy section */
		DDF08A762D2BBD3A00EF3A0C /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = DDF08A5A2D2BBD3900EF3A0C /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = DDF08A612D2BBD3900EF3A0C;
			remoteInfo = "Priority Matrix";
		};
		DDF08A802D2BBD3A00EF3A0C /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = DDF08A5A2D2BBD3900EF3A0C /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = DDF08A612D2BBD3900EF3A0C;
			remoteInfo = "Priority Matrix";
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		DDF08A622D2BBD3900EF3A0C /* Priority Matrix.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = "Priority Matrix.app"; sourceTree = BUILT_PRODUCTS_DIR; };
		DDF08A752D2BBD3A00EF3A0C /* Priority MatrixTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = "Priority MatrixTests.xctest"; sourceTree = BUILT_PRODUCTS_DIR; };
		DDF08A7F2D2BBD3A00EF3A0C /* Priority MatrixUITests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = "Priority MatrixUITests.xctest"; sourceTree = BUILT_PRODUCTS_DIR; };
		DDF08ADE2D33FECB00EF3A0C /* Smart_ToDo.mobileprovision */ = {isa = PBXFileReference; lastKnownFileType = file; name = Smart_ToDo.mobileprovision; path = ../../Downloads/Smart_ToDo.mobileprovision; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedBuildFileExceptionSet section */
		DDF08A872D2BBD3A00EF3A0C /* Exceptions for "Priority Matrix" folder in "Priority Matrix" target */ = {
			isa = PBXFileSystemSynchronizedBuildFileExceptionSet;
			membershipExceptions = (
				Config/Debug.xcconfig,
				Config/Release.xcconfig,
				Info.plist,
			);
			target = DDF08A612D2BBD3900EF3A0C /* Priority Matrix */;
		};
/* End PBXFileSystemSynchronizedBuildFileExceptionSet section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		DDF08A642D2BBD3900EF3A0C /* Priority Matrix */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			exceptions = (
				DDF08A872D2BBD3A00EF3A0C /* Exceptions for "Priority Matrix" folder in "Priority Matrix" target */,
			);
			path = "Priority Matrix";
			sourceTree = "<group>";
		};
		DDF08A782D2BBD3A00EF3A0C /* Priority MatrixTests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = "Priority MatrixTests";
			sourceTree = "<group>";
		};
		DDF08A822D2BBD3A00EF3A0C /* Priority MatrixUITests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = "Priority MatrixUITests";
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		DDF08A5F2D2BBD3900EF3A0C /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		DDF08A722D2BBD3A00EF3A0C /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		DDF08A7C2D2BBD3A00EF3A0C /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		DDF08A592D2BBD3900EF3A0C = {
			isa = PBXGroup;
			children = (
				DDF08A642D2BBD3900EF3A0C /* Priority Matrix */,
				DDF08A782D2BBD3A00EF3A0C /* Priority MatrixTests */,
				DDF08A822D2BBD3A00EF3A0C /* Priority MatrixUITests */,
				DDF08ADD2D33FECB00EF3A0C /* Frameworks */,
				DDF08A632D2BBD3900EF3A0C /* Products */,
			);
			sourceTree = "<group>";
		};
		DDF08A632D2BBD3900EF3A0C /* Products */ = {
			isa = PBXGroup;
			children = (
				DDF08A622D2BBD3900EF3A0C /* Priority Matrix.app */,
				DDF08A752D2BBD3A00EF3A0C /* Priority MatrixTests.xctest */,
				DDF08A7F2D2BBD3A00EF3A0C /* Priority MatrixUITests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		DDF08ADD2D33FECB00EF3A0C /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				DDF08ADE2D33FECB00EF3A0C /* Smart_ToDo.mobileprovision */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		DDF08A612D2BBD3900EF3A0C /* Priority Matrix */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = DDF08A882D2BBD3A00EF3A0C /* Build configuration list for PBXNativeTarget "Priority Matrix" */;
			buildPhases = (
				DDF08A5E2D2BBD3900EF3A0C /* Sources */,
				DDF08A5F2D2BBD3900EF3A0C /* Frameworks */,
				DDF08A602D2BBD3900EF3A0C /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				DDF08A642D2BBD3900EF3A0C /* Priority Matrix */,
			);
			name = "Priority Matrix";
			packageProductDependencies = (
			);
			productName = "Priority Matrix";
			productReference = DDF08A622D2BBD3900EF3A0C /* Priority Matrix.app */;
			productType = "com.apple.product-type.application";
		};
		DDF08A742D2BBD3A00EF3A0C /* Priority MatrixTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = DDF08A8D2D2BBD3A00EF3A0C /* Build configuration list for PBXNativeTarget "Priority MatrixTests" */;
			buildPhases = (
				DDF08A712D2BBD3A00EF3A0C /* Sources */,
				DDF08A722D2BBD3A00EF3A0C /* Frameworks */,
				DDF08A732D2BBD3A00EF3A0C /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				DDF08A772D2BBD3A00EF3A0C /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				DDF08A782D2BBD3A00EF3A0C /* Priority MatrixTests */,
			);
			name = "Priority MatrixTests";
			packageProductDependencies = (
			);
			productName = "Priority MatrixTests";
			productReference = DDF08A752D2BBD3A00EF3A0C /* Priority MatrixTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		DDF08A7E2D2BBD3A00EF3A0C /* Priority MatrixUITests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = DDF08A902D2BBD3A00EF3A0C /* Build configuration list for PBXNativeTarget "Priority MatrixUITests" */;
			buildPhases = (
				DDF08A7B2D2BBD3A00EF3A0C /* Sources */,
				DDF08A7C2D2BBD3A00EF3A0C /* Frameworks */,
				DDF08A7D2D2BBD3A00EF3A0C /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				DDF08A812D2BBD3A00EF3A0C /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				DDF08A822D2BBD3A00EF3A0C /* Priority MatrixUITests */,
			);
			name = "Priority MatrixUITests";
			packageProductDependencies = (
			);
			productName = "Priority MatrixUITests";
			productReference = DDF08A7F2D2BBD3A00EF3A0C /* Priority MatrixUITests.xctest */;
			productType = "com.apple.product-type.bundle.ui-testing";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		DDF08A5A2D2BBD3900EF3A0C /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1620;
				LastUpgradeCheck = 1630;
				TargetAttributes = {
					DDF08A612D2BBD3900EF3A0C = {
						CreatedOnToolsVersion = 16.2;
					};
					DDF08A742D2BBD3A00EF3A0C = {
						CreatedOnToolsVersion = 16.2;
						TestTargetID = DDF08A612D2BBD3900EF3A0C;
					};
					DDF08A7E2D2BBD3A00EF3A0C = {
						CreatedOnToolsVersion = 16.2;
						TestTargetID = DDF08A612D2BBD3900EF3A0C;
					};
				};
			};
			buildConfigurationList = DDF08A5D2D2BBD3900EF3A0C /* Build configuration list for PBXProject "Priority Matrix" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
				"zh-Hans",
				zh_CN,
			);
			mainGroup = DDF08A592D2BBD3900EF3A0C;
			minimizedProjectReferenceProxies = 1;
			preferredProjectObjectVersion = 77;
			productRefGroup = DDF08A632D2BBD3900EF3A0C /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				DDF08A612D2BBD3900EF3A0C /* Priority Matrix */,
				DDF08A742D2BBD3A00EF3A0C /* Priority MatrixTests */,
				DDF08A7E2D2BBD3A00EF3A0C /* Priority MatrixUITests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		DDF08A602D2BBD3900EF3A0C /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		DDF08A732D2BBD3A00EF3A0C /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		DDF08A7D2D2BBD3A00EF3A0C /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		DDF08A5E2D2BBD3900EF3A0C /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		DDF08A712D2BBD3A00EF3A0C /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		DDF08A7B2D2BBD3A00EF3A0C /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		DDF08A772D2BBD3A00EF3A0C /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = DDF08A612D2BBD3900EF3A0C /* Priority Matrix */;
			targetProxy = DDF08A762D2BBD3A00EF3A0C /* PBXContainerItemProxy */;
		};
		DDF08A812D2BBD3A00EF3A0C /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = DDF08A612D2BBD3900EF3A0C /* Priority Matrix */;
			targetProxy = DDF08A802D2BBD3A00EF3A0C /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		DDF08A892D2BBD3A00EF3A0C /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReferenceAnchor = DDF08A642D2BBD3900EF3A0C /* Priority Matrix */;
			baseConfigurationReferenceRelativePath = Config/Debug.xcconfig;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				ASSETCATALOG_COMPILER_INCLUDE_ALL_APPICON_ASSETS = NO;
				CODE_SIGN_ENTITLEMENTS = "Priority Matrix/Priority Matrix.entitlements";
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_ASSET_PATHS = "\"Priority Matrix/Preview Content\"";
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = "Priority Matrix/Info.plist";
				INFOPLIST_KEY_CFBundleDisplayName = "Smart ToDo";
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.productivity";
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportsDocumentBrowser = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 17.6;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 0.5;
				PRODUCT_BUNDLE_IDENTIFIER = "LIG-Nutrition.Priority-Matrix";
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = Debug;
		};
		DDF08A8A2D2BBD3A00EF3A0C /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReferenceAnchor = DDF08A642D2BBD3900EF3A0C /* Priority Matrix */;
			baseConfigurationReferenceRelativePath = Config/Release.xcconfig;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				ASSETCATALOG_COMPILER_INCLUDE_ALL_APPICON_ASSETS = NO;
				CODE_SIGN_ENTITLEMENTS = "Priority Matrix/Priority Matrix.entitlements";
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_ASSET_PATHS = "\"Priority Matrix/Preview Content\"";
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = "Priority Matrix/Info.plist";
				INFOPLIST_KEY_CFBundleDisplayName = "Smart ToDo";
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.productivity";
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportsDocumentBrowser = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 17.6;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 0.5;
				PRODUCT_BUNDLE_IDENTIFIER = "LIG-Nutrition.Priority-Matrix";
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = Release;
		};
		DDF08A8B2D2BBD3A00EF3A0C /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReferenceAnchor = DDF08A642D2BBD3900EF3A0C /* Priority Matrix */;
			baseConfigurationReferenceRelativePath = Config/Debug.xcconfig;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				DEVELOPMENT_TEAM = WRR3ADG534;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 17.6;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		DDF08A8C2D2BBD3A00EF3A0C /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReferenceAnchor = DDF08A642D2BBD3900EF3A0C /* Priority Matrix */;
			baseConfigurationReferenceRelativePath = Config/Release.xcconfig;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEVELOPMENT_TEAM = WRR3ADG534;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 17.6;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		DDF08A8E2D2BBD3A00EF3A0C /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReferenceAnchor = DDF08A642D2BBD3900EF3A0C /* Priority Matrix */;
			baseConfigurationReferenceRelativePath = Config/Debug.xcconfig;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 17.6;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "LIG-Nutrition.Priority-MatrixTests";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/Priority Matrix.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/Priority Matrix";
			};
			name = Debug;
		};
		DDF08A8F2D2BBD3A00EF3A0C /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReferenceAnchor = DDF08A642D2BBD3900EF3A0C /* Priority Matrix */;
			baseConfigurationReferenceRelativePath = Config/Release.xcconfig;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 17.6;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "LIG-Nutrition.Priority-MatrixTests";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/Priority Matrix.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/Priority Matrix";
			};
			name = Release;
		};
		DDF08A912D2BBD3A00EF3A0C /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "LIG-Nutrition.Priority-MatrixUITests";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = "Priority Matrix";
			};
			name = Debug;
		};
		DDF08A922D2BBD3A00EF3A0C /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "LIG-Nutrition.Priority-MatrixUITests";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = "Priority Matrix";
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		DDF08A5D2D2BBD3900EF3A0C /* Build configuration list for PBXProject "Priority Matrix" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				DDF08A8B2D2BBD3A00EF3A0C /* Debug */,
				DDF08A8C2D2BBD3A00EF3A0C /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		DDF08A882D2BBD3A00EF3A0C /* Build configuration list for PBXNativeTarget "Priority Matrix" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				DDF08A892D2BBD3A00EF3A0C /* Debug */,
				DDF08A8A2D2BBD3A00EF3A0C /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		DDF08A8D2D2BBD3A00EF3A0C /* Build configuration list for PBXNativeTarget "Priority MatrixTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				DDF08A8E2D2BBD3A00EF3A0C /* Debug */,
				DDF08A8F2D2BBD3A00EF3A0C /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		DDF08A902D2BBD3A00EF3A0C /* Build configuration list for PBXNativeTarget "Priority MatrixUITests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				DDF08A912D2BBD3A00EF3A0C /* Debug */,
				DDF08A922D2BBD3A00EF3A0C /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = DDF08A5A2D2BBD3900EF3A0C /* Project object */;
}
