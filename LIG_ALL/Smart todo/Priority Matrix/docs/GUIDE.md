# Priority Matrix 开发指南

## 概述

本指南为 Priority Matrix 开发者提供完整的开发环境设置、编码规范、测试策略和问题排查方案。

## 快速开始

### 环境要求

#### 必要条件
- **Xcode**: 15.0 或更高版本
- **iOS**: 17.0 或更高版本
- **macOS**: 14.0 或更高版本 (用于开发)
- **Swift**: 6.0 或更高版本

#### 推荐配置
- **macOS**: macOS Sonoma 14.0+
- **RAM**: 16GB 或更多
- **存储**: 至少 10GB 可用空间
- **网络**: 稳定的互联网连接（用于 AI 服务）

### 项目设置

#### 1. 克隆项目
```bash
# 使用 SSH (推荐)
<NAME_EMAIL>:your-username/priority-matrix.git

# 或使用 HTTPS
git clone https://github.com/your-username/priority-matrix.git

# 进入项目目录
cd priority-matrix
```

#### 2. 打开项目
```bash
# 使用 Xcode 打开项目
open "Priority Matrix.xcodeproj"
```

#### 3. 检查项目配置
- **Bundle Identifier**: 确保格式为 `com.yourcompany.prioritymatrix`
- **开发团队**: 在 "Signing & Capabilities" 标签页选择开发团队
- **自动签名**: 确保 "Automatically manage signing" 已启用

### 构建命令

#### 基本构建
```bash
# 调试构建
xcodebuild -project "Priority Matrix.xcodeproj" -scheme "Priority Matrix" -configuration Debug build

# 发布构建
xcodebuild -project "Priority Matrix.xcodeproj" -scheme "Priority Matrix" -configuration Release build

# 清理项目
xcodebuild clean -project "Priority Matrix.xcodeproj" -scheme "Priority Matrix"
```

#### 测试命令
```bash
# 运行所有测试
xcodebuild -project "Priority Matrix.xcodeproj" -scheme "Priority Matrix" -configuration Debug test

# 只运行单元测试
xcodebuild -project "Priority Matrix.xcodeproj" -scheme "Priority Matrix" -only-testing "Priority MatrixTests" test

# 只运行 UI 测试
xcodebuild -project "Priority Matrix.xcodeproj" -scheme "Priority Matrix" -only-testing "Priority MatrixUITests" test
```

### API 密钥配置

#### 方式一：环境变量 (推荐)
```bash
# 设置 Claude API 密钥
export CLAUDE_API_KEY_VALUE="sk-ant-your-api-key-here"

# 设置 DeepSeek API 密钥
export DEEPSEEK_API_KEY_VALUE="sk-your-deepseek-key-here"

# 永久设置 (添加到 shell 配置文件)
echo 'export CLAUDE_API_KEY_VALUE="sk-ant-your-api-key-here"' >> ~/.zshrc
echo 'export DEEPSEEK_API_KEY_VALUE="sk-your-deepseek-key-here"' >> ~/.zshrc
source ~/.zshrc
```

#### 方式二：Xcode Scheme 配置
1. 在 Xcode 中选择 "Priority Matrix" scheme
2. 点击 "Edit Scheme..."
3. 选择 "Run" → "Environment Variables"
4. 添加环境变量：
   - `CLAUDE_API_KEY_VALUE`: 你的 Claude API 密钥
   - `DEEPSEEK_API_KEY_VALUE`: 你的 DeepSeek API 密钥

#### 获取 API 密钥
- **Claude API**: 访问 [Anthropic Console](https://console.anthropic.com/)
- **DeepSeek API**: 访问 [DeepSeek Platform](https://platform.deepseek.com/)

## 开发最佳实践

### Swift 编码规范

#### 命名约定
```swift
// ✅ 类名使用 PascalCase
class TaskFilterManager { }
final class UserTask { }

// ✅ 变量和函数使用 camelCase
var selectedFilter: TaskFilter
func analyzeTask(_ input: String) -> AITaskResponse

// ✅ 常量使用 camelCase
let maxRetryCount = 3
private let defaultTimeout: TimeInterval = 30

// ✅ 枚举使用 PascalCase，case 使用 camelCase
enum TaskFilter {
    case all
    case quadrant1
    case dueToday
}

// ✅ 协议名称通常以 -able 或 -Protocol 结尾
protocol AIServiceProtocol { }
protocol Cacheable { }
```

#### 避免命名冲突
```swift
// ❌ 避免与 Swift 标准库冲突
class Task { }        // 与 Swift.Task 冲突
class Date { }        // 与 Foundation.Date 冲突

// ✅ 使用具体的业务名称
class UserTask { }
class EventDate { }

// ✅ 布尔属性使用 is/has/can 前缀
var isCompleted: Bool
var hasDeadline: Bool
var canEdit: Bool
```

### SwiftUI 最佳实践

#### 视图组织
```swift
// ✅ 保持视图简洁，单一职责
struct TaskRowView: View {
    let task: UserTask
    let onTap: () -> Void
    
    var body: some View {
        HStack {
            TaskStatusIcon(isCompleted: task.isCompleted)
            TaskContent(task: task)
            Spacer()
            TaskPriorityIndicator(quadrant: task.quadrant)
        }
        .onTapGesture(perform: onTap)
    }
}
```

#### 状态管理
```swift
// ✅ 明确状态所有权
struct ParentView: View {
    @State private var selectedTask: UserTask?
    @StateObject private var filterManager = TaskFilterManager.shared
    
    var body: some View {
        TaskListView(
            selectedTask: $selectedTask,
            filterManager: filterManager
        )
    }
}

// ✅ 使用计算属性进行数据转换
private var filteredTasks: [UserTask] {
    filterManager.applyFilters(to: tasks)
}
```

#### 性能优化
```swift
// ✅ 使用懒加载避免过度渲染
LazyVStack {
    ForEach(tasks) { task in
        TaskRowView(task: task)
    }
}

// ✅ 为列表项提供稳定的 ID
ForEach(tasks, id: \.id) { task in
    TaskRowView(task: task)
}

// ✅ 避免在视图中创建对象
private static let dateFormatter = DateFormatter()
```

### SwiftData 最佳实践

#### 模型定义
```swift
// ✅ 正确的 SwiftData 模型
@Model
@preconcurrency  // Swift 6 兼容性
final class UserTask {
    var title: String
    var taskDescription: String  // 避免使用 description
    var isCompleted: Bool
    var dateCreated: Date
    
    init(title: String, description: String) {
        self.title = title
        self.taskDescription = description
        self.isCompleted = false
        self.dateCreated = Date()
    }
}
```

#### 数组属性处理
```swift
// ✅ SwiftData 数组属性的正确处理
@Model
final class AITaskResponse {
    private var suggestionsJSON: String = "[]"
    
    var suggestions: [String] {
        get {
            guard let data = suggestionsJSON.data(using: .utf8),
                  let array = try? JSONDecoder().decode([String].self, from: data) else {
                return []
            }
            return array
        }
        set {
            guard let data = try? JSONEncoder().encode(newValue),
                  let json = String(data: data, encoding: .utf8) else {
                suggestionsJSON = "[]"
                return
            }
            suggestionsJSON = json
        }
    }
}
```

#### 查询优化
```swift
// ✅ 使用谓词进行高效过滤
@Query(filter: #Predicate<UserTask> { task in
    !task.isCompleted && task.quadrant == 1
}) var urgentTasks: [UserTask]

// ✅ 合理的排序
@Query(sort: [
    SortDescriptor(\UserTask.dateCreated, order: .reverse)
]) var recentTasks: [UserTask]
```

## Swift 6 并发编程

### 并发安全模式

#### Actor 隔离
```swift
// ✅ UI 管理器使用 @MainActor
@MainActor
@Observable
final class ToastManager {
    var message: String?
    var isShowing: Bool = false
    
    func show(_ message: String) {
        withAnimation {
            self.message = message
            self.isShowing = true
        }
    }
}

// ✅ SwiftData 模型使用 @preconcurrency
@Model
@preconcurrency
final class UserTask {
    // 模型属性
}
```

#### 线程安全的全局状态
```swift
// ✅ 使用锁保护共享状态
private final class LanguageState {
    static let shared = LanguageState()
    
    private var _currentLanguage: String
    private let lock = NSLock()
    
    var currentLanguage: String {
        lock.lock()
        defer { lock.unlock() }
        return _currentLanguage
    }
    
    func updateLanguage(_ language: String) {
        lock.lock()
        defer { lock.unlock() }
        _currentLanguage = language
    }
}
```

#### 异步操作模式
```swift
// ✅ 正确的异步操作
func scheduleNotification(for task: UserTask) async throws {
    // 后台工作
    let notificationContent = await createNotificationContent(for: task)
    
    // 主线程 UI 更新
    await MainActor.run {
        self.pendingNotifications.append(notificationContent)
    }
}
```

### 并发编程注意事项

#### Context Menu 操作
```swift
// ✅ 正确处理 Context Menu 操作
Button("Complete") {
    Task {
        do {
            // 等待 Context Menu 关闭
            try await Task.sleep(nanoseconds: 300_000_000)
            
            // 主线程更新 UI
            await MainActor.run {
                withAnimation(.spring()) {
                    task.isCompleted = true
                }
                try? modelContext.save()
            }
        } catch {
            print("Error completing task: \(error)")
        }
    }
}
```

## 错误处理

### 错误类型定义
```swift
// ✅ 清晰的错误类型层次
enum TaskError: LocalizedError {
    case invalidInput(String)
    case saveFailed(Error)
    case notFound(UUID)
    
    var errorDescription: String? {
        switch self {
        case .invalidInput(let input):
            return "Invalid input: \(input)"
        case .saveFailed(let error):
            return "Failed to save: \(error.localizedDescription)"
        case .notFound(let id):
            return "Task not found: \(id)"
        }
    }
}

enum AIServiceError: LocalizedError {
    case invalidAPIKey
    case networkError(Error)
    case parsingError(Error)
    
    var errorDescription: String? {
        switch self {
        case .invalidAPIKey:
            return LocalizedString("Invalid API key", comment: "")
        case .networkError(let error):
            return LocalizedString("Network error: \(error.localizedDescription)", comment: "")
        case .parsingError(let error):
            return LocalizedString("Failed to parse response: \(error.localizedDescription)", comment: "")
        }
    }
}
```

### 错误处理策略
```swift
// ✅ 分层错误处理
class TaskService {
    func saveTask(_ task: UserTask) throws {
        // 验证输入
        guard !task.title.isEmpty else {
            throw TaskError.invalidInput("Title cannot be empty")
        }
        
        do {
            try modelContext.save()
        } catch {
            throw TaskError.saveFailed(error)
        }
    }
}

// ✅ UI 层错误处理
struct TaskEditView: View {
    @State private var errorMessage: String?
    @State private var showingError = false
    
    private func saveTask() {
        do {
            try taskService.saveTask(task)
            dismiss()
        } catch {
            errorMessage = error.localizedDescription
            showingError = true
        }
    }
    
    var body: some View {
        // UI 内容
        .alert("Error", isPresented: $showingError) {
            Button("OK") { }
        } message: {
            Text(errorMessage ?? "Unknown error")
        }
    }
}
```

## 测试策略

### 测试架构
- **单元测试**: 测试单个组件和函数
- **集成测试**: 测试组件间的交互
- **UI测试**: 测试完整的用户交互流程
- **性能测试**: 测试关键操作的性能

### 测试环境配置
```swift
class Priority_MatrixTests: XCTestCase {
    var modelContainer: ModelContainer!
    var modelContext: ModelContext!
    
    override func setUp() {
        super.setUp()
        
        // 创建内存中的测试数据库
        let config = ModelConfiguration(isStoredInMemoryOnly: true)
        do {
            modelContainer = try ModelContainer(
                for: UserTask.self, AITaskResponse.self,
                configurations: config
            )
            modelContext = ModelContext(modelContainer)
        } catch {
            fatalError("Failed to create test model container: \(error)")
        }
    }
    
    override func tearDown() {
        modelContainer = nil
        modelContext = nil
        super.tearDown()
    }
}
```

### 单元测试示例
```swift
// UserTask 模型测试
class UserTaskTests: XCTestCase {
    func testQuadrantCalculation() {
        // 测试象限1：重要且紧急
        let q1Task = UserTask(title: "Urgent Important", isImportant: true, isUrgent: true)
        XCTAssertEqual(q1Task.quadrant, 1)
        
        // 测试象限2：重要不紧急
        let q2Task = UserTask(title: "Important Not Urgent", isImportant: true, isUrgent: false)
        XCTAssertEqual(q2Task.quadrant, 2)
        
        // 测试象限3：不重要但紧急
        let q3Task = UserTask(title: "Not Important Urgent", isImportant: false, isUrgent: true)
        XCTAssertEqual(q3Task.quadrant, 3)
        
        // 测试象限4：不重要不紧急
        let q4Task = UserTask(title: "Not Important Not Urgent", isImportant: false, isUrgent: false)
        XCTAssertEqual(q4Task.quadrant, 4)
    }
    
    func testTaskValidation() {
        // 测试有效任务
        let validTask = UserTask(title: "Valid Task")
        XCTAssertTrue(validTask.isValid)
        
        // 测试无效任务（空标题）
        let invalidTask = UserTask(title: "")
        XCTAssertFalse(invalidTask.isValid)
    }
}
```

### 异步测试
```swift
class AIServiceTests: XCTestCase {
    func testTaskAnalysis() async throws {
        // Given
        let service = ClaudeService(configuration: testConfig)
        let input = "Complete project documentation"
        
        // When
        let response = try await service.analyzeTask(input)
        
        // Then
        XCTAssertFalse(response.title.isEmpty)
        XCTAssertNotNil(response.isImportant)
        XCTAssertNotNil(response.isUrgent)
    }
}
```

### Mock 对象
```swift
class MockAIService: AIServiceProtocol {
    var configuration: AIServiceConfiguration
    var shouldFail = false
    var mockResponse: AITaskResponse?
    
    func analyzeTask(_ input: String) async throws -> AITaskResponse {
        if shouldFail {
            throw AIServiceError.networkError(NSError(domain: "MockError", code: -1))
        }
        
        return mockResponse ?? AITaskResponse(
            title: "Mock Analysis of: \(input)",
            analysis: "This is a mock analysis",
            suggestions: ["Mock suggestion 1", "Mock suggestion 2"],
            hiddenValue: "Mock hidden value",
            isImportant: input.lowercased().contains("important"),
            isUrgent: input.lowercased().contains("urgent")
        )
    }
}
```

## 国际化最佳实践

### 字符串本地化
```swift
// ✅ 使用自定义本地化函数
Text(LocalizedString("Tasks", comment: "Tasks tab title"))

// ✅ 提供有意义的注释
LocalizedString("Task completed!", comment: "Success message when user completes a task")

// ✅ 使用分层的键名
LocalizedString("Matrix.Quadrant1.Title", comment: "Title for urgent and important quadrant")
```

### 处理动态内容
```swift
// ✅ 使用格式化字符串
String(format: LocalizedString("Due in %d hours", comment: "Time remaining format"), hours)
```

## 性能优化

### 内存管理
```swift
// ✅ 避免循环引用
class TaskViewController {
    func setupObservers() {
        NotificationCenter.default.addObserver(
            forName: .taskUpdated,
            object: nil,
            queue: .main
        ) { [weak self] notification in
            self?.refreshTasks()
        }
    }
}
```

### 计算优化
```swift
// ✅ 缓存昂贵的计算
class AnalyticsService {
    private var cachedAnalysis: [String: ComprehensiveAnalysis] = [:]
    private let cacheExpiry: TimeInterval = 300 // 5分钟
    
    func getAnalysis(for tasks: [UserTask]) -> ComprehensiveAnalysis {
        let key = generateCacheKey(for: tasks)
        
        if let cached = cachedAnalysis[key],
           Date().timeIntervalSince(cached.generatedAt) < cacheExpiry {
            return cached
        }
        
        let analysis = calculateAnalysis(tasks)
        cachedAnalysis[key] = analysis
        return analysis
    }
}
```

## 安全最佳实践

### API 密钥管理
```swift
// ✅ 使用 Keychain 存储敏感信息
final class KeychainManager {
    func save(key: String, value: String) throws {
        let data = value.data(using: .utf8)!
        
        let query: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrService as String: "com.prioritymatrix.apikeys",
            kSecAttrAccount as String: key,
            kSecValueData as String: data
        ]
        
        SecItemDelete(query as CFDictionary)
        let status = SecItemAdd(query as CFDictionary, nil)
        
        guard status == errSecSuccess else {
            throw KeychainError.saveFailed(status)
        }
    }
}
```

### 网络安全
```swift
// ✅ 验证 HTTPS 连接
func makeRequest(to url: URL) async throws -> Data {
    guard url.scheme == "https" else {
        throw NetworkError.insecureConnection
    }
    
    let (data, response) = try await session.data(from: url)
    
    guard let httpResponse = response as? HTTPURLResponse,
          200...299 ~= httpResponse.statusCode else {
        throw NetworkError.invalidResponse
    }
    
    return data
}
```

## 问题排查

### 常见编译错误

#### Swift 6 并发错误
```swift
// ❌ 错误做法
@Model
@MainActor
final class UserTask {
    // SwiftData 不支持 @MainActor 模型
}

// ✅ 正确做法
@Model
@preconcurrency
final class UserTask {
    // 使用 @preconcurrency 抑制并发警告
}
```

#### SwiftData 数组存储问题
```swift
// ❌ 问题：Could not materialize Objective-C class named "Array"
// SwiftData 不直接支持数组类型

// ✅ 解决方案：使用 JSON 字符串存储
@Model
final class AITaskResponse {
    private var suggestionsJSON: String = "[]"
    
    var suggestions: [String] {
        get {
            guard let data = suggestionsJSON.data(using: .utf8),
                  let array = try? JSONDecoder().decode([String].self, from: data) else {
                return []
            }
            return array
        }
        set {
            // JSON 编码存储
        }
    }
}
```

#### 命名冲突错误
```swift
// ❌ 问题：与 Swift.Task 冲突
class Task {  
    var title: String
}

// ✅ 解决方案：使用明确的业务名称
class UserTask {  
    var title: String
}
```

### 运行时问题

#### Context Menu 崩溃
```swift
// ✅ 正确处理时序问题
Button("Complete") {
    Task {
        do {
            // 等待 Context Menu 关闭
            try await Task.sleep(nanoseconds: 300_000_000)
            
            await MainActor.run {
                withAnimation(.spring()) {
                    task.isCompleted = true
                }
                try? modelContext.save()
            }
        } catch {
            print("Error completing task: \(error)")
        }
    }
}
```

#### AI 服务调用失败
```swift
// 添加详细的错误处理和日志
func analyzeTask(_ input: String) async throws -> AITaskResponse {
    print("🤖 开始AI分析: \(input)")
    
    do {
        let response = try await aiService.analyzeTask(input)
        print("✅ AI分析成功: \(response.title)")
        return response
    } catch AIServiceError.invalidAPIKey {
        print("❌ API密钥无效")
        throw AIServiceError.invalidAPIKey
    } catch AIServiceError.networkError(let error) {
        print("❌ 网络错误: \(error.localizedDescription)")
        throw AIServiceError.networkError(error)
    }
}
```

### 调试工具

#### 日志记录系统
```swift
import os.log

extension Logger {
    private static var subsystem = Bundle.main.bundleIdentifier!
    
    static let ui = Logger(subsystem: subsystem, category: "UI")
    static let data = Logger(subsystem: subsystem, category: "Data")
    static let network = Logger(subsystem: subsystem, category: "Network")
    static let ai = Logger(subsystem: subsystem, category: "AI")
}

// 使用示例
func saveTask(_ task: UserTask) {
    Logger.data.info("保存任务: \(task.title, privacy: .public)")
    
    do {
        try modelContext.save()
        Logger.data.info("✅ 任务保存成功")
    } catch {
        Logger.data.error("❌ 任务保存失败: \(error.localizedDescription, privacy: .public)")
    }
}
```

#### 性能测量
```swift
func measurePerformance<T>(
    name: String,
    operation: () throws -> T
) rethrows -> T {
    let startTime = CFAbsoluteTimeGetCurrent()
    let result = try operation()
    let timeElapsed = CFAbsoluteTimeGetCurrent() - startTime
    
    print("⏱️ \(name) 耗时: \(timeElapsed * 1000) ms")
    return result
}
```

## 开发工作流

### 分支管理
```bash
# 创建功能分支
git checkout -b feature/new-feature-name

# 定期同步主分支
git checkout main
git pull origin main
git checkout feature/new-feature-name
git merge main
```

### 提交规范
```bash
# 使用语义化提交信息
git commit -m "feat: add new task creation flow"
git commit -m "fix: resolve SwiftData concurrency issue"
git commit -m "docs: update API integration guide"
```

### 代码审查清单
- [ ] 所有新代码都有适当的测试
- [ ] 遵循项目编码规范
- [ ] 没有硬编码的敏感信息
- [ ] 错误处理完整且用户友好
- [ ] 国际化字符串已添加
- [ ] 性能影响已考虑
- [ ] Swift 6 并发安全

## 发布前检查

### 功能验证
- [ ] 所有主要功能正常工作
- [ ] AI 分析功能表现正常
- [ ] 通知系统正确性
- [ ] 语言切换功能完整性
- [ ] 数据持久化可靠性

### 性能验证
- [ ] 应用启动时间 < 3秒
- [ ] 界面响应流畅
- [ ] 内存使用合理
- [ ] 电池消耗测试

### 兼容性验证
- [ ] 不同设备尺寸表现
- [ ] 最低 iOS 版本测试
- [ ] 横竖屏适配
- [ ] 暗色模式适配

### 安全验证
- [ ] API 密钥安全存储
- [ ] 网络传输加密
- [ ] 用户数据隐私保护
- [ ] 输入验证和防护

### 国际化验证
- [ ] 所有文本已本地化
- [ ] 不同语言界面布局
- [ ] 日期数字格式地区化