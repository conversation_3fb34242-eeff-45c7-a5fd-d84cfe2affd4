# Priority Matrix 文档中心

欢迎来到 Priority Matrix 的文档中心！这里是您了解和使用 Priority Matrix 的完整指南。

## 项目概述

Priority Matrix 是一个基于艾森豪威尔矩阵（四象限法则）的 iOS 任务管理应用，采用 SwiftUI 构建，集成了 AI 智能分析功能。

### 核心特性
- 📋 **四象限任务管理**：基于重要性和紧急性分类任务
- 🤖 **AI 智能分析**：集成 Claude 3.5 和 DeepSeek 进行任务分析
  - Claude 3.5 集成
  - DeepSeek 集成
- 🔔 **智能通知**：差异化的提醒策略
- 📊 **数据分析**：任务完成趋势和效率分析
- 🌐 **多语言支持**：中英文无缝切换
- 🎨 **现代化界面**：SwiftUI 构建的流畅体验
  - 优雅的动画效果
  - 触觉反馈
  - 深色模式支持
  - 自适应布局

## 文档导航

### 📚 核心文档
全面了解 Priority Matrix 的设计、功能和开发指南

- [🏗️ **ARCHITECTURE.md**](ARCHITECTURE.md) - 完整的系统架构指南
  - 整体架构概述和设计理念
  - 数据层、服务层、管理层、视图层详细说明
  - SwiftData 模型和数据管理
  - AI 服务和分析服务架构
  
- [✨ **FEATURES.md**](FEATURES.md) - 完整的功能指南
  - 🤖 AI 集成功能（Claude & DeepSeek）
  - 📊 数据分析和可视化
  - 🌐 国际化和多语言支持
  - 🔔 智能通知系统
  - 📱 iOS 小组件开发
  
- [📖 **GUIDE.md**](GUIDE.md) - 开发者完整指南
  - 环境配置和构建指南
  - 代码规范和最佳实践
  - 测试指南和质量保证
  - 常见问题和故障排除

### 📚 API 示例
实际的 API 使用示例和代码片段

本目录包含项目中使用的不同 AI 模型提供商的官方 API 示例，基于各提供商的最新官方文档。

- [Claude API 示例](api-examples/claude_example.md) - Anthropic Claude API 官方示例
- [DeepSeek API 示例](api-examples/deepseek_example.md) - DeepSeek API 官方示例

**示例内容包括：**
- API 端点和请求头
- 请求体格式和示例
- 完整的请求示例（curl 和 Python）
- 响应格式和示例
- 安全注意事项和文档链接

**安全提示：**
- 切勿提交真实的 API 密钥
- 在实际实现中使用环境变量
- 保护敏感信息安全

## 快速开始

### 1. 环境要求
- Xcode 15.0+
- iOS 17.0+
- Swift 6.0+
- macOS 14.0+

### 2. 构建项目
```bash
# 克隆项目
git clone [repository-url]

# 构建项目
xcodebuild -project "Priority Matrix.xcodeproj" -scheme "Priority Matrix" -configuration Debug build

# 运行测试
xcodebuild -project "Priority Matrix.xcodeproj" -scheme "Priority Matrix" -configuration Debug test
```

### 3. 配置 API 密钥
参考 [功能指南 - AI 集成](FEATURES.md#🤖-ai-集成功能) 配置 Claude 或 DeepSeek API 密钥。

## 技术栈

### 核心技术
- **SwiftUI** - 现代化的 UI 框架
- **SwiftData** - 数据持久化和管理
- **Swift 6** - 严格并发模式支持（完全兼容）
- **Combine** - 响应式编程
- **UserNotifications** - 智能通知系统

### 集成服务
- **Anthropic Claude** - AI 任务分析
- **DeepSeek** - 备选 AI 服务
- **Keychain Services** - 安全存储

### 开发环境要求
- Xcode 15.0+ (已支持 Xcode 16)
- iOS 17.6+
- macOS 14.0+
- Swift 5.10+ (完全兼容 Swift 6)

### 项目结构
```
Priority Matrix/
├── Models/         # 数据模型
│   ├── UserTask.swift
│   ├── AITaskResponse.swift
│   └── MODELS_GUIDE.md
├── Views/          # 视图层
│   ├── MatrixView.swift
│   ├── TaskListView.swift
│   ├── AnalyticsView.swift
│   └── Components/
├── Services/       # 服务层
│   ├── AIServiceProtocol.swift
│   ├── ClaudeService.swift
│   ├── DeepSeekService.swift
│   └── SERVICES_GUIDE.md
├── Utils/          # 工具类
│   ├── NotificationManager.swift
│   ├── KeychainManager.swift
│   └── HapticManager.swift
├── Extensions/     # 扩展
│   └── Color+Hex.swift
└── docs/          # 项目文档
```

### 版本信息
- 当前版本：0.4.5
- 最低支持：iOS 17.6
- 设备支持：iPhone

### 开发状态

#### 已完成功能
- ✅ 基础任务管理
  - 任务创建、编辑、删除
  - 四象限分类
  - 截止日期管理
- ✅ AI 分析集成
  - Claude 3.5 服务
  - DeepSeek 服务
  - 多语言提示词
- ✅ 通知系统
  - 差异化提醒策略
  - 多级优先级
  - 智能调度
- ✅ 数据分析
  - 任务分布统计
  - 完成率分析
  - 趋势图表
- ✅ UI/UX优化
  - 深色模式
  - 动画效果
  - 触觉反馈
- ✅ 本地化
  - 中文支持
  - 英文支持

#### 技术升级
- ✅ Swift 6 兼容性 (已完成)
  - 完全支持 Swift 6 严格并发模式
  - Actor 隔离优化
  - 线程安全改进
  - SwiftData 并发优化

#### 开发计划
- 🔥 通知系统优化 (最高优先级)
  - 自适应通知时间算法
  - 个性化提醒频率
  - 通知内容智能化
  - 免打扰模式集成
- 📋 Widget 桌面小组件 (已规划，开发中)
  - 任务概览小组件
  - 专注模式计时器
  - 效率分析报告
- 📅 语音输入功能 (技术调研中)
  - 语音识别服务
  - 实时转写
  - UI 实现
- 📅 数据同步 (待启动)
  - iCloud/CloudKit 集成
  - 多设备同步
  - 离线支持
- 🔄 性能优化 (持续进行)
  - 响应速度
  - 内存使用
  - 电池消耗

注：🔥 最高优先级 | 📋 开发中 | 📅 已规划 | 🔄 持续优化

## 贡献指南

我们欢迎任何形式的贡献！请阅读以下指南：

1. **代码贡献**：遵循 [开发者指南](GUIDE.md) 中的最佳实践
2. **问题报告**：使用 GitHub Issues 报告 bug
3. **功能请求**：通过 GitHub Discussions 讨论新功能
4. **文档改进**：帮助完善文档内容

## 支持和社区

- 📧 **联系我们**：[您的邮箱]
- 🐛 **问题反馈**：GitHub Issues
- 💬 **社区讨论**：GitHub Discussions
- 📖 **更新日志**：查看 [CHANGELOG.md](../CHANGELOG.md)

## 许可证

本项目采用 [MIT 许可证](../LICENSE)。

---

**提示**：如果您是第一次接触 Priority Matrix，建议按以下顺序阅读文档：
1. [整体架构指南](ARCHITECTURE.md) - 了解系统设计
2. [开发者指南](GUIDE.md) - 环境配置和快速开始
3. [功能指南](FEATURES.md) - 深入了解各项功能
4. [API 示例](api-examples/) - 实际代码示例