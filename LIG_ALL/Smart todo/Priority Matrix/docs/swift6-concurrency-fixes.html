<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Swift 6 并发问题解决方案详解</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1, h2, h3 {
            color: #2c3e50;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }
        h1 {
            text-align: center;
            color: #e74c3c;
        }
        .error-box {
            background: #ffebee;
            border-left: 4px solid #f44336;
            padding: 15px;
            margin: 15px 0;
            border-radius: 4px;
        }
        .solution-box {
            background: #e8f5e8;
            border-left: 4px solid #4caf50;
            padding: 15px;
            margin: 15px 0;
            border-radius: 4px;
        }
        .warning-box {
            background: #fff3e0;
            border-left: 4px solid #ff9800;
            padding: 15px;
            margin: 15px 0;
            border-radius: 4px;
        }
        pre {
            background: #1a202c;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 8px;
            overflow-x: auto;
            font-size: 14px;
            border: 1px solid #4a5568;
        }
        pre code {
            background: transparent;
            color: inherit;
            padding: 0;
            font-family: 'SF Mono', Monaco, 'Cascadia Code', monospace;
        }
        code {
            background: #f1f5f9;
            color: #334155;
            padding: 2px 6px;
            border-radius: 3px;
            font-family: 'SF Mono', Monaco, 'Cascadia Code', monospace;
            border: 1px solid #e2e8f0;
        }
        .highlight {
            background: #fff3cd;
            padding: 2px 4px;
            border-radius: 3px;
        }
        .table-container {
            overflow-x: auto;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
            font-weight: bold;
        }
        .toc {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .toc ul {
            list-style-type: none;
            padding-left: 0;
        }
        .toc li {
            margin: 8px 0;
        }
        .toc a {
            text-decoration: none;
            color: #3498db;
        }
        .toc a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 Swift 6 并发问题解决方案详解</h1>
        
        <div class="toc">
            <h3>📋 目录</h3>
            <ul>
                <li><a href="#overview">1. 问题概述</a></li>
                <li><a href="#sendable-issues">2. Sendable 协议问题</a></li>
                <li><a href="#actor-isolation">3. Actor 隔离问题</a></li>
                <li><a href="#swiftdata-concurrency">4. SwiftData 并发模式</a></li>
                <li><a href="#solutions">5. 解决方案详解</a></li>
                <li><a href="#best-practices">6. 最佳实践总结</a></li>
            </ul>
        </div>

        <section id="overview">
            <h2>1. 问题概述</h2>
            <p>Swift 6 引入了严格的并发检查机制，旨在消除数据竞争和并发安全问题。在我们的 Priority Matrix 项目中，遇到了以下三个主要问题：</p>
            
            <div class="error-box">
                <h4>🔴 遇到的错误</h4>
                <ol>
                    <li><strong>NotificationManager.swift:1032</strong> - Sendable 闭包中捕获非 Sendable 类型</li>
                    <li><strong>NotificationManager.swift:1042</strong> - UserTask 不符合 Sendable 协议</li>
                    <li><strong>TaskListView.swift:348</strong> - AITaskResponse 无法从非隔离上下文发送</li>
                </ol>
            </div>
        </section>

        <section id="sendable-issues">
            <h2>2. Sendable 协议问题</h2>
            
            <h3>2.1 什么是 Sendable？</h3>
            <p><code>Sendable</code> 是 Swift 6 中的一个标记协议，表示类型可以安全地在并发边界之间传递。</p>
            
            <div class="warning-box">
                <h4>⚠️ Sendable 规则</h4>
                <ul>
                    <li>值类型（struct, enum）通常自动符合 Sendable</li>
                    <li>不可变引用类型（final class 且所有属性为 let）可以符合</li>
                    <li>可变引用类型需要特殊处理才能符合 Sendable</li>
                    <li>SwiftData 的 @Model 类型不能符合 Sendable</li>
                </ul>
            </div>

            <h3>2.2 问题分析</h3>
            
            <h4>错误 1: NotificationManager 闭包捕获</h4>
            <pre><code>// ❌ 问题代码
self.aggregationTimer = Timer.scheduledTimer(withTimeInterval: self.preferences.aggregationTimeWindow, repeats: false) { [weak self] _ in
    Task {
        await self?.processAggregatedNotifications()  // ← 这里出错
    }
}</code></pre>

            <div class="error-box">
                <p><strong>错误原因：</strong>Timer 的闭包是 @Sendable 的，但 NotificationManager 不符合 Sendable 协议，无法安全传递到闭包中。</p>
            </div>

            <h4>错误 2: UserTask 的 Sendable 问题</h4>
            <pre><code>// ❌ 问题代码
private var pendingNotifications: [(task: UserTask, scheduledTime: Date)] = []

let tasksToAggregate = pendingTasks.map { $0.task }  // ← UserTask 不是 Sendable</code></pre>

            <div class="error-box">
                <p><strong>错误原因：</strong>SwiftData 的 @Model 类型（如 UserTask）不符合 Sendable 协议，因为它们需要与数据库上下文绑定。</p>
            </div>
        </section>

        <section id="actor-isolation">
            <h2>3. Actor 隔离问题</h2>
            
            <h3>3.1 什么是 Actor 隔离？</h3>
            <p>Actor 隔离确保只有特定的执行上下文可以访问 actor 的状态，防止数据竞争。</p>

            <h4>错误 3: AITaskResponse 跨隔离边界</h4>
            <pre><code>// ❌ 问题代码
Task { @MainActor in
    let analysis = try await service.analyzeTaskWithRetry(draft.taskInput)  // ← 这里出错
    // ...
}</code></pre>

            <div class="error-box">
                <p><strong>错误原因：</strong><code>analyzeTaskWithRetry</code> 方法在非隔离上下文中执行，但返回的 <code>AITaskResponse</code> 需要在 MainActor 上使用。</p>
            </div>

            <div class="table-container">
                <table>
                    <thead>
                        <tr>
                            <th>隔离类型</th>
                            <th>执行上下文</th>
                            <th>适用场景</th>
                            <th>注意事项</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><code>@MainActor</code></td>
                            <td>主线程</td>
                            <td>UI 操作</td>
                            <td>避免阻塞 UI</td>
                        </tr>
                        <tr>
                            <td><code>nonisolated</code></td>
                            <td>任意线程</td>
                            <td>纯计算</td>
                            <td>不能访问隔离状态</td>
                        </tr>
                        <tr>
                            <td>自定义 Actor</td>
                            <td>串行队列</td>
                            <td>状态管理</td>
                            <td>需要 await 访问</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </section>

        <section id="swiftdata-concurrency">
            <h2>4. SwiftData 并发模式</h2>
            
            <h3>4.1 SwiftData 的并发限制</h3>
            <div class="warning-box">
                <h4>⚠️ SwiftData 关键限制</h4>
                <ul>
                    <li>@Model 对象不符合 Sendable 协议</li>
                    <li>模型对象与 ModelContext 绑定</li>
                    <li>跨线程传递需要使用 PersistentIdentifier</li>
                    <li>建议使用 @preconcurrency 抑制警告</li>
                </ul>
            </div>

            <h3>4.2 推荐的 SwiftData 并发模式</h3>
            <pre><code>// ✅ 正确的模式
@Model
@preconcurrency  // 抑制 Sendable 警告
final class UserTask {
    // 模型定义...
}

// 跨线程传递使用 ID
let taskId: PersistentIdentifier = task.persistentModelID

// 在目标上下文中重新获取
let task = modelContext[taskId, as: UserTask.self]</code></pre>
        </section>

        <section id="solutions">
            <h2>5. 解决方案详解</h2>
            
            <h3>5.1 NotificationManager 隔离解决方案</h3>
            
            <div class="solution-box">
                <h4>✅ 解决方案：添加 @MainActor 隔离</h4>
                <pre><code>// 修改前
final class NotificationManager {
    static let shared = NotificationManager()
    // ...
}

// 修改后  
@MainActor
final class NotificationManager {
    static let shared = NotificationManager()
    // ...
}</code></pre>
            </div>

            <h4>为什么这样解决？</h4>
            <ul>
                <li><strong>统一执行上下文：</strong>所有 NotificationManager 的方法都在 MainActor 上执行</li>
                <li><strong>简化并发模型：</strong>不需要复杂的锁机制</li>
                <li><strong>UI 友好：</strong>通知管理通常与 UI 更新相关</li>
            </ul>

            <h3>5.2 SwiftData 模型传递解决方案</h3>
            
            <div class="solution-box">
                <h4>✅ 解决方案：使用 PersistentIdentifier</h4>
                <pre><code>// 修改前：直接存储 UserTask 对象
private var pendingNotifications: [(task: UserTask, scheduledTime: Date)] = []

// 修改后：存储持久化标识符
private var pendingNotifications: [(taskId: PersistentIdentifier, scheduledTime: Date)] = []

// 使用时的变化
let taskData = (taskId: task.persistentModelID, scheduledTime: Date())</code></pre>
            </div>

            <h4>PersistentIdentifier 的优势</h4>
            <ul>
                <li><strong>Sendable 兼容：</strong>PersistentIdentifier 符合 Sendable 协议</li>
                <li><strong>轻量级：</strong>只存储 ID，不持有整个对象</li>
                <li><strong>跨上下文：</strong>可以在不同的 ModelContext 中使用</li>
                <li><strong>内存效率：</strong>避免对象的强引用循环</li>
            </ul>

            <h3>5.3 AI 服务隔离解决方案</h3>
            
            <div class="solution-box">
                <h4>✅ 解决方案：方法级别的 @MainActor 隔离</h4>
                <pre><code>// 在协议中添加 @MainActor
protocol AIServiceProtocol {
    @MainActor
    func analyzeTask(_ input: String) async throws -> AITaskResponse
}

extension AIServiceProtocol {
    @MainActor
    func analyzeTaskWithRetry(_ input: String) async throws -> AITaskResponse {
        // 实现...
    }
}</code></pre>
            </div>

            <h4>为什么选择方法级隔离？</h4>
            <ul>
                <li><strong>精确控制：</strong>只有需要的方法在 MainActor 上执行</li>
                <li><strong>性能考虑：</strong>网络请求等操作不阻塞主线程</li>
                <li><strong>返回值安全：</strong>确保返回的对象可以安全用于 UI 更新</li>
            </ul>
        </section>

        <section id="best-practices">
            <h2>6. 最佳实践总结</h2>
            
            <h3>6.1 Swift 6 并发设计原则</h3>
            <div class="solution-box">
                <h4>🎯 核心原则</h4>
                <ol>
                    <li><strong>隔离优于同步：</strong>使用 actor 隔离而不是锁</li>
                    <li><strong>最小化共享：</strong>减少跨线程的数据共享</li>
                    <li><strong>类型安全：</strong>利用 Sendable 协议确保安全传递</li>
                    <li><strong>上下文明确：</strong>明确每个操作的执行上下文</li>
                </ol>
            </div>

            <h3>6.2 常见模式和解决方案</h3>
            
            <div class="table-container">
                <table>
                    <thead>
                        <tr>
                            <th>问题类型</th>
                            <th>症状</th>
                            <th>解决方案</th>
                            <th>示例</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>Sendable 闭包捕获</td>
                            <td>闭包中无法访问 self</td>
                            <td>添加 @MainActor 隔离</td>
                            <td><code>@MainActor class Manager</code></td>
                        </tr>
                        <tr>
                            <td>SwiftData 跨线程</td>
                            <td>模型对象无法传递</td>
                            <td>使用 PersistentIdentifier</td>
                            <td><code>task.persistentModelID</code></td>
                        </tr>
                        <tr>
                            <td>返回值隔离错误</td>
                            <td>non-sendable result</td>
                            <td>方法添加 @MainActor</td>
                            <td><code>@MainActor func analyze()</code></td>
                        </tr>
                        <tr>
                            <td>异步调用缺失</td>
                            <td>async but not marked await</td>
                            <td>添加 await 关键字</td>
                            <td><code>await manager.cleanup()</code></td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <h3>6.3 代码检查清单</h3>
            <div class="warning-box">
                <h4>📝 Swift 6 迁移检查清单</h4>
                <ul>
                    <li>☑️ 所有 UI 管理器类添加 <code>@MainActor</code></li>
                    <li>☑️ SwiftData 模型添加 <code>@preconcurrency</code></li>
                    <li>☑️ 跨线程传递使用 ID 而不是对象</li>
                    <li>☑️ 异步方法调用添加 <code>await</code></li>
                    <li>☑️ 闭包捕获检查 Sendable 兼容性</li>
                    <li>☑️ 全局状态使用 actor 保护</li>
                </ul>
            </div>

            <h3>6.4 性能考虑</h3>
            <p>虽然 Swift 6 的并发模型提供了安全性，但也要注意性能影响：</p>
            
            <ul>
                <li><strong>避免过度隔离：</strong>不是所有类都需要 @MainActor</li>
                <li><strong>合理使用 nonisolated：</strong>纯计算方法可以不受隔离限制</li>
                <li><strong>异步边界最小化：</strong>减少不必要的 async/await 调用</li>
                <li><strong>批量操作：</strong>将多个相关操作组合在同一隔离上下文中</li>
            </ul>

            <h3>6.5 调试技巧</h3>
            <div class="solution-box">
                <h4>🔧 常用调试方法</h4>
                <ul>
                    <li><strong>编译器诊断：</strong>使用 <code>-strict-concurrency=complete</code> 标志</li>
                    <li><strong>运行时检查：</strong>启用 Thread Sanitizer</li>
                    <li><strong>静态分析：</strong>使用 Xcode 的并发检查工具</li>
                    <li><strong>日志追踪：</strong>记录执行上下文和线程信息</li>
                </ul>
            </div>
        </section>

        <hr>
        <footer>
            <p><em>本文档基于 Priority Matrix 项目的实际 Swift 6 迁移经验总结。更新日期：2025年6月</em></p>
            <p><strong>相关资源：</strong></p>
            <ul>
                <li><a href="https://developer.apple.com/documentation/swift/concurrency">Swift Concurrency 官方文档</a></li>
                <li><a href="https://developer.apple.com/documentation/swiftdata">SwiftData 官方文档</a></li>
                <li><a href="https://github.com/apple/swift-evolution/blob/main/proposals/0302-concurrent-value-and-concurrent-closures.md">Sendable 协议提案</a></li>
            </ul>
        </footer>
    </div>
</body>
</html>