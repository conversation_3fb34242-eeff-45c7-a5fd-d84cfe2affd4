# PRIORITY MATRIX 功能完整指南

## 概述

Priority Matrix 是一个基于艾森豪威尔矩阵的智能任务管理 iOS 应用，集成了先进的 AI 服务、智能通知、数据分析和国际化支持，为用户提供全面的任务管理解决方案。

## 🚀 开发状态与计划

### 已完成功能 ✅

#### 核心任务管理
- ✅ 任务创建、编辑、删除
- ✅ 四象限分类系统（艾森豪威尔矩阵）
- ✅ 截止日期管理
- ✅ 任务状态跟踪
- ✅ 批量操作支持

#### AI 分析集成
- ✅ **Claude 3.5 服务**：高质量任务分析和建议
- ✅ **DeepSeek 服务**：成本效益的 AI 分析
- ✅ **双语智能检测**：中文/英文自动识别
- ✅ **自动表单填充**：AI 分析结果自动应用

#### 智能通知系统
- ✅ **差异化提醒策略**：基于象限的不同通知频率
- ✅ **多级优先级**：重要紧急任务优先提醒
- ✅ **智能调度**：避免休息时间打扰
- ✅ **通知权限管理**：优雅的权限请求和处理

#### 数据分析功能
- ✅ **象限分布统计**：任务分布可视化
- ✅ **完成率分析**：效率趋势追踪
- ✅ **趋势图表**：时间序列数据展示
- ✅ **效率指标**：生产力评估

#### 用户体验优化
- ✅ **深色模式**：完整的主题支持
- ✅ **动画效果**：流畅的界面交互
- ✅ **触觉反馈**：增强的用户体验
- ✅ **自适应布局**：适配不同屏幕尺寸

#### 国际化支持
- ✅ **中文支持**：完整的简体中文本地化
- ✅ **英文支持**：基础英文界面
- ✅ **实时语言切换**：无需重启应用
- ✅ **AI 多语言支持**：智能语言检测和对应提示词

#### 技术架构
- ✅ **Swift 6 兼容性**：完全支持严格并发模式
- ✅ **SwiftData 集成**：现代化数据持久化
- ✅ **Actor 隔离优化**：线程安全改进
- ✅ **性能优化**：内存和电池使用优化

### 开发中功能 🚧

#### 高优先级开发中
- 🚧 **通知系统优化** (最高优先级)
  - 自适应通知时间算法
  - 个性化提醒频率
  - 通知内容智能化
  - 免打扰模式集成

- 🚧 **iOS 小组件开发** (MVP 阶段)
  - 任务概览小组件（小、中、大尺寸）
  - 实时数据同步
  - Deep Link 支持
  - Widget 交互功能

### 计划中功能 📅

#### 近期计划 (Q1 2024)
- 📅 **专注模式计时器小组件**
  - 番茄钟集成
  - 任务时间跟踪
  - 专注统计分析

- 📅 **数据分析增强**
  - 效率分析报告小组件
  - 自定义分析周期
  - 数据导出功能增强

#### 中期计划 (Q2 2024)
- 📅 **语音输入功能** (技术调研中)
  - 语音识别服务集成
  - 实时语音转文字
  - 语音命令支持
  - 多语言语音识别

- 📅 **高级通知功能**
  - 位置感知通知
  - 智能提醒建议
  - 通知分析报告

#### 长期规划 (Q3-Q4 2024)
- 📅 **数据同步功能** (待启动)
  - iCloud/CloudKit 集成
  - 多设备同步
  - 离线数据支持
  - 同步冲突解决

- 📅 **协作功能**
  - 任务分享
  - 团队协作
  - 评论系统

#### 持续优化 🔄
- 🔄 **性能优化** (持续进行)
  - 应用启动速度
  - 内存使用优化
  - 电池消耗优化
  - 网络请求优化

- 🔄 **用户体验改进**
  - 界面细节优化
  - 交互流程改进
  - 错误处理增强

### 版本信息
- **当前版本**: 0.4.5
- **最低支持**: iOS 17.6
- **设备支持**: iPhone
- **Swift 版本**: Swift 6 (完全兼容)

### 优先级说明
- 🚧 **开发中**: 正在积极开发的功能
- 📅 **已规划**: 已确定开发计划和时间线
- 🔄 **持续优化**: 长期持续改进的方面
- ✅ **已完成**: 已实现并发布的功能

---

## 🤖 AI 集成功能

### 支持的 AI 服务

#### Anthropic Claude
- **模型**: Claude 3.5 Sonnet (默认)、Claude 3.5 Haiku、Claude 3 Haiku
- **特点**: 优秀的中文理解、精准的任务分析、详细的建议输出
- **适用场景**: 复杂任务分析、需要深度理解的场景

#### DeepSeek
- **模型**: DeepSeek-Chat (默认)、DeepSeek-Reasoner
- **特点**: 成本效益高、响应速度快、中文支持良好
- **适用场景**: 大量任务处理、成本敏感的场景

### 核心功能

#### 1. 智能任务分析
AI 服务会分析用户输入的任务描述，提供以下信息：

- **任务标题优化**: 提供更清晰、准确的任务标题
- **重要性判断**: 基于艾森豪威尔矩阵判断任务是否重要
- **紧急性评估**: 评估任务的时间敏感性
- **详细分析**: 对任务的深度解析和背景分析
- **执行建议**: 3-5条具体的执行建议
- **隐藏价值**: 发现任务背后的潜在价值和意义

#### 2. 双语智能检测
系统自动检测用户输入语言（中文/英文），使用对应的提示词模板：

```swift
// 中文输入检测
let isChineseInput = input.range(of: "[\\u4e00-\\u9fff]", options: .regularExpression) != nil

// 对应的提示词模板
if isChineseInput {
    // 使用中文提示词
    return chinesePromptTemplate
} else {
    // 使用英文提示词
    return englishPromptTemplate
}
```

#### 3. 自动表单填充
AI 分析完成后，系统会自动更新任务表单：

- 优化后的任务标题
- 重要性标记（Important/Not Important）
- 紧急性标记（Urgent/Not Urgent）
- AI 分析结果存储

### API 配置指南

#### 获取 API 密钥

**Claude API 密钥**
1. 访问 [Anthropic Console](https://console.anthropic.com/)
2. 注册或登录账户
3. 进入 API Keys 页面
4. 创建新的 API 密钥
5. 复制密钥（以 `sk-ant-` 开头）

**DeepSeek API 密钥**
1. 访问 [DeepSeek Platform](https://platform.deepseek.com/)
2. 注册或登录账户
3. 进入 API Keys 页面
4. 创建新的 API 密钥
5. 复制密钥（以 `sk-` 开头）

#### 配置方式

**方式一：应用内设置（推荐）**
1. 打开 Priority Matrix
2. 进入"设置"页面
3. 在"AI 集成"部分配置 API 密钥
4. 选择首选的 AI 服务提供商

**方式二：环境变量**
```bash
# Claude API 密钥
export CLAUDE_API_KEY_VALUE="your_claude_api_key_here"

# DeepSeek API 密钥  
export DEEPSEEK_API_KEY_VALUE="your_deepseek_api_key_here"
```

**方式三：Xcode 配置（开发用）**
1. 选择项目的 Scheme
2. 编辑 Scheme → Run → Environment Variables
3. 添加环境变量：
   - `CLAUDE_API_KEY_VALUE`
   - `DEEPSEEK_API_KEY_VALUE`

### 错误处理

#### 常见错误类型

**1. API 密钥问题**
```swift
// 错误：无效的 API 密钥
case .invalidAPIKey:
    // 解决方案：
    // 1. 检查密钥格式是否正确
    // 2. 确认密钥是否过期
    // 3. 验证密钥权限设置
```

**2. 网络连接问题**
```swift
// 错误：网络请求失败
case .networkError(let error):
    // 解决方案：
    // 1. 检查网络连接
    // 2. 确认防火墙设置
    // 3. 重试请求
```

**3. 响应解析问题**
```swift
// 错误：JSON 解析失败
case .parsingError(let error):
    // 解决方案：
    // 1. 检查响应格式
    // 2. 处理 Markdown 包装（DeepSeek）
    // 3. 降级处理
```

---

## 📊 数据分析功能

### 核心分析功能

#### 1. 象限分布分析

**QuadrantDistribution 模型**
```swift
struct QuadrantDistribution {
    let quadrant: Int
    let count: Int
    let total: Int
    
    var percentage: Double {
        guard total > 0 else { return 0 }
        return Double(count) / Double(total) * 100
    }
    
    var color: Color {
        switch quadrant {
        case 1: return .red     // 重要且紧急
        case 2: return .orange  // 重要不紧急
        case 3: return .yellow  // 不重要但紧急
        case 4: return .gray    // 不重要不紧急
        default: return .clear
        }
    }
}
```

#### 2. 完成趋势分析

**CompletionTrend 模型**
```swift
struct CompletionTrend {
    let period: AnalysisPeriod
    let dataPoints: [CompletionDataPoint]
    let totalCompleted: Int
    let averagePerDay: Double
    let trend: TrendDirection
    
    enum TrendDirection {
        case increasing
        case decreasing
        case stable
    }
}

enum AnalysisPeriod: String, CaseIterable {
    case week = "week"
    case month = "month"
    case quarter = "quarter"
    
    var days: Int {
        switch self {
        case .week: return 7
        case .month: return 30
        case .quarter: return 90
        }
    }
}
```

#### 3. 效率指标分析

**ProductivityMetrics 模型**
```swift
struct ProductivityMetrics {
    let completionRate: Double          // 完成率
    let averageCompletionTime: TimeInterval  // 平均完成时间
    let quadrantEfficiency: [Int: Double]    // 各象限效率
    let peakProductivityHour: Int           // 高效时段
    let streakDays: Int                     // 连续完成天数
    
    var completionRateDescription: String {
        switch completionRate {
        case 0.8...:
            return LocalizedString("Excellent", comment: "High completion rate")
        case 0.6..<0.8:
            return LocalizedString("Good", comment: "Medium completion rate")
        case 0.4..<0.6:
            return LocalizedString("Fair", comment: "Low completion rate")
        default:
            return LocalizedString("Needs Improvement", comment: "Very low completion rate")
        }
    }
}
```

### 数据可视化

#### 象限分布饼图
- 四象限任务分布的可视化展示
- 颜色编码系统（红色-紧急重要，橙色-重要不紧急等）
- 百分比和数量显示

#### 完成趋势折线图
- 时间序列的任务完成趋势
- 支持周、月、季度视图
- 平均完成率和总量统计

#### 效率热力图
- 用户活动时间的热力分布
- 识别高效工作时段
- 为任务安排提供参考

### 分析服务实现

**TrendAnalysisService - 趋势分析服务**

```swift
final class TrendAnalysisService {
    static let shared = TrendAnalysisService()
    
    func generateComprehensiveAnalysis(tasks: [UserTask]) -> ComprehensiveAnalysis {
        let quadrantDistribution = analyzeQuadrantDistribution(tasks: tasks)
        let completionTrend = analyzeCompletionTrend(tasks: tasks, period: .month)
        let productivityMetrics = calculateProductivityMetrics(tasks: tasks)
        let timeDistribution = analyzeTimeDistribution(tasks: tasks)
        let insights = generateInsights(
            distribution: quadrantDistribution,
            trend: completionTrend,
            metrics: productivityMetrics
        )
        
        return ComprehensiveAnalysis(
            quadrantDistribution: quadrantDistribution,
            completionTrend: completionTrend,
            productivityMetrics: productivityMetrics,
            timeDistribution: timeDistribution,
            insights: insights,
            generatedAt: Date()
        )
    }
}
```

### 数据导出功能

**导出格式支持**
- **CSV**: 适合Excel等表格软件分析
- **JSON**: 适合程序化处理
- **PDF**: 适合报告和存档

**导出内容**
- 任务完成数据
- 象限分布统计
- 时间趋势数据
- 效率指标报告

---

## 🌐 国际化支持

### 当前语言支持

#### 已支持语言
- **英语** (`en`) - 基础本地化
- **简体中文** (`zh_CN`) - 完整翻译

#### 语言文件结构
```
Priority Matrix/
├── Base.lproj/
│   └── Localizable.strings     # 英语 (基础语言)
└── zh_CN.lproj/
    └── Localizable.strings     # 简体中文
```

### 核心架构

#### LanguageManager - 语言管理器

**线程安全的状态类**
```swift
private final class LanguageState {
    static let shared = LanguageState()
    
    private var _currentLanguage: String
    private var _bundle: Bundle
    private let lock = NSLock()
    
    var currentLanguage: String {
        lock.lock()
        defer { lock.unlock() }
        return _currentLanguage
    }
    
    func updateLanguage(_ language: String) {
        lock.lock()
        defer { lock.unlock() }
        
        _currentLanguage = language
        _bundle = Self.createBundle(for: Self.getEffectiveLanguage(from: language))
        UserDefaults.standard.set(language, forKey: "selected_language")
    }
}
```

**主界面管理器**
```swift
@MainActor
@Observable
final class LanguageManager {
    static let shared = LanguageManager()
    
    @Published var currentLanguage: String {
        didSet {
            if currentLanguage != oldValue {
                LanguageState.shared.updateLanguage(currentLanguage)
            }
        }
    }
    
    func setLanguage(_ language: String) {
        currentLanguage = language
    }
}
```

### 自定义本地化函数

**LocalizedString 函数**
```swift
func LocalizedString(_ key: String, comment: String) -> String {
    return NSLocalizedString(key, bundle: LanguageState.shared.bundle, comment: comment)
}
```

**使用示例**
```swift
// SwiftUI 视图中使用
Text(LocalizedString("Tasks", comment: "Tasks tab title"))

// 管理器中使用
let message = LocalizedString("Task completed!", comment: "Task completion message")
ToastManager.shared.show(message)
```

### 语言切换机制

#### 系统语言检测
```swift
private static func getEffectiveLanguage(from selection: String) -> String {
    if selection == "system" {
        let preferredLanguages = Bundle.main.preferredLocalizations
        let systemLanguage = preferredLanguages.first ?? "en"
        
        switch systemLanguage {
        case "zh-Hans", "zh-CN", "zh-Hans-CN":
            return "zh_CN"
        case let lang where lang.hasPrefix("zh"):
            return "zh_CN"
        default:
            return "en"
        }
    }
    
    let supportedLanguages = ["en", "zh_CN"]
    return supportedLanguages.contains(selection) ? selection : "en"
}
```

#### 实时语言切换
```swift
// 在 SettingsView 中
Picker(LocalizedString("Language", comment: "Language setting"), selection: $selectedLanguage) {
    Text(LocalizedString("Follow System", comment: "Follow system language")).tag("system")
    Text("English").tag("en")
    Text("简体中文").tag("zh_CN")
}
.onChange(of: selectedLanguage) { oldValue, newValue in
    languageManager.setLanguage(newValue)
}
```

### 添加新语言

#### 1. 创建本地化文件

**目录结构**
```
Priority Matrix/
├── Base.lproj/
│   └── Localizable.strings
├── zh_CN.lproj/
│   └── Localizable.strings  
├── ja.lproj/                # 新增：日语
│   └── Localizable.strings
├── ko.lproj/                # 新增：韩语  
│   └── Localizable.strings
├── fr.lproj/                # 新增：法语
│   └── Localizable.strings
└── de.lproj/                # 新增：德语
    └── Localizable.strings
```

#### 2. 翻译字符串文件

**翻译示例 (日语)**
```strings
// ja.lproj/Localizable.strings
"Settings" = "設定";
"Tasks" = "タスク";
"Important" = "重要";
"Urgent" = "緊急";
"Priority Matrix" = "優先度マトリックス";
"Eisenhower Matrix" = "アイゼンハワー・マトリックス";
```

#### 3. 更新 LanguageManager

**扩展语言检测逻辑**
```swift
private static func getEffectiveLanguage(from selection: String) -> String {
    if selection == "system" {
        let preferredLanguages = Bundle.main.preferredLocalizations
        let systemLanguage = preferredLanguages.first ?? "en"
        
        switch systemLanguage {
        case "zh-Hans", "zh-CN", "zh-Hans-CN":
            return "zh_CN"
        case "ja", "ja-JP":
            return "ja"     // 日语
        case "ko", "ko-KR":
            return "ko"     // 韩语
        case "fr", "fr-FR":
            return "fr"     // 法语
        case "de", "de-DE":
            return "de"     // 德语
        case "es", "es-ES":
            return "es"     // 西班牙语
        default:
            return "en"
        }
    }
    
    let supportedLanguages = ["en", "zh_CN", "ja", "ko", "fr", "de", "es"]
    return supportedLanguages.contains(selection) ? selection : "en"
}
```

### AI 服务的多语言支持

#### 智能语言检测
```swift
private func detectInputLanguage(_ input: String) -> String {
    // 中文字符检测
    if input.range(of: "[\\u4e00-\\u9fff]", options: .regularExpression) != nil {
        return "zh"
    }
    
    // 日文字符检测 (平假名、片假名、汉字)
    if input.range(of: "[\\u3040-\\u309f\\u30a0-\\u30ff\\u4e00-\\u9fff]", options: .regularExpression) != nil {
        return "ja"
    }
    
    // 韩文字符检测
    if input.range(of: "[\\uac00-\\ud7af]", options: .regularExpression) != nil {
        return "ko"
    }
    
    // 默认英语
    return "en"
}
```

---

## 🔔 智能通知系统

### 通知策略

#### 基于象限的差异化策略

**第一象限（重要且紧急）⚠️**
- **特点**: 最高优先级，需要立即关注
- **通知策略**:
  - 截止前 24 小时：每 4 小时提醒一次
  - 最后 1 小时：额外提醒一次
  - 优先级：高
  - 声音：默认通知音
  - 横幅样式：持久显示

**第二象限（重要不紧急）📌**
- **特点**: 重要但可以规划，需要合理安排
- **通知策略**:
  - 截止前 48 小时：提醒一次
  - 截止前 24 小时：提醒一次
  - 优先级：中等
  - 声音：默认通知音
  - 横幅样式：标准

**第三象限（紧急不重要）⏰**
- **特点**: 紧急但不重要，可能需要委托
- **通知策略**:
  - 截止前 24 小时：提醒一次
  - 优先级：低
  - 声音：无声音
  - 横幅样式：临时显示

**第四象限（不重要不紧急）**
- **特点**: 可以消除或延后的任务
- **通知策略**: 不发送任何通知

### 实现架构

#### NotificationManager - 通知管理器

```swift
final class NotificationManager {
    static let shared = NotificationManager()
    
    // 请求通知权限
    func requestAuthorization() async throws
    
    // 为任务安排通知
    func scheduleNotification(for task: UserTask) async throws
    
    // 取消任务通知
    func cancelNotification(for task: UserTask) async
    
    // 计算通知时间
    private func calculateNotificationTimes(for task: UserTask, dueDate: Date) -> [Date]
    
    // 创建通知内容
    private func createNotificationContent(for task: UserTask, scheduledTime: Date) -> UNMutableNotificationContent
}
```

### 通知时间计算

#### 算法逻辑
```swift
private func calculateNotificationTimes(for task: UserTask, dueDate: Date) -> [Date] {
    let now = Date()
    var times: [Date] = []
    
    switch task.quadrant {
    case 1: // 重要且紧急
        times = [
            dueDate.addingTimeInterval(-24 * 3600), // 24小时前
            dueDate.addingTimeInterval(-4 * 3600),  // 4小时前
            dueDate.addingTimeInterval(-1 * 3600)   // 1小时前
        ]
        
    case 2: // 重要不紧急
        times = [
            dueDate.addingTimeInterval(-48 * 3600), // 48小时前
            dueDate.addingTimeInterval(-24 * 3600)  // 24小时前
        ]
        
    case 3: // 不重要但紧急
        times = [
            dueDate.addingTimeInterval(-24 * 3600)  // 24小时前
        ]
        
    case 4: // 不重要不紧急
        times = [] // 不发送通知
        
    default:
        times = []
    }
    
    // 过滤已过期的时间
    return times.filter { $0 > now }
}
```

### 通知内容定制

#### 多语言支持
```swift
private func createNotificationContent(for task: UserTask, scheduledTime: Date) -> UNMutableNotificationContent {
    let content = UNMutableNotificationContent()
    
    let timeRemaining = task.dueDate!.timeIntervalSince(scheduledTime)
    let hoursRemaining = Int(timeRemaining / 3600)
    
    switch task.quadrant {
    case 1:
        content.title = "⚠️ " + LocalizedString("Important & Urgent", comment: "")
        content.body = String(format: LocalizedString("Task '%@' is due in %d hours", comment: ""), 
                             task.title, hoursRemaining)
        content.sound = .default
        
    case 2:
        content.title = "📌 " + LocalizedString("Important Task", comment: "")
        content.body = String(format: LocalizedString("Task '%@' is due in %d hours", comment: ""), 
                             task.title, hoursRemaining)
        content.sound = .default
        
    case 3:
        content.title = "⏰ " + LocalizedString("Task Reminder", comment: "")
        content.body = String(format: LocalizedString("Task '%@' is due in %d hours", comment: ""), 
                             task.title, hoursRemaining)
        content.sound = nil
        
    default:
        content.title = LocalizedString("Task Reminder", comment: "")
        content.body = task.title
    }
    
    content.categoryIdentifier = "TASK_REMINDER"
    content.userInfo = ["taskId": task.id.uuidString]
    
    return content
}
```

### 通知优化计划 🔔

#### 核心问题与解决方案
当前通知系统存在以下问题：
1. **用户控制不足** → 个性化通知设置 + 即时控制选项
2. **信息密度低** → 高密度内容 + 上下文感知
3. **批量通知处理** → 智能聚合 + 通知压缩
4. **交互操作缺失** → 快速延后 + 多种关闭选项
5. **免打扰智能化** → 自动检测 + 自定义时间段 + 紧急任务覆盖

#### 核心功能详细设计

**1. 用户控制增强**
- 象限级别通知开关（每个象限可单独控制）
- 通知频率选择：积极/正常/温和/最少
- 免打扰时间段设置
- 即时控制：15分钟后、1小时后、今天关闭、本周关闭

**2. 信息密度优化**
- 高密度通知内容：任务标题 + 紧急程度 + 时间信息
- 上下文感知：工作时间、当前任务量、剩余时间
- 智能提示：预估完成时间、建议执行时段
- 相关任务显示：同象限其他紧急任务

**3. 批量处理优化**
- 聚合条件：5分钟内3个以上通知自动聚合
- 压缩策略：按象限和紧急程度分组显示
- 智能摘要：显示总数 + 各象限任务数 + 最紧急任务
- 优先级保持：使用最高优先级通知的设置

**4. 交互操作增强**
- 快速操作：15分钟后、1小时后、标记完成、查看详情
- 关闭选项：今天不再提醒、本周不再提醒
- 聚合通知：查看全部、批量延后
- 反馈提示：操作确认和状态更新

**5. 免打扰智能化**
- 系统免打扰检测：自动遵循系统设置
- 自定义免打扰时间：支持多个时间段设置
- 紧急任务覆盖：象限1任务可突破免打扰
- 智能延迟：自动计算下次合适的通知时间

#### 简化实施要点

**技术实现**
- 扩展NotificationManager增加聚合和增强内容生成
- 新增通知分类和快速操作处理
- 用户偏好设置存储和读取
- 免打扰逻辑集成

**性能优化**
- 异步通知处理，避免阻塞主线程
- 聚合算法时间复杂度O(n log n)
- 通知数据定期清理
- 批量处理减少系统调用

---

## 📱 iOS 小组件

### 小组件总览

| 类型 | 名称 | 主要用途 | 开发阶段 |
|------|------|----------|----------|
| 核心小组件 | Task Overview | 任务概览与统计 | 🚧 MVP |
| 核心小组件 | Focus Widget | 专注模式计时器 | 📅 计划中 |
| 分析小组件 | Analytics Widget | 效率分析与报告 | 📅 计划中 |

### Task Overview Widget - 任务概览小组件

#### 小尺寸 (2x2)
- 显示最紧急的单个任务
- 任务标题和截止时间
- 象限标识和颜色编码

#### 中等尺寸 (4x2)
- 左侧：四象限任务数量概览
- 右侧：紧急任务列表（最多3个）
- 象限分布和任务详情并列显示

#### 大尺寸 (4x4)
- 完整的四象限网格视图
- 每个象限显示任务数量和具体任务
- 今日完成进度统计
- 总任务数和完成率

### 数据模型

#### TaskOverviewEntry
```swift
struct TaskOverviewEntry: TimelineEntry {
    let date: Date
    let configuration: TaskOverviewConfiguration
    let totalTasks: Int
    let quadrantCounts: [Int: Int]
    let tasksByQuadrant: [Int: [QuickTaskInfo]]
    let urgentTasks: [QuickTaskInfo]
    let mostUrgentTask: QuickTaskInfo?
    let todayCompleted: Int
    let todayTotal: Int
    let lastUpdated: Date
}
```

### Widget配置

#### Widget主体
```swift
struct TaskOverviewWidget: Widget {
    let kind: String = "TaskOverviewWidget"
    
    var body: some WidgetConfiguration {
        StaticConfiguration(
            kind: kind,
            provider: TaskOverviewTimelineProvider()
        ) { entry in
            TaskOverviewWidgetView(entry: entry)
                .containerBackground(.fill.tertiary, for: .widget)
        }
        .configurationDisplayName("Task Overview")
        .description("View your task distribution and urgent items at a glance.")
        .supportedFamilies([.systemSmall, .systemMedium, .systemLarge])
    }
}
```

### 数据同步架构

#### App Group 配置
```swift
// 在主应用和 Widget Extension 间共享数据
let appGroupIdentifier = "group.com.prioritymatrix.shared"

extension UserDefaults {
    static let shared = UserDefaults(suiteName: "group.com.prioritymatrix.shared")!
}
```

#### 共享数据模型
```swift
// 简化的任务模型，用于 Widget
struct SharedTask: Codable, Identifiable {
    let id: UUID
    let title: String
    let quadrant: Int
    let dueDate: Date?
    let isCompleted: Bool
    let createdDate: Date
    
    init(from userTask: UserTask) {
        self.id = userTask.id
        self.title = userTask.title
        self.quadrant = userTask.quadrant
        self.dueDate = userTask.dueDate
        self.isCompleted = userTask.isCompleted
        self.createdDate = userTask.dateCreated
    }
}
```

### Widget交互功能

#### Deep Link 支持
```swift
// 在Widget中添加点击动作
struct TaskRowView: View {
    let task: SharedTask
    
    var body: some View {
        HStack {
            Text(task.title)
            Spacer()
            Image(systemName: "chevron.right")
                .foregroundColor(.secondary)
        }
        .widgetURL(URL(string: "prioritymatrix://task/\(task.id)")!)
    }
}
```

---

## 🏗️ 架构概览

### 数据层
- **SwiftData Models**: `UserTask` 是核心模型，使用 SwiftData 持久化
- **AI Integration**: 任务可通过 `AITaskResponse` 增强 AI 分析
- **Task Management**: 草稿模式（`TaskDraft`, `TaskEditDraft`）用于安全编辑

### 服务层  
- **AI Services**: 基于协议的设计，使用 `AIServiceProtocol`
  - `ClaudeService`: Anthropic Claude 3.5 集成
  - `DeepSeekService`: DeepSeek API 集成
- **Analysis Services**: `TrendAnalysisService` 用于数据分析
- **Security**: `KeychainManager` 用于安全的 API 密钥存储

### 管理层
- **TaskFilterManager**: 处理任务过滤逻辑
- **TaskSortManager**: 管理任务排序算法  
- **NotificationManager**: 智能通知调度，使用差异化策略
- **ToastManager**: 用户反馈系统
- **HapticManager**: 触觉反馈协调
- **LanguageManager**: 语言状态管理，支持实时切换

### 视图层
- **Primary Views**: `MatrixView`（四象限）、`TaskListView`、`AnalyticsView`
- **Detail Views**: `TaskDetailView` 用于任务编辑
- **Supporting Views**: `SettingsView`、`CalendarView`、`CompletedTasksView`

---

## 🔑 关键实现模式

### 任务象限系统
任务使用艾森豪威尔矩阵分类：
- 象限 1: 重要且紧急
- 象限 2: 重要且不紧急  
- 象限 3: 不重要但紧急
- 象限 4: 不重要且不紧急

`UserTask` 中的 `quadrant` 计算属性根据 `isImportant` 和 `isUrgent` 标志自动确定位置。

### AI 集成流程
1. 用户输入通过 `AIServiceProtocol` 处理
2. 服务使用复杂的提示词，支持双语（中文/英文检测）
3. 响应解析包含指数退避的重试逻辑
4. 结果通过 `updateFromAIAnalysis()` 更新任务属性

### 通知策略
应用实现智能通知调度：
- 基于任务象限的差异化时间
- 任务完成/删除时自动取消
- 前台和后台通知处理（在 `AppDelegate` 中）

### 数据持久化
- SwiftData 用于本地存储，`UserTask` 为主要模型
- 模型容器在 `Priority_MatrixApp.swift` 中配置
- 任务和 AI 分析之间的自动关系管理

---

## 🛠️ 开发注意事项

### API 密钥配置
API 密钥使用 `KeychainManager` 安全存储，可通过以下方式设置：
- 环境变量：`CLAUDE_API_KEY_VALUE`、`DEEPSEEK_API_KEY_VALUE`
- 应用内设置视图
- Xcode scheme 配置（用于开发）

### 性能考虑
- SwiftData 查询应针对大型任务集合进行优化
- AI 服务调用实现重试逻辑和用户反馈
- 通知调度批量处理以避免系统限制

### 测试结构
- **单元测试**: 服务层测试（`ClaudeServiceTests`、`DeepSeekServiceTests`）
- **集成测试**: 实用工具测试（`KeychainManagerTests`、`ToastManagerTests`）  
- **UI 测试**: 完整用户流程测试

---

## 🚀 开发和构建

### 构建命令

```bash
# 构建项目
xcodebuild -project "Priority Matrix.xcodeproj" -scheme "Priority Matrix" -configuration Debug build

# 运行测试
xcodebuild -project "Priority Matrix.xcodeproj" -scheme "Priority Matrix" -configuration Debug test

# 发布构建
xcodebuild -project "Priority Matrix.xcodeproj" -scheme "Priority Matrix" -configuration Release build

# 运行特定测试目标
xcodebuild -project "Priority Matrix.xcodeproj" -scheme "Priority Matrix" -only-testing "Priority MatrixTests" test
xcodebuild -project "Priority Matrix.xcodeproj" -scheme "Priority Matrix" -only-testing "Priority MatrixUITests" test
```

### Swift 6 兼容性

项目完全兼容 Swift 6 及其严格并发模式：

#### 关键并发模式
- **SwiftData Models**: 使用 `@preconcurrency` 保持与 SwiftData 要求的兼容性
- **UI Managers**: 对所有 UI 相关状态管理使用 `@MainActor`
- **Thread-Safe Globals**: 为全局状态访问实现线程安全模式
- **Actor Isolation**: UI 和数据层之间的适当隔离边界

#### Swift 6 重要注意事项
- SwiftData 模型（`@Model`）由于框架要求无法进行 actor 隔离
- 对 SwiftData 模型使用 `@preconcurrency` 属性以抑制并发警告
- UI 相关管理器（ToastManager、LanguageManager）应使用 `@MainActor` 隔离
- 访问共享状态的全局函数需要线程安全实现

---

## 📈 最佳实践

### 本地化指南
- **始终**使用 `LocalizedString()` 而不是 `NSLocalizedString()` 进行动态语言更新
- 添加新 UI 文本时，确保同时添加到 `Base.lproj/Localizable.strings` 和 `zh_CN.lproj/Localizable.strings`
- 通过在设置中更改语言并验证即时 UI 更新来测试语言切换
- 应用支持无需应用重启的无缝语言切换

### 一般开发规则
- 按要求执行；不多不少
- 除非绝对必要以实现目标，否则不要创建文件
- 总是优先编辑现有文件而不是创建新文件
- 除非用户明确要求，否则不要主动创建文档文件

### 任务管理
- 使用 `TodoWrite` 和 `TodoRead` 工具进行任务规划和跟踪
- 在开始工作前将任务标记为进行中
- 完成后立即将任务标记为已完成
- 维护清晰的任务状态和优先级

### 安全实践
- 始终遵循安全最佳实践
- 永远不要引入暴露或记录机密和密钥的代码
- 永远不要将机密或密钥提交到仓库
- 使用 KeychainManager 进行安全的 API 密钥存储

---

**相关文档**：
- 项目说明：`CLAUDE.md`
- 架构文档：`docs/architecture/`
- 开发指南：`docs/development/`
- API 示例：`docs/api-examples/`