# Priority Matrix 系统架构

## 概述

Priority Matrix 采用清晰的四层架构设计，基于 SwiftUI 和 SwiftData 构建，全面兼容 Swift 6 严格并发模式。系统围绕艾森豪威尔矩阵（四象限任务管理）理念构建，集成 AI 智能分析功能。

## 整体架构图

```
┌─────────────────────────────────────────────────────────────┐
│                      Priority Matrix                        │
├─────────────────────────────────────────────────────────────┤
│  View Layer (SwiftUI)                                       │
│  ┌─────────────┬─────────────┬─────────────┬─────────────┐  │
│  │ MatrixView  │ TaskListView│ AnalyticsV. │ SettingsV.  │  │
│  │             │             │             │             │  │
│  │ 4-Quadrant  │ List & Edit │ Data Charts │ Config      │  │
│  └─────────────┴─────────────┴─────────────┴─────────────┘  │
├─────────────────────────────────────────────────────────────┤
│  Manager Layer                                              │
│  ┌─────────────┬─────────────┬─────────────┬─────────────┐  │
│  │TaskFilter   │ TaskSort    │Notification │ Toast       │  │
│  │Manager      │ Manager     │ Manager     │ Manager     │  │
│  └─────────────┴─────────────┴─────────────┴─────────────┘  │
├─────────────────────────────────────────────────────────────┤
│  Service Layer                                              │
│  ┌─────────────┬─────────────┬─────────────┬─────────────┐  │
│  │ AI Services │ Analysis    │ Keychain    │ Haptic      │  │
│  │ Protocol    │ Service     │ Manager     │ Manager     │  │
│  │ ├─Claude    │             │             │             │  │
│  │ └─DeepSeek  │             │             │             │  │
│  └─────────────┴─────────────┴─────────────┴─────────────┘  │
├─────────────────────────────────────────────────────────────┤
│  Data Layer (SwiftData)                                     │
│  ┌─────────────┬─────────────┬─────────────┬─────────────┐  │
│  │ UserTask    │AITaskResp.  │ TaskDraft   │TaskEditDraft│  │
│  │ @Model      │ @Model      │ @Observable │ @Observable │  │
│  └─────────────┴─────────────┴─────────────┴─────────────┘  │
└─────────────────────────────────────────────────────────────┘
```

## 核心设计理念

### 1. 分层架构
- **视图层 (View Layer)**：SwiftUI 界面展示和交互
- **管理层 (Manager Layer)**：业务逻辑和状态管理
- **服务层 (Service Layer)**：外部服务集成和工具功能
- **数据层 (Data Layer)**：数据模型和持久化

### 2. 艾森豪威尔矩阵核心
- **象限 1**：重要且紧急 (Do First)
- **象限 2**：重要不紧急 (Schedule)
- **象限 3**：不重要但紧急 (Delegate)
- **象限 4**：不重要不紧急 (Eliminate)

### 3. Swift 6 并发安全
- SwiftData 模型使用 `@preconcurrency`
- UI 管理器使用 `@MainActor` 隔离
- 线程安全的全局状态访问
- 结构化并发模式

## 技术栈

### 前端框架
- **SwiftUI**：现代化声明式 UI 框架
- **Combine**：响应式编程和数据绑定
- **Swift 6**：类型安全和并发安全

### 数据管理
- **SwiftData**：苹果官方的数据持久化框架
- **Core Data 兼容**：底层使用 Core Data 引擎
- **iCloud 同步**：自动跨设备数据同步

### 外部集成
- **Anthropic Claude**：主要 AI 分析服务
- **DeepSeek API**：备选 AI 服务提供商
- **UserNotifications**：智能通知系统
- **Keychain Services**：安全的 API 密钥存储

## 数据层架构

### 数据模型结构

```
Data Layer
├── Core Models (@Model)
│   ├── UserTask           # 核心任务模型
│   └── AITaskResponse     # AI 分析结果模型
├── Draft Models (@Observable)
│   ├── TaskDraft          # 新建任务草稿
│   └── TaskEditDraft      # 编辑任务草稿
└── Analysis Models
    └── QuadrantDistribution # 象限分布统计
```

### UserTask - 核心任务模型

```swift
@Model
@preconcurrency
final class UserTask {
    var id: UUID
    var title: String
    var taskDescription: String
    var isCompleted: Bool
    var isImportant: Bool
    var isUrgent: Bool
    var dateCreated: Date
    var dueDate: Date?
    var aiAnalysis: AITaskResponse?
    
    // 计算属性：自动象限分类
    var quadrant: Int {
        switch (isImportant, isUrgent) {
        case (true, true): return 1    // 重要且紧急
        case (true, false): return 2   // 重要不紧急
        case (false, true): return 3   // 不重要但紧急
        case (false, false): return 4  // 不重要不紧急
        }
    }
}
```

### 草稿模式设计

```swift
@Observable
final class TaskDraft {
    var title: String = ""
    var taskDescription: String = ""
    var isImportant: Bool = false
    var isUrgent: Bool = false
    var dueDate: Date = Date()
    var hasDueDate: Bool = false
    var aiResponse: AITaskResponse?
    
    func updateFromAIAnalysis(_ analysis: AITaskResponse) {
        // 安全更新任务属性
    }
}
```

### SwiftData 配置

```swift
// 模型容器设置
let modelContainer = try ModelContainer(for: UserTask.self, AITaskResponse.self)

// 支持 iCloud 同步
let modelConfiguration = ModelConfiguration(
    schema: schema,
    isStoredInMemoryOnly: false,
    cloudKitDatabase: .automatic
)
```

## 服务层架构

### AI 服务系统

```
AI Services
├── AIServiceProtocol     # 统一服务接口
├── ClaudeService         # Anthropic Claude 集成
├── DeepSeekService       # DeepSeek API 集成
└── AIServiceConfiguration # 多服务配置管理
```

#### 协议导向设计

```swift
protocol AIServiceProtocol {
    var configuration: AIServiceConfiguration { get }
    func analyzeTask(_ input: String) async throws -> AITaskResponse
}
```

#### 双语提示词支持

```swift
private func buildPrompt(for input: String) -> String {
    let isChineseInput = input.range(of: "[\\u4e00-\\u9fff]", options: .regularExpression) != nil
    
    if isChineseInput {
        return """
        你是一个专业的任务管理助手。请分析用户输入的任务，并按照艾森豪威尔矩阵进行分类。
        
        用户输入: \(input)
        
        请严格按照以下JSON格式返回分析结果：
        {
            "title": "优化后的任务标题",
            "analysis": "详细分析说明",
            "suggestions": ["建议1", "建议2", "建议3"],
            "hiddenValue": "隐藏价值分析",
            "isImportant": true/false,
            "isUrgent": true/false
        }
        """
    } else {
        // 英文提示词模板
    }
}
```

### 安全服务

#### KeychainManager - 安全存储

```swift
final class KeychainManager {
    static let shared = KeychainManager()
    private let service = "com.prioritymatrix.apikeys"
    
    func save(key: String, value: String) throws {
        // 安全存储 API 密钥
    }
    
    func load(key: String) -> String? {
        // 安全读取 API 密钥
    }
}
```

### 通知服务

#### 差异化通知策略

```swift
// 基于象限的通知时间计算
private func calculateNotificationTimes(for task: UserTask, dueDate: Date) -> [Date] {
    switch task.quadrant {
    case 1: // 重要且紧急
        return [
            dueDate.addingTimeInterval(-24 * 3600), // 24小时前
            dueDate.addingTimeInterval(-4 * 3600),  // 4小时前
            dueDate.addingTimeInterval(-1 * 3600)   // 1小时前
        ].filter { $0 > Date() }
        
    case 2: // 重要不紧急
        return [
            dueDate.addingTimeInterval(-48 * 3600), // 48小时前
            dueDate.addingTimeInterval(-24 * 3600)  // 24小时前
        ].filter { $0 > Date() }
        
    case 3: // 不重要但紧急
        return [
            dueDate.addingTimeInterval(-24 * 3600)  // 24小时前
        ].filter { $0 > Date() }
        
    case 4: // 不重要不紧急
        return [] // 不发送通知
        
    default:
        return []
    }
}
```

## 管理层架构

### 状态管理器

```
Manager Layer
├── State Managers (@MainActor)
│   ├── TaskFilterManager    # 任务过滤管理
│   ├── TaskSortManager      # 任务排序管理
│   ├── ToastManager         # 全局提示管理
│   └── LanguageManager      # 语言切换管理
├── Business Managers
│   ├── NotificationManager  # 通知业务管理
│   └── HapticManager        # 触觉反馈管理
└── Thread-Safe State
    └── LanguageState        # 线程安全语言状态
```

### 任务过滤系统

```swift
@MainActor
@Observable
final class TaskFilterManager {
    static let shared = TaskFilterManager()
    
    var selectedFilter: TaskFilter = .all
    var selectedDueDateFilter: TaskFilter? = nil
    
    func applyFilters(to tasks: [UserTask]) -> [UserTask] {
        // 应用多重过滤逻辑
    }
}

enum TaskFilter: String, CaseIterable {
    case all, allImportant, allUrgent
    case quadrant1, quadrant2, quadrant3, quadrant4
    case completed, dueToday, dueTomorrow, dueThisWeek, overdue
}
```

### 国际化管理

#### 线程安全语言状态

```swift
private final class LanguageState {
    static let shared = LanguageState()
    
    private var _currentLanguage: String
    private var _bundle: Bundle
    private let lock = NSLock()
    
    func updateLanguage(_ language: String) {
        lock.lock()
        defer { lock.unlock() }
        
        _currentLanguage = language
        _bundle = Self.createBundle(for: Self.getEffectiveLanguage(from: language))
        UserDefaults.standard.set(language, forKey: "selected_language")
    }
}

// 全局本地化函数
func LocalizedString(_ key: String, comment: String) -> String {
    return NSLocalizedString(key, bundle: LanguageState.shared.bundle, comment: comment)
}
```

### 全局提示管理

```swift
@MainActor
@Observable
final class ToastManager {
    static let shared = ToastManager()
    
    var message: String?
    var isShowing: Bool = false
    private var hideTask: Task<Void, Never>?
    
    func show(_ message: String, duration: TimeInterval? = nil) {
        withAnimation(.spring()) {
            self.message = message
            self.isShowing = true
        }
        // 自动隐藏逻辑
    }
}
```

## 视图层架构

### 应用结构

```
View Layer (SwiftUI)
├── App Entry
│   ├── Priority_MatrixApp.swift    # 应用入口点
│   └── ContentView.swift           # 主容器视图
├── Primary Views
│   ├── MatrixView.swift            # 四象限主视图
│   ├── TaskListView.swift          # 任务列表视图
│   ├── AnalyticsView.swift         # 数据分析视图
│   └── SettingsView.swift          # 设置页面
├── Detail Views
│   ├── TaskDetailView.swift        # 任务详情编辑
│   ├── AddTaskView.swift           # 新建任务
│   └── CompletedTasksView.swift    # 已完成任务
└── UI Components
    ├── ToastView.swift             # 提示组件
    ├── QuadrantView.swift          # 象限卡片
    ├── TaskCardView.swift          # 任务卡片
    └── FilterBarView.swift         # 过滤器栏
```

### MVVM + Manager 模式

```swift
struct MatrixView: View {
    @Environment(\.modelContext) private var modelContext  // Model
    @Query(filter: #Predicate<UserTask> { !$0.isCompleted }) 
    private var activeTasks: [UserTask]                     // Data Source
    
    @StateObject private var filterManager = TaskFilterManager.shared  // ViewModel
    @State private var selectedTask: UserTask?                          // Local State
    
    private var filteredTasks: [UserTask] {                 // Computed Property
        filterManager.applyFilters(to: activeTasks)
    }
}
```

### 组件化设计

#### QuadrantView - 象限卡片

```swift
struct QuadrantView: View {
    let quadrant: Int
    let tasks: [UserTask]
    @Binding var selectedTask: UserTask?
    
    private var quadrantInfo: (title: String, color: Color, icon: String) {
        switch quadrant {
        case 1: return (LocalizedString("Important & Urgent", comment: ""), .red, "exclamationmark.triangle.fill")
        case 2: return (LocalizedString("Important & Not Urgent", comment: ""), .orange, "star.fill")
        case 3: return (LocalizedString("Not Important & Urgent", comment: ""), .yellow, "clock.fill")
        case 4: return (LocalizedString("Not Important & Not Urgent", comment: ""), .gray, "minus.circle.fill")
        default: return ("", .clear, "")
        }
    }
}
```

#### TaskCardView - 任务卡片

```swift
struct TaskCardView: View {
    let task: UserTask
    let onTap: () -> Void
    
    private var priorityColor: Color {
        switch task.quadrant {
        case 1: return .red
        case 2: return .orange
        case 3: return .yellow
        case 4: return .gray
        default: return .gray
        }
    }
}
```

## 数据流架构

```
User Input → View → Manager → Service → Data
    ↑                                    ↓
    └────────── UI Update ←──────────────┘
```

### 数据流向说明
1. **用户输入**：用户在 SwiftUI 界面进行操作
2. **视图处理**：视图组件接收用户输入
3. **管理器协调**：相应的 Manager 处理业务逻辑
4. **服务调用**：必要时调用外部服务（如 AI 分析）
5. **数据更新**：通过 SwiftData 更新本地数据
6. **界面更新**：通过 SwiftUI 的响应式机制自动更新界面

## 关键设计模式

### 1. 协议导向设计
```swift
protocol AIServiceProtocol {
    func analyzeTask(_ input: String) async throws -> AITaskResponse
}
```

### 2. 响应式数据绑定
- 使用 `@Observable` 和 `@ObservableObject`
- 自动 UI 更新
- 状态变化的实时响应

### 3. 安全的并发模式
- 主线程 UI 更新：`@MainActor`
- 后台数据处理：结构化并发
- 线程安全的状态管理

### 4. 草稿模式
- 使用草稿对象管理临时状态
- 避免直接修改持久化数据
- 支持取消和恢复操作

## 性能优化策略

### 1. 数据管理优化
- SwiftData 懒加载
- 分页查询大量任务
- 索引优化常用查询

### 2. UI 性能优化
- SwiftUI 视图复用
- 懒加载长列表
- 动画性能调优

### 3. 网络请求优化
- AI 服务请求缓存
- 失败重试机制
- 请求合并策略

### 4. 内存管理
- 及时释放大对象
- 避免循环引用
- 后台任务清理

## 安全与隐私

### 1. 数据安全
- API 密钥 Keychain 存储
- 本地数据加密
- 网络传输 HTTPS

### 2. 隐私保护
- 最小权限原则
- 用户数据本地存储
- 透明的数据使用说明

### 3. 合规性
- 符合苹果应用商店政策
- GDPR 数据保护规范
- 用户隐私控制选项

## 错误处理

### 服务层错误

```swift
enum AIServiceError: LocalizedError {
    case invalidAPIKey
    case networkError(Error)
    case httpError(Int)
    case invalidResponse
    case parsingError(Error)
    case rateLimitExceeded
    case serviceUnavailable
}
```

### 数据层错误

```swift
enum DataError: LocalizedError {
    case saveFailed(Error)
    case fetchFailed(Error)
    case deleteFailed(Error)
}
```

### 通知错误

```swift
enum NotificationError: LocalizedError {
    case authorizationDenied
    case schedulingFailed
    case invalidTask
}
```

## 测试策略

### 1. 单元测试
- 数据模型测试
- 业务逻辑测试
- 工具函数测试

### 2. 集成测试
- API 服务集成测试
- 数据库操作测试
- 通知系统测试

### 3. UI 测试
- 用户流程测试
- 界面交互测试
- 可访问性测试

## 未来扩展性

### 1. 功能扩展
- 团队协作功能
- 高级数据分析
- 第三方服务集成

### 2. 平台扩展
- macOS 版本开发
- watchOS 伴侣应用
- Web 端同步查看

### 3. 技术升级
- 新版 Swift 特性采用
- SwiftUI 新组件使用
- 苹果新框架集成

## 最佳实践

### 1. 代码组织
- 保持清晰的分层架构
- 遵循单一职责原则
- 使用协议导向设计

### 2. 状态管理
- 明确状态所有权
- 避免状态冗余
- 使用适当的并发模式

### 3. 性能考虑
- 避免不必要的重建
- 使用懒加载优化
- 合理使用动画效果

### 4. 可维护性
- 编写清晰的文档
- 提供充分的测试覆盖
- 遵循一致的代码风格