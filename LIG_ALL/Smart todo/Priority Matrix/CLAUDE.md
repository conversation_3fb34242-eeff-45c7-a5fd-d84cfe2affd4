# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

Priority Matrix is an iOS app built with SwiftUI that implements the Eisenhower Matrix for task management. The app integrates AI services (Claude 3.5 and DeepSeek) to analyze tasks and provide intelligent categorization suggestions.

## Documentation Structure

The project documentation has been consolidated into comprehensive guides:

- **`docs/FEATURES.md`** - Complete features guide covering AI integration, analytics, internationalization, notifications, and widgets
- **`docs/ARCHITECTURE.md`** - Full system architecture documentation
- **`docs/GUIDE.md`** - Complete developer guide with best practices and development workflows
- **`docs/README.md`** - Documentation hub and project overview
- **`docs/api-examples/`** - API usage examples and code snippets

All previous scattered documentation from `docs/features/`, `docs/development/`, and `docs/architecture/` directories has been integrated into these consolidated documents for better maintainability and navigation.

## Build and Development Commands

This is an Xcode project. Use the following commands:

```bash
# Build the project
xcodebuild -project "Priority Matrix.xcodeproj" -scheme "Priority Matrix" -configuration Debug build

# Run tests
xcodebuild -project "Priority Matrix.xcodeproj" -scheme "Priority Matrix" -configuration Debug test

# Build for release
xcodebuild -project "Priority Matrix.xcodeproj" -scheme "Priority Matrix" -configuration Release build

# Run specific test target
xcodebuild -project "Priority Matrix.xcodeproj" -scheme "Priority Matrix" -only-testing "Priority MatrixTests" test
xcodebuild -project "Priority Matrix.xcodeproj" -scheme "Priority Matrix" -only-testing "Priority MatrixUITests" test
```

The main app target is "Priority Matrix" with test targets "Priority MatrixTests" and "Priority MatrixUITests".

## Swift 6 Compatibility

The project is fully compatible with Swift 6 and its strict concurrency mode:

### Key Concurrency Patterns
- **SwiftData Models**: Use `@preconcurrency` to maintain compatibility with SwiftData requirements
- **UI Managers**: Use `@MainActor` for all UI-related state management
- **Thread-Safe Globals**: Implement thread-safe patterns for global state access
- **Actor Isolation**: Proper isolation boundaries between UI and data layers

### Important Notes for Swift 6
- SwiftData models (`@Model`) cannot be actor-isolated due to framework requirements
- Use `@preconcurrency` attribute for SwiftData models to suppress concurrency warnings
- UI-related managers (ToastManager, LanguageManager) should be `@MainActor` isolated
- Global functions accessing shared state need thread-safe implementations

## Architecture Overview

The app follows a layered SwiftUI architecture with clear separation of concerns:

### Data Layer
- **SwiftData Models**: `UserTask` is the core model with SwiftData persistence
- **AI Integration**: Tasks can be enhanced with AI analysis via `AITaskResponse`
- **Task Management**: Draft patterns (`TaskDraft`, `TaskEditDraft`) for safe editing

### Service Layer  
- **AI Services**: Protocol-based design with `AIServiceProtocol`
  - `ClaudeService`: Anthropic Claude 3.5 integration
  - `DeepSeekService`: DeepSeek API integration
- **Analysis Services**: `TrendAnalysisService` for data analytics
- **Security**: `KeychainManager` for secure API key storage

### Manager Layer
- **TaskFilterManager**: Handles task filtering logic
- **TaskSortManager**: Manages task sorting algorithms  
- **NotificationManager**: Smart notification scheduling with differential strategies
- **ToastManager**: User feedback system
- **HapticManager**: Tactile feedback coordination

### View Layer
- **Primary Views**: `MatrixView` (4-quadrant), `TaskListView`, `AnalyticsView`
- **Detail Views**: `TaskDetailView` for task editing
- **Supporting Views**: `SettingsView`, `CalendarView`, `CompletedTasksView`

## Key Implementation Patterns

### Task Quadrant System
Tasks are categorized using the Eisenhower Matrix:
- Quadrant 1: Important & Urgent
- Quadrant 2: Important & Not Urgent  
- Quadrant 3: Not Important & Urgent
- Quadrant 4: Not Important & Not Urgent

The `quadrant` computed property in `UserTask` automatically determines placement based on `isImportant` and `isUrgent` flags.

### AI Integration Flow
1. User input is processed through `AIServiceProtocol`
2. Services use sophisticated prompts with bilingual support (Chinese/English detection)
3. Response parsing includes retry logic with exponential backoff
4. Results update task properties via `updateFromAIAnalysis()`

### Notification Strategy
The app implements intelligent notification scheduling:
- Differential timing based on task quadrant
- Automatic cancellation on task completion/deletion
- Foreground and background notification handling in `AppDelegate`

### Data Persistence
- SwiftData for local storage with `UserTask` as the primary model
- Model container configured in `Priority_MatrixApp.swift`
- Automatic relationship management between tasks and AI analysis

## Development Notes

### API Key Configuration
API keys are stored securely using `KeychainManager` and can be set via:
- Environment variables: `CLAUDE_API_KEY_VALUE`, `DEEPSEEK_API_KEY_VALUE`
- Settings view within the app
- Xcode scheme configuration (for development)

### Internationalization
The app supports bilingual operation:
- Chinese (`zh_CN.lproj/`)
- English (Base localization)
- AI prompts automatically adapt to input language

### Testing Structure
- **Unit Tests**: Service layer testing (`ClaudeServiceTests`, `DeepSeekServiceTests`)
- **Integration Tests**: Utility testing (`KeychainManagerTests`, `ToastManagerTests`)  
- **UI Tests**: Full user flow testing

### Performance Considerations
- SwiftData queries should be optimized for large task collections
- AI service calls implement retry logic and user feedback
- Notification scheduling is batched to avoid system limits

## Common Development Tasks

### Adding New AI Services
1. Implement `AIServiceProtocol`
2. Add provider enum case in `AIServiceConfiguration`
3. Configure headers and parameters for the new service
4. Add service selection in settings

### Extending Task Properties
1. Update `UserTask` model (consider migration needs)
2. Modify AI prompt templates in `AIServiceProtocol`
3. Update `AITaskResponse` parsing
4. Adjust view layer to display new properties

### Adding New View Controllers
Follow the existing pattern:
1. Create SwiftUI views in appropriate `Views/` subdirectory
2. Use managers for business logic (don't put logic in views)
3. Implement proper state management with `@StateObject` and `@ObservedObject`
4. Add navigation integration in `ContentView`

## Internationalization and Localization

The app uses a custom `LanguageManager` for seamless language switching without requiring app restart.

### Current Language Support
- **English** (`en`) - Base localization
- **Simplified Chinese** (`zh_CN`) - Complete translation

### Language Switching Architecture
- **LanguageManager**: Central language state management with `@MainActor` isolation
- **LanguageState**: Thread-safe global state storage using `NSLock`
- **LocalizedString()**: Custom localization function that can be called from any context
- **Environment Integration**: Language manager injected into SwiftUI environment
- **Real-time Updates**: Views update immediately when language changes

### Adding New Languages

To add support for additional languages (e.g., Japanese, Korean, French, etc.):

#### 1. Create Localization Files
```
Priority Matrix/
├── Base.lproj/
│   └── Localizable.strings
├── zh_CN.lproj/
│   └── Localizable.strings  
├── ja.lproj/                # New: Japanese
│   └── Localizable.strings
├── ko.lproj/                # New: Korean  
│   └── Localizable.strings
└── fr.lproj/                # New: French
    └── Localizable.strings
```

#### 2. Translate All Strings
Copy all key-value pairs from `Base.lproj/Localizable.strings` and translate values:

```strings
// ja.lproj/Localizable.strings (Japanese example)
"Settings" = "設定";
"Tasks" = "タスク";
"Important" = "重要";
"Urgent" = "緊急";
"Priority Matrix" = "優先度マトリックス";
"Eisenhower Matrix" = "アイゼンハワー・マトリックス";
```

#### 3. Update LanguageManager
Modify `Utils/LanguageManager.swift`:

```swift
private func getEffectiveLanguage() -> String {
    if currentLanguage == "system" {
        let preferredLanguages = Bundle.main.preferredLocalizations
        let systemLanguage = preferredLanguages.first ?? "en"
        
        switch systemLanguage {
        case "zh-Hans", "zh-CN", "zh-Hans-CN":
            return "zh_CN"
        case let lang where lang.hasPrefix("zh"):
            return "zh_CN"
        case "ja", "ja-JP":
            return "ja"     // Add Japanese
        case "ko", "ko-KR":
            return "ko"     // Add Korean
        case "fr", "fr-FR":
            return "fr"     // Add French
        case "de", "de-DE":
            return "de"     // Add German
        case "es", "es-ES":
            return "es"     // Add Spanish
        default:
            return "en"
        }
    }
    
    // Validate user-selected language
    let supportedLanguages = ["en", "zh_CN", "ja", "ko", "fr", "de", "es"]
    return supportedLanguages.contains(currentLanguage) ? currentLanguage : "en"
}
```

#### 4. Update Settings UI
Modify `Views/SettingsView.swift` picker options:

```swift
Picker("Language", selection: $selectedLanguage) {
    Text(LocalizedString("Follow System", comment: "Follow system language")).tag("system")
    Text("English").tag("en")
    Text("简体中文").tag("zh_CN")
    Text("日本語").tag("ja")        // Add Japanese
    Text("한국어").tag("ko")         // Add Korean  
    Text("Français").tag("fr")      // Add French
    Text("Deutsch").tag("de")       // Add German
    Text("Español").tag("es")       // Add Spanish
}
```

#### 5. Xcode Project Configuration
1. Open project in Xcode
2. Select project root → Project → Info → Localizations
3. Click "+" to add new language
4. Select target language (ja, ko, fr, etc.)
5. Choose files to localize (Localizable.strings)

### Localization Best Practices

#### String Key Naming
- Use descriptive, hierarchical keys: `"Matrix.Quadrant1.Title"`
- Include context in comments: `comment: "Quadrant 1 section title"`
- Avoid hardcoded strings in code

#### Translation Considerations
- **RTL Languages**: Consider adding Arabic/Hebrew with RTL layout support
- **String Length**: Account for text expansion/contraction across languages
- **Cultural Context**: Adapt examples and references for target culture
- **Pluralization**: Use `.stringsdict` files for complex plural rules

#### Dynamic Content
- **AI Responses**: Consider language-specific AI prompts
- **Date/Time**: Use locale-aware formatters
- **Numbers**: Respect regional number formatting

### Testing Localization
```bash
# Test with different languages in simulator
# Device Settings → General → Language & Region

# Verify all strings are translated
grep -r "NSLocalizedString" . --include="*.swift"

# Check for missing translations
plutil -lint Localizable.strings
```

### Language-Specific Features
- **Chinese**: Support both Simplified and Traditional variants if needed
- **Japanese**: Consider Hiragana/Katakana input methods
- **Arabic/Hebrew**: Implement RTL layout support
- **European Languages**: Handle special characters and diacritics

---

## Important Development Reminders

### Localization Guidelines
- **ALWAYS** use `LocalizedString()` instead of `NSLocalizedString()` for dynamic language updates
- When adding new UI text, ensure it's added to both `Base.lproj/Localizable.strings` and `zh_CN.lproj/Localizable.strings`
- Test language switching by changing language in Settings and verifying immediate UI updates
- The app supports seamless language switching WITHOUT requiring app restart

### General Development Rules
- Do what has been asked; nothing more, nothing less
- NEVER create files unless absolutely necessary for achieving your goal
- ALWAYS prefer editing existing files to creating new ones
- NEVER proactively create documentation files unless explicitly requested

## Documentation Guidelines

When referencing documentation:
- Use `docs/FEATURES.md` for feature-related information and implementation guides
- Use `docs/ARCHITECTURE.md` for system design and architectural patterns
- Use `docs/GUIDE.md` for development workflows, best practices, and troubleshooting
- Use `docs/api-examples/` for code examples and API usage patterns

The documentation structure has been optimized for maintainability with consolidated guides rather than scattered individual files.