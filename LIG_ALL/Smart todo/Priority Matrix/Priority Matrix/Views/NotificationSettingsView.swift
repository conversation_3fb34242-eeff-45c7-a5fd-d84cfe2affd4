import SwiftUI
import UserNotifications

struct NotificationSettingsView: View {
    @Binding var preferences: NotificationPreferences
    @State private var showingAddDNDPeriod = false
    @State private var newDNDPeriod = DoNotDisturbPeriod(
        startTime: DateComponents(hour: 22, minute: 0),
        endTime: DateComponents(hour: 8, minute: 0)
    )
    
    var body: some View {
        NavigationView {
            List {
                // 象限级别开关
                Section(header: Text(LocalizedString("Quadrant Notifications", comment: "Quadrant notification section"))) {
                    ForEach([1, 2, 3, 4], id: \.self) { quadrant in
                        Toggle(isOn: Binding(
                            get: { preferences.quadrantEnabled[quadrant] ?? false },
                            set: { preferences.quadrantEnabled[quadrant] = $0 }
                        )) {
                            HStack {
                                Text(LocalizedString("Quadrant \(quadrant)", comment: "Quadrant label"))
                                Spacer()
                                Text(quadrantDescription(quadrant))
                                    .font(.caption)
                                    .foregroundColor(.gray)
                            }
                        }
                    }
                }
                
                // 通知频率设置
                Section(header: Text(LocalizedString("Notification Frequency", comment: "Frequency section"))) {
                    Picker(LocalizedString("Frequency", comment: "Frequency picker"), selection: $preferences.notificationFrequency) {
                        ForEach(NotificationFrequency.allCases, id: \.self) { frequency in
                            Text(frequency.localizedName).tag(frequency)
                        }
                    }
                    .pickerStyle(SegmentedPickerStyle())
                    
                    Text(frequencyDescription(preferences.notificationFrequency))
                        .font(.caption)
                        .foregroundColor(.gray)
                }
                
                // 免打扰时间段
                Section(header: Text(LocalizedString("Do Not Disturb Periods", comment: "DND section"))) {
                    ForEach(preferences.doNotDisturbPeriods.indices, id: \.self) { index in
                        HStack {
                            VStack(alignment: .leading) {
                                Text(formatDNDPeriod(preferences.doNotDisturbPeriods[index]))
                                    .font(.body)
                                if !preferences.doNotDisturbPeriods[index].isEnabled {
                                    Text(LocalizedString("Disabled", comment: "DND period disabled"))
                                        .font(.caption)
                                        .foregroundColor(.gray)
                                }
                            }
                            
                            Spacer()
                            
                            Toggle("", isOn: Binding(
                                get: { preferences.doNotDisturbPeriods[index].isEnabled },
                                set: { preferences.doNotDisturbPeriods[index].isEnabled = $0 }
                            ))
                        }
                        .swipeActions {
                            Button(role: .destructive) {
                                preferences.doNotDisturbPeriods.remove(at: index)
                            } label: {
                                Image(systemName: "trash")
                            }
                        }
                    }
                    
                    Button {
                        showingAddDNDPeriod = true
                    } label: {
                        Label(LocalizedString("Add DND Period", comment: "Add DND period button"), systemImage: "plus")
                    }
                }
                
                // 高级设置
                Section(header: Text(LocalizedString("Advanced Settings", comment: "Advanced section"))) {
                    Toggle(LocalizedString("Override DND for Urgent Tasks", comment: "Override DND toggle"), 
                           isOn: $preferences.urgentTasksOverrideDND)
                    
                    Toggle(LocalizedString("Enable Notification Aggregation", comment: "Aggregation toggle"), 
                           isOn: $preferences.enableNotificationAggregation)
                    
                    if preferences.enableNotificationAggregation {
                        VStack(alignment: .leading, spacing: 8) {
                            HStack {
                                Text(LocalizedString("Aggregation Threshold", comment: "Aggregation threshold"))
                                Spacer()
                                Text("\(preferences.aggregationThreshold)")
                                    .foregroundColor(.gray)
                            }
                            
                            Slider(
                                value: Binding(
                                    get: { Double(preferences.aggregationThreshold) },
                                    set: { preferences.aggregationThreshold = Int($0) }
                                ),
                                in: 2...10,
                                step: 1
                            )
                            
                            Text(LocalizedString("Notifications will be grouped when \(preferences.aggregationThreshold) or more arrive within 5 minutes", comment: "Aggregation description"))
                                .font(.caption)
                                .foregroundColor(.gray)
                        }
                    }
                }
                
                // 当前延后的任务
                if !preferences.snoozeRecords.isEmpty {
                    Section(header: Text(LocalizedString("Snoozed Tasks", comment: "Snoozed tasks section"))) {
                        ForEach(Array(preferences.snoozeRecords.keys), id: \.self) { taskId in
                            if let snoozeUntil = preferences.snoozeRecords[taskId],
                               snoozeUntil > Date() {
                                HStack {
                                    VStack(alignment: .leading) {
                                        Text(String(taskId.prefix(8)) + "...")
                                            .font(.caption)
                                            .foregroundColor(.gray)
                                        Text(LocalizedString("Snoozed until", comment: "Snoozed until label"))
                                            .font(.body)
                                    }
                                    
                                    Spacer()
                                    
                                    Text(formatDate(snoozeUntil))
                                        .font(.caption)
                                        .foregroundColor(.blue)
                                    
                                    Button {
                                        preferences.snoozeRecords.removeValue(forKey: taskId)
                                    } label: {
                                        Image(systemName: "xmark.circle")
                                            .foregroundColor(.red)
                                    }
                                }
                            }
                        }
                    }
                }
            }
            .navigationTitle(LocalizedString("Notification Settings", comment: "Notification settings title"))
            .navigationBarTitleDisplayMode(.inline)
            .sheet(isPresented: $showingAddDNDPeriod) {
                AddDNDPeriodView(period: $newDNDPeriod) { period in
                    preferences.doNotDisturbPeriods.append(period)
                }
            }
        }
    }
    
    private func quadrantDescription(_ quadrant: Int) -> String {
        switch quadrant {
        case 1: return LocalizedString("Important & Urgent", comment: "Q1 description")
        case 2: return LocalizedString("Important & Not Urgent", comment: "Q2 description")  
        case 3: return LocalizedString("Not Important & Urgent", comment: "Q3 description")
        case 4: return LocalizedString("Not Important & Not Urgent", comment: "Q4 description")
        default: return ""
        }
    }
    
    private func frequencyDescription(_ frequency: NotificationFrequency) -> String {
        switch frequency {
        case .aggressive:
            return LocalizedString("More frequent notifications for all tasks", comment: "Aggressive frequency description")
        case .normal:
            return LocalizedString("Standard notification timing", comment: "Normal frequency description")
        case .gentle:
            return LocalizedString("Reduced frequency, less interruption", comment: "Gentle frequency description")
        case .minimal:
            return LocalizedString("Minimal notifications, only most important", comment: "Minimal frequency description")
        }
    }
    
    private func formatDNDPeriod(_ period: DoNotDisturbPeriod) -> String {
        let formatter = DateFormatter()
        formatter.timeStyle = .short
        
        let startTime = Calendar.current.date(from: period.startTime) ?? Date()
        let endTime = Calendar.current.date(from: period.endTime) ?? Date()
        
        return "\(formatter.string(from: startTime)) - \(formatter.string(from: endTime))"
    }
    
    private func formatDate(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateStyle = .short
        formatter.timeStyle = .short
        return formatter.string(from: date)
    }
}

struct AddDNDPeriodView: View {
    @Binding var period: DoNotDisturbPeriod
    let onSave: (DoNotDisturbPeriod) -> Void
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                Text(LocalizedString("Add Do Not Disturb Period", comment: "Add DND period title"))
                    .font(.title2)
                    .fontWeight(.semibold)
                
                VStack(alignment: .leading, spacing: 16) {
                    HStack {
                        Text(LocalizedString("Start Time", comment: "Start time label"))
                        Spacer()
                        DatePicker("", selection: Binding(
                            get: { Calendar.current.date(from: period.startTime) ?? Date() },
                            set: { period.startTime = Calendar.current.dateComponents([.hour, .minute], from: $0) }
                        ), displayedComponents: .hourAndMinute)
                        .labelsHidden()
                    }
                    
                    HStack {
                        Text(LocalizedString("End Time", comment: "End time label"))
                        Spacer()
                        DatePicker("", selection: Binding(
                            get: { Calendar.current.date(from: period.endTime) ?? Date() },
                            set: { period.endTime = Calendar.current.dateComponents([.hour, .minute], from: $0) }
                        ), displayedComponents: .hourAndMinute)
                        .labelsHidden()
                    }
                    
                    Toggle(LocalizedString("Enabled", comment: "Enabled toggle"), isOn: $period.isEnabled)
                }
                .padding()
                .background(Color.gray.opacity(0.1))
                .cornerRadius(12)
                
                Spacer()
            }
            .padding()
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button(LocalizedString("Cancel", comment: "Cancel button")) {
                        dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button(LocalizedString("Save", comment: "Save button")) {
                        onSave(period)
                        dismiss()
                    }
                }
            }
        }
    }
}

#Preview {
    @Previewable @State var preferences = NotificationPreferences()
    return NotificationSettingsView(preferences: $preferences)
}