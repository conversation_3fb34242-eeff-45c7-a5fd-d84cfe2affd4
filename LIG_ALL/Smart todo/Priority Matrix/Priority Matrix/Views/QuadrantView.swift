import SwiftUI
import SwiftData

struct QuadrantView: View {
    let title: String
    let tasks: [UserTask]
    let taskColor: Color
    @Binding var hoveredTaskId: PersistentIdentifier?
    let isExpanded: Bool
    let onTitleTap: () -> Void
    let height: CGFloat
    let quadrant: Int
    let filterManager: TaskFilterManager
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            quadrantHeader
            
            Divider()
            
            tasksList
        }
        .padding(12)
        .frame(maxWidth: .infinity, minHeight: height, maxHeight: height)
        .background(taskColor.opacity(0.25))
        .cornerRadius(12)
    }
    
    private var quadrantHeader: some View {
        HStack(spacing: 8) {
            Text(title)
                .font(.callout)
                .fontWeight(.semibold)
                .lineLimit(2)
                .fixedSize(horizontal: false, vertical: true)
                .frame(height: 40)
                .multilineTextAlignment(.leading)
            
            Spacer()
            
            // 状态图标组
            VStack(spacing: 4) {
                // Important 图标
                Label("", systemImage: "star.fill")
                    .labelStyle(.iconOnly)
                    .foregroundColor(quadrant == 1 || quadrant == 2 ? .red : Color(.systemGray4))
                    .font(.system(size: 14))
                
                // Urgent 图标
                Label("", systemImage: "timer")
                    .labelStyle(.iconOnly)
                    .foregroundColor(quadrant == 1 || quadrant == 3 ? .orange : Color(.systemGray4))
                    .font(.system(size: 14))
            }
            .frame(width: 20)
        }
        .frame(maxWidth: .infinity, alignment: .leading)
        .padding(.bottom, 4)
        .contentShape(Rectangle())
        .onTapGesture {
            switch quadrant {
            case 1:
                filterManager.selectedFilter = filterManager.selectedFilter == .quadrant1 ? .all : .quadrant1
            case 2:
                filterManager.selectedFilter = filterManager.selectedFilter == .quadrant2 ? .all : .quadrant2
            case 3:
                filterManager.selectedFilter = filterManager.selectedFilter == .quadrant3 ? .all : .quadrant3
            case 4:
                filterManager.selectedFilter = filterManager.selectedFilter == .quadrant4 ? .all : .quadrant4
            default:
                break
            }
        }
    }
    
    private var tasksList: some View {
        ScrollView(showsIndicators: true) {
            VStack(alignment: .leading, spacing: 8) {
                ForEach(tasks) { task in
                    taskCard(for: task)
                }
            }
        }
    }
    
    private func taskCard(for task: UserTask) -> some View {
        TaskCardView(
            task: task,
            color: Color(.secondarySystemBackground).opacity(0.9),
            isHovered: task.persistentModelID == hoveredTaskId
        )
        .onHover { isHovered in
            hoveredTaskId = isHovered ? task.persistentModelID : nil
        }
    }
} 
