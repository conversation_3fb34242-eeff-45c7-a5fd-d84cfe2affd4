import SwiftUI

struct ToastModifier: ViewModifier {
    @ObservedObject var toastManager = ToastManager.shared
    
    func body(content: Content) -> some View {
        ZStack {
            content
            
            if toastManager.isShowing, let message = toastManager.message {
                VStack {
                    toastView(message: message)
                        .padding(.top, 60)
                    Spacer()
                }
            }
        }
    }
    
    private func toastView(message: String) -> some View {
        HStack(spacing: 12) {
            Image(systemName: "exclamationmark.circle.fill")
                .font(.system(size: 20))
                .foregroundColor(.white)
            
            Text(message)
                .foregroundColor(.white)
                .font(.system(size: 15))
                .lineLimit(3)
                .multilineTextAlignment(.leading)
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 12)
        .background(Color(red: 251/255, green: 140/255, blue: 0/255)) // Orange 600 (#FB8C00)
        .cornerRadius(10)
        .padding(.horizontal, 20)
        .transition(.opacity)
        .animation(.easeInOut, value: toastManager.isShowing)
        .shadow(color: .black.opacity(0.15), radius: 4, x: 0, y: 2)
    }
}

extension View {
    func toast() -> some View {
        modifier(ToastModifier())
    }
}

#Preview {
    struct TestView: View {
        let toastManager = ToastManager.shared
        
        var body: some View {
            VStack {
                Button("Show Normal Message") {
                    toastManager.show("系统检测到网络异常，正在重试，请稍后查看")
                }
                .padding()
                
                Button("Show Error Message") {
                    toastManager.show("系统遇到解析问题，已自动修复，给您带来不便敬请谅解", duration: 4.0)
                }
                .padding()
                
                Button("Show Long Message") {
                    toastManager.show("系统正在处理您的请求，这可能需要一些时间。我们会在处理完成后通知您，您可以继续其他操作。")
                }
                .padding()
            }
            .toast()
            .preferredColorScheme(.light)
        }
    }
    
    return TestView()
} 