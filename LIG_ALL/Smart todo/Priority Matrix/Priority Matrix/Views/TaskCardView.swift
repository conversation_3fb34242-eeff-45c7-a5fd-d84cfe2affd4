import SwiftUI
import SwiftData

struct TaskCardView: View {
    @Environment(\.modelContext) private var modelContext
    @Environment(\.dismiss) private var dismiss
    let task: UserTask
    let color: Color
    let isHovered: Bool
    @State private var showingDeleteConfirmation = false
    @State private var showingError = false
    
    private let dateFormatter: DateFormatter = {
        let formatter = DateFormatter()
        formatter.dateFormat = "M.d HH:mm"
        return formatter
    }()
    
    var body: some View {
        NavigationLink(destination: TaskDetailView(task: task)) { 
            Text(task.title)
                .padding(8)
                .frame(maxWidth: .infinity, alignment: .leading)
                .background(color)
                .cornerRadius(8)
                .shadow(color: Color.black.opacity(0.1), radius: 2, x: 0, y: 1)
                .scaleEffect(isHovered ? 1.02 : 1.0)
                .animation(.easeOut(duration: 0.3), value: isHovered)
        }
        .buttonStyle(PlainButtonStyle())
        .contextMenu {
            Label {
                Text("Due Date: " + (task.dueDate.map { dateFormatter.string(from: $0) } ?? "not set"))
            } icon: {
                Image(systemName: "calendar")
            }
            
            Divider()
            
            Button {
                task.isCompleted = true
                try? modelContext.save()
            } label: {
                Label(LocalizedString("Complete", comment: "Complete menu item"), 
                      systemImage: "checkmark.circle")
            }
            
            Button(role: .destructive) {
                showingDeleteConfirmation = true
            } label: {
                Label(LocalizedString("Delete", comment: "Delete menu item"), 
                      systemImage: "trash")
            }
        }
        .confirmationDialog(
            LocalizedString("Delete Task", comment: "Delete confirmation title"),
            isPresented: $showingDeleteConfirmation,
            titleVisibility: .visible
        ) {
            Button(role: .destructive) {
                modelContext.delete(task)
                try? modelContext.save()
                dismiss()
            } label: {
                Text(LocalizedString("Delete", comment: "Delete confirmation button"))
            }
            
            Button(role: .cancel) { } label: {
                Text(LocalizedString("Cancel", comment: "Cancel button"))
            }
        } message: {
            Text(LocalizedString("Are you sure you want to delete this task?", comment: "Delete confirmation message"))
        }
        .alert(LocalizedString("Error", comment: "Error alert title"), 
               isPresented: $showingError) {
            Button("OK", role: .cancel) { }
        } message: {
            Text(LocalizedString("Failed to complete task. Please try again.", comment: "Task completion error message"))
        }
    }
} 
