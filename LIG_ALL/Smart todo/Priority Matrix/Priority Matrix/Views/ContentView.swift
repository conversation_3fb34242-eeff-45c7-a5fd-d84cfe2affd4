import SwiftUI
import SwiftData

struct ContentView: View {
    @Environment(\.modelContext) private var modelContext
    @EnvironmentObject private var languageManager: LanguageManager
    @Query private var tasks: [UserTask]
    @State private var selectedTab = 0
    @State private var showingAddTask = false
    @State private var filterManager = TaskFilterManager()
    @State private var showTabBar = true
    
    func setTabBarVisibility(_ visible: Bool) {
        withAnimation {
            showTabBar = visible
        }
    }
    
    var body: some View {
        GeometryReader { geometry in
            ZStack(alignment: .bottom) {
                TabView(selection: $selectedTab) {
                    MatrixView()
                        .environment(filterManager)
                        .environment(\.setTabBarVisibility, setTabBarVisibility)
                        .tag(0)
                    
                    TaskListView()
                        .environment(filterManager)
                        .environment(\.setTabBarVisibility, setTabBarVisibility)
                        .tag(1)
                    
                    AnalyticsView()
                        .environment(\.setTabBarVisibility, setTabBarVisibility)
                        .tag(2)
                    
                    SettingsView()
                        .environment(\.setTabBarVisibility, setTabBarVisibility)
                        .tag(3)
                }
                
                if showTabBar {
                    VStack(spacing: 0) {
                        Spacer()
                        
                        // Add Button
                        <PERSON>ton {
                            showingAddTask = true
                        } label: {
                            Circle()
                                .fill(Color.blue)
                                .frame(width: 64, height: 64)
                                .overlay(
                                    Image(systemName: "plus")
                                        .font(.system(size: 24, weight: .medium))
                                        .foregroundColor(.white)
                                )
                                .shadow(color: .black.opacity(0.1), radius: 4, x: 0, y: 2)
                        }
                        .offset(y: geometry.safeAreaInsets.bottom > 0 ? 60 : 45)
                        .zIndex(1)
                        
                        // Custom Tab Bar
                        HStack {
                            ForEach(0..<4) { index in
                                if index == 2 {
                                    Spacer()
                                }
                                Button {
                                    withAnimation {
                                        selectedTab = index
                                    }
                                } label: {
                                    VStack(spacing: 4) {
                                        Image(systemName: tabIcon(for: index))
                                            .font(.system(size: 24))
                                        Text(tabTitle(for: index))
                                            .font(.system(size: 10))
                                    }
                                    .foregroundColor(selectedTab == index ? .blue : .gray)
                                }
                                if index == 1 {
                                    Spacer()
                                }
                                if index < 3 {
                                    Spacer()
                                }
                            }
                        }
                        .frame(height: 49)
                        .padding(.horizontal, 16)
                        .padding(.top, 10)
                        .padding(.bottom, geometry.safeAreaInsets.bottom > 0 ? 0 : 8)
                        .background(Color(UIColor.systemBackground))
                        .overlay(
                            Rectangle()
                                .frame(height: 1)
                                .foregroundColor(Color(UIColor.separator))
                                .opacity(0.3),
                            alignment: .top
                        )
                    }
                }
            }
        }
        .sheet(isPresented: $showingAddTask) {
            AddTaskSheet()
        }
        .environment(\.showTabBar, showTabBar)
        .toast()
        .updateOnLanguageChange()
    }
    
    private func tabIcon(for index: Int) -> String {
        switch index {
        case 0: return "square.grid.2x2"
        case 1: return "list.bullet"
        case 2: return "chart.pie.fill"
        case 3: return "gear"
        default: return ""
        }
    }
    
    private func tabTitle(for index: Int) -> String {
        switch index {
        case 0: return LocalizedString("Matrix", comment: "Tab item title")
        case 1: return LocalizedString("List", comment: "Tab item title")
        case 2: return LocalizedString("Analytics", comment: "Tab item title")
        case 3: return LocalizedString("Settings", comment: "Tab item title")
        default: return ""
        }
    }
}

#Preview {
    do {
        let config = ModelConfiguration(isStoredInMemoryOnly: true)
        let container = try ModelContainer(for: UserTask.self, configurations: config)
        return ContentView()
            .modelContainer(container)
    } catch {
        return Text("Failed to create preview: \(error.localizedDescription)")
    }
} 
