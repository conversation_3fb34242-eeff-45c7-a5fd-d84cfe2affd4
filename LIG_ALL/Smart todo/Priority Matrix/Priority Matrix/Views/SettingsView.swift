import SwiftUI
import SwiftData

struct SettingsView: View {
    @Environment(\.modelContext) private var modelContext
    @EnvironmentObject private var languageManager: LanguageManager
    @Query private var tasks: [UserTask]
    @State private var showingDeleteConfirmation = false
    @State private var selectedLanguage: String = "system"
    @State private var notificationPreferences: NotificationPreferences = NotificationManager.shared.getPreferences()
    @State private var showingNotificationSettings = false
    @State private var systemStatus: NotificationSystemStatus?
    @State private var isLoadingStatus = false
    
    // 获取系统语言
    private var systemLanguage: String {
        let preferredLanguages = Bundle.main.preferredLocalizations
        let systemLang = preferredLanguages.first ?? "en"
        
        // 返回实际会使用的语言标识
        switch systemLang {
        case "zh-Hans", "zh-CN", "zh-Hans-CN":
            return "zh_CN"
        case let lang where lang.hasPrefix("zh"):
            return "zh_CN"
        default:
            return "en"
        }
    }
    
    // 当前实际使用的语言
    private var currentLanguage: String {
        if languageManager.currentLanguage == "system" {
            return systemLanguage
        }
        return languageManager.currentLanguage
    }
    
    var body: some View {
        NavigationView {
            List {
                Section(header: Text(LocalizedString("Language", comment: "Language section header"))) {
                    Picker("Language", selection: $selectedLanguage) {
                        Text(LocalizedString("Follow System", comment: "Follow system language")).tag("system")
                        Text("English").tag("en")
                        Text("简体中文").tag("zh_CN")
                    }
                    .onChange(of: selectedLanguage) { oldValue, newValue in
                        if oldValue != newValue {
                            languageManager.setLanguage(newValue)
                        }
                    }
                    
                    if selectedLanguage == "system" {
                        Text(LocalizedString("Current System Language", comment: "Current system language label") + ": " + (systemLanguage == "zh_CN" ? "简体中文" : "English"))
                            .foregroundColor(.gray)
                    }
                }
                
                Section(header: Text(LocalizedString("Notifications", comment: "Notifications section header"))) {
                    NavigationLink {
                        NotificationSettingsView(preferences: $notificationPreferences)
                    } label: {
                        Label(LocalizedString("Notification Preferences", comment: "Notification preferences button"), systemImage: "bell")
                    }
                    
                    HStack {
                        Label(LocalizedString("Notification Frequency", comment: "Current frequency label"), systemImage: "bell.badge")
                        Spacer()
                        Text(notificationPreferences.notificationFrequency.localizedName)
                            .foregroundColor(.gray)
                    }
                    
                    Button {
                        testNotificationSystem()
                    } label: {
                        Label(LocalizedString("Test Notifications", comment: "Test notifications button"), systemImage: "bell.and.waves.left.and.right")
                    }
                    
                    if let status = systemStatus {
                        VStack(alignment: .leading, spacing: 4) {
                            HStack {
                                Image(systemName: status.isHealthy ? "checkmark.circle.fill" : "exclamationmark.triangle.fill")
                                    .foregroundColor(status.isHealthy ? .green : .orange)
                                Text(LocalizedString("System Status", comment: "Notification system status"))
                                Spacer()
                                if isLoadingStatus {
                                    ProgressView()
                                        .scaleEffect(0.8)
                                } else {
                                    Button(LocalizedString("Refresh", comment: "Refresh status")) {
                                        loadSystemStatus()
                                    }
                                    .font(.caption)
                                }
                            }
                            
                            Text("\(LocalizedString("Pending", comment: "Pending notifications")): \(status.pendingNotifications) | \(LocalizedString("Memory", comment: "Memory usage")): \(formatBytes(status.memoryEstimate))")
                                .font(.caption)
                                .foregroundColor(.gray)
                        }
                    }
                }
                
                Section(header: Text(LocalizedString("Data Management", comment: "Settings section header"))) {
                    NavigationLink {
                        CompletedTasksView()
                    } label: {
                        Label(LocalizedString("Completed Tasks", comment: "Completed tasks button"), systemImage: "checkmark.circle")
                    }
                    
                    Button(role: .destructive) {
                        showingDeleteConfirmation = true
                    } label: {
                        Label(LocalizedString("Delete All Tasks", comment: "Delete all tasks button"), systemImage: "trash")
                    }
                }
                
                Section(header: Text(LocalizedString("About", comment: "About section header"))) {
                    HStack {
                        Text(LocalizedString("Version", comment: "Version label"))
                        Spacer()
                        Text(Bundle.main.appVersion)
                            .foregroundColor(.gray)
                    }
                }
            }
            .navigationTitle(LocalizedString("Settings", comment: "Navigation title"))
            .navigationBarTitleDisplayMode(.inline)
            .alert(LocalizedString("Delete All Tasks", comment: "Delete confirmation title"), isPresented: $showingDeleteConfirmation) {
                Button(LocalizedString("Cancel", comment: "Cancel button"), role: .cancel) { }
                Button(LocalizedString("Delete", comment: "Delete button"), role: .destructive) {
                    deleteAllTasks()
                }
            } message: {
                Text(LocalizedString("Are you sure you want to delete all tasks? This action cannot be undone.", comment: "Delete confirmation message"))
            }
        }
        .onAppear {
            selectedLanguage = languageManager.currentLanguage
            notificationPreferences = NotificationManager.shared.getPreferences()
            loadSystemStatus()
        }
        .onChange(of: notificationPreferences) { _, newPreferences in
            NotificationManager.shared.updatePreferences(newPreferences)
        }
    }
    
    private func deleteAllTasks() {
        for task in tasks {
            modelContext.delete(task)
        }
    }
    
    private func testNotificationSystem() {
        Task {
            await NotificationManager.shared.verifyNotificationSystem()
            // 可以在这里添加一个测试通知
            await scheduleTestNotification()
        }
    }
    
    private func scheduleTestNotification() async {
        let content = UNMutableNotificationContent()
        content.title = LocalizedString("Test Notification", comment: "Test notification title")
        content.body = LocalizedString("This is a test notification from Priority Matrix.", comment: "Test notification body")
        content.sound = .default
        content.categoryIdentifier = "test-notification"
        
        let trigger = UNTimeIntervalNotificationTrigger(timeInterval: 2, repeats: false)
        let request = UNNotificationRequest(identifier: "test-\(Date().timeIntervalSince1970)", content: content, trigger: trigger)
        
        do {
            try await UNUserNotificationCenter.current().add(request)
            print("测试通知已调度")
        } catch {
            print("测试通知调度失败: \(error.localizedDescription)")
        }
    }
    
    private func loadSystemStatus() {
        isLoadingStatus = true
        Task {
            let status = await NotificationManager.shared.getSystemResourceUsage()
            await MainActor.run {
                self.systemStatus = status
                self.isLoadingStatus = false
            }
        }
    }
    
    private func formatBytes(_ bytes: Int) -> String {
        let formatter = ByteCountFormatter()
        formatter.countStyle = .memory
        return formatter.string(fromByteCount: Int64(bytes))
    }
}

#Preview {
    do {
        let config = ModelConfiguration(isStoredInMemoryOnly: true)
        let container = try ModelContainer(for: UserTask.self, configurations: config)
        return SettingsView()
            .modelContainer(container)
    } catch {
        return Text("Failed to create preview: \(error.localizedDescription)")
    }
}

extension Bundle {
    var appVersion: String {
        return infoDictionary?["CFBundleShortVersionString"] as? String ?? "0.0.0"
    }
    
    var buildNumber: String {
        return infoDictionary?["CFBundleVersion"] as? String ?? "0"
    }
}
