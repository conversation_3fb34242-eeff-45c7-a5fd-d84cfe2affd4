import SwiftUI
import SwiftData

struct CompletedTasksView: View {
    @Environment(\.modelContext) private var modelContext
    @Query private var tasks: [UserTask]
    
    // 过滤已完成的任务
    private var completedTasks: [UserTask] {
        tasks.filter { $0.isCompleted }
    }
    
    var body: some View {
        NavigationView {
            List {
                ForEach(completedTasks) { task in
                    VStack(alignment: .leading, spacing: 4) {
                        HStack {
                            Text(task.title)
                                .strikethrough(true)
                                .foregroundColor(.gray)
                            
                            Spacer()
                            
                            Image(systemName: "checkmark.circle.fill")
                                .foregroundColor(.green)
                        }
                        
                        if !task.taskDescription.isEmpty {
                            Text(task.taskDescription)
                                .font(.subheadline)
                                .foregroundColor(.secondary)
                        }
                    }
                    .swipeActions(edge: .leading) {
                        Button {
                            task.isCompleted.toggle()  // 让 didSet 处理通知
                        } label: {
                            Label(LocalizedString("Uncomplete", comment: "Uncomplete task action"), systemImage: "arrow.uturn.backward")
                        }
                        .tint(.blue)
                    }
                    .swipeActions(edge: .trailing, allowsFullSwipe: true) {
                        Button(role: .destructive) {
                            modelContext.delete(task)
                            try? modelContext.save()
                        } label: {
                            Label(LocalizedString("Delete", comment: "Delete task action"), systemImage: "trash")
                        }
                    }
                }
            }
            .overlay {
                if completedTasks.isEmpty {
                    ContentUnavailableView(
                        LocalizedString("No Completed Tasks", comment: "Empty state title"),
                        systemImage: "checkmark.circle",
                        description: Text(LocalizedString("Tasks you complete will appear here", comment: "Empty state description"))
                    )
                }
            }
        }
    }
}

#Preview {
    do {
        let config = ModelConfiguration(isStoredInMemoryOnly: true)
        let container = try ModelContainer(for: UserTask.self, configurations: config)
        return CompletedTasksView()
            .modelContainer(container)
    } catch {
        return Text("Failed to create preview: \(error.localizedDescription)")
    }
} 