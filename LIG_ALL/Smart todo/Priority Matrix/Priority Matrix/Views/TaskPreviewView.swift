import SwiftUI
import SwiftData

struct TaskPreviewView: View {
    let task: UserTask
    let onComplete: () -> Void
    let onDelete: () -> Void
    let onTapDescription: () -> Void
    
    var body: some View {
        VStack(spacing: 0) {
            // 描述预览区域
            Text(task.taskDescription.isEmpty ? LocalizedString("No description", comment: "Empty task description") : task.taskDescription)
                .font(.body)
                .lineLimit(3)
                .padding()
                .frame(maxWidth: .infinity, alignment: .leading)
                .contentShape(Rectangle())
                .onTapGesture {
                    HapticManager.impact(style: .light)
                    onTapDescription()
                }
            
            if let dueDate = task.dueDate {
                Text(formatDueDate(dueDate))
                    .font(.subheadline)
                    .foregroundColor(isOverdue(dueDate) ? .red : .blue)
                    .frame(maxWidth: .infinity, alignment: .trailing)
                    .padding(.horizontal)
                    .padding(.bottom, 8)
            }
            
            // 操作按钮区域
            HStack(spacing: 0) {
                Button {
                    HapticManager.notification(type: .success)
                    onComplete()  // 让 UserTask 的 didSet 处理通知
                } label: {
                    Label(LocalizedString("Complete", comment: "Complete task button"), systemImage: "checkmark.circle.fill")
                        .labelStyle(.iconOnly)
                        .font(.title2)
                        .foregroundColor(.green)
                }
                .frame(maxWidth: .infinity)
                .padding(.vertical, 12)
                .accessibilityLabel(LocalizedString("Complete task", comment: "Accessibility label for complete button"))
                
                Divider()
                
                Button {
                    HapticManager.notification(type: .warning)
                    onDelete()
                } label: {
                    Label(LocalizedString("Delete", comment: "Delete task button"), systemImage: "trash.fill")
                        .labelStyle(.iconOnly)
                        .font(.title2)
                        .foregroundColor(.red)
                }
                .frame(maxWidth: .infinity)
                .padding(.vertical, 12)
                .accessibilityLabel(LocalizedString("Delete task", comment: "Accessibility label for delete button"))
            }
            .frame(height: 44)
        }
        .background(Color(UIColor.systemBackground))
        .cornerRadius(12)
        .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 4)
    }
    
    private func formatDueDate(_ date: Date) -> String {
        let calendar = Calendar.current
        let timeStr = date.formatted(.dateTime.hour().minute())
        
        let prefix = LocalizedString("Due Date: ", comment: "Due date label with colon")
        
        if calendar.isDateInToday(date) {
            let todayStr = LocalizedString("today", comment: "Today in due date")
            return "\(prefix)\(todayStr) \(timeStr)"
        } else if calendar.isDateInTomorrow(date) {
            let tomorrowStr = LocalizedString("tomorrow", comment: "Tomorrow in due date")
            return "\(prefix)\(tomorrowStr) \(timeStr)"
        } else {
            let dateStr = date.formatted(.dateTime.month().day())
            return "\(prefix)\(dateStr) \(timeStr)"
        }
    }
    
    private func isOverdue(_ date: Date) -> Bool {
        return date < Date()
    }
} 