import SwiftUI
import SwiftData

struct CalendarView: View {
    @Environment(\.modelContext) private var modelContext
    @Query private var tasks: [UserTask]
    @State private var showingCompletedTasks = false
    @Bindable var filterManager: TaskFilterManager = .shared
    @Bindable var sortManager: TaskSortManager = .shared
    
    var body: some View {
        NavigationView {
            Text(LocalizedString("Calendar View - Coming Soon", comment: "Calendar view placeholder"))
                .navigationTitle(LocalizedString("Calendar", comment: "Navigation title"))
                .navigationBarTitleDisplayMode(.inline)
                .toolbar {
                    ToolbarItem(placement: .navigationBarLeading) {
                        Menu {
                            Picker("Filter", selection: $filterManager.selectedFilter) {
                                Group {
                                    Text(TaskFilter.all.description).tag(TaskFilter.all)
                                    Text(TaskFilter.allImportant.description).tag(TaskFilter.allImportant)
                                    Text(TaskFilter.allUrgent.description).tag(TaskFilter.allUrgent)
                                }
                                
                                Divider()
                                
                                Group {
                                    Text(TaskFilter.quadrant1.description).tag(TaskFilter.quadrant1)
                                    Text(TaskFilter.quadrant2.description).tag(TaskFilter.quadrant2)
                                    Text(TaskFilter.quadrant3.description).tag(TaskFilter.quadrant3)
                                    Text(TaskFilter.quadrant4.description).tag(TaskFilter.quadrant4)
                                }
                            }
                        } label: {
                            HStack(spacing: 4) {
                                Text(filterManager.selectedFilter.shortDescription)
                                Image(systemName: "chevron.down")
                                    .font(.caption)
                            }
                            .foregroundColor(.blue)
                        }
                    }
                    
                    ToolbarItem(placement: .principal) {
                        Text(LocalizedString("Calendar", comment: "Navigation title"))
                            .font(.headline)
                    }
                    
                    ToolbarItem(placement: .navigationBarTrailing) {
                        Menu {
                            Picker("Sort", selection: $sortManager.selectedSortOption) {
                                ForEach(TaskSortOption.allCases, id: \.self) { option in
                                    Text(option.description).tag(option)
                                }
                            }
                            
                            Divider()
                            
                            Button {
                                sortManager.sortOrder = sortManager.sortOrder == .ascending ? .descending : .ascending
                            } label: {
                                Label(
                                    sortManager.sortOrder == .ascending ? "Sort Descending" : "Sort Ascending",
                                    systemImage: sortManager.sortOrder == .ascending ? "arrow.up" : "arrow.down"
                                )
                            }
                        } label: {
                            HStack(spacing: 4) {
                                Text(LocalizedString("Sort By", comment: "Sort menu button"))
                                Image(systemName: "chevron.down")
                                    .font(.caption)
                            }
                            .foregroundColor(.blue)
                        }
                    }
                }
                .sheet(isPresented: $showingCompletedTasks) {
                    CompletedTasksView()
                }
        }
    }
}

#Preview {
    CalendarView()
} 
