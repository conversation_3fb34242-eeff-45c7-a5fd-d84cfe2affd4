import SwiftUI
import SwiftData

struct TaskDetailView: View {
    @Bindable var task: UserTask
    @Environment(\.dismiss) private var dismiss
    @Environment(\.setTabBarVisibility) private var setTabBarVisibility
    @State private var editDraft: TaskEditDraft?
    @State private var isEditing = false
    @FocusState private var focusedField: Field?
    
    enum Field {
        case title
        case description
    }
    
    private func handleDismiss() {
        setTabBarVisibility(true)
        dismiss()
    }
    
    // 更新任务通知
    private func updateTaskNotifications() {
        Task {
            do {
                print("开始更新任务通知...")
                print("任务ID: \(task.id)")
                print("当前截止时间: \(task.dueDate?.formatted() ?? "无")")
                print("当前象限: \(task.quadrant)")
                
                try await NotificationManager.shared.scheduleNotification(for: task)
                print("✅ 任务通知更新成功")
            } catch {
                print("❌ 更新任务通知失败: \(error.localizedDescription)")
            }
        }
    }
    
    // 保存编辑
    private func saveEdit() {
        guard let draft = editDraft else { return }
        
        let oldDueDate = task.dueDate
        let oldQuadrant = task.quadrant
        
        // 更新任务属性
        task.title = draft.title
        task.taskDescription = draft.description
        task.isImportant = draft.isImportant
        task.isUrgent = draft.isUrgent
        task.dueDate = draft.hasDueDate ? draft.dueDate : nil
        
        // 检查是否需要更新通知
        if oldDueDate != task.dueDate || oldQuadrant != task.quadrant {
            print("检测到截止时间或优先级变化，更新通知...")
            print("旧截止时间: \(oldDueDate?.formatted() ?? "无")")
            print("新截止时间: \(task.dueDate?.formatted() ?? "无")")
            print("旧象限: \(oldQuadrant)")
            print("新象限: \(task.quadrant)")
            updateTaskNotifications()
        }
        
        isEditing = false
        editDraft = nil
    }
    
    // 开始编辑
    private func startEditing() {
        editDraft = TaskEditDraft(from: task)
        isEditing = true
        focusedField = .title
    }
    
    // 取消编辑
    private func cancelEdit() {
        isEditing = false
        editDraft = nil
        focusedField = nil
    }
    
    var body: some View {
        ScrollView {
            VStack(spacing: 16) {
                // Task Details Card
                VStack(alignment: .leading, spacing: 16) {
                    if let draft = editDraft, isEditing {
                        TextField(LocalizedString("Title", comment: "Task title"), 
                                text: Binding(
                                    get: { draft.title },
                                    set: { draft.title = $0 }
                                ))
                            .padding(10)
                            .background(Color(.secondarySystemBackground))
                            .cornerRadius(8)
                            .overlay(
                                RoundedRectangle(cornerRadius: 8)
                                    .stroke(Color(.separator), lineWidth: 1)
                            )
                            .focused($focusedField, equals: .title)
                        
                        TextEditor(text: Binding(
                            get: { draft.description },
                            set: { draft.description = $0 }
                        ))
                            .frame(minHeight: 100)
                            .padding(2)
                            .background(Color(.secondarySystemBackground))
                            .cornerRadius(8)
                            .overlay(
                                RoundedRectangle(cornerRadius: 8)
                                    .stroke(Color(.separator), lineWidth: 1)
                            )
                            .focused($focusedField, equals: .description)
                            .overlay(
                                Group {
                                    if draft.description.isEmpty {
                                        Text(LocalizedString("Description", comment: "Task description placeholder"))
                                            .foregroundColor(.secondary)
                                            .padding(.leading, 4)
                                    }
                                },
                                alignment: .topLeading
                            )
                        
                        // Important Toggle with Icon
                        HStack(spacing: 6) {
                            Image(systemName: "star.fill")
                                .foregroundColor(.red)
                                .frame(width: 20)
                            Toggle(isOn: Binding(
                                get: { draft.isImportant },
                                set: { draft.isImportant = $0 }
                            )) {
                                Text(LocalizedString("Important", comment: "Important toggle"))
                            }
                        }
                        
                        // Urgent Toggle with Icon
                        HStack(spacing: 6) {
                            Image(systemName: "timer")
                                .foregroundColor(.orange)
                                .frame(width: 20)
                            Toggle(isOn: Binding(
                                get: { draft.isUrgent },
                                set: { draft.isUrgent = $0 }
                            )) {
                                Text(LocalizedString("Urgent", comment: "Urgent toggle"))
                            }
                        }
                        
                        // Due Date Section
                        HStack(spacing: 6) {
                            Image(systemName: "calendar")
                                .foregroundColor(.blue)
                                .frame(width: 20)
                            Toggle(isOn: Binding(
                                get: { draft.hasDueDate },
                                set: { draft.hasDueDate = $0 }
                            )) {
                                Text(LocalizedString("Due Date", comment: "Due date toggle"))
                            }
                        }
                        
                        if draft.hasDueDate {
                            DatePicker(
                                "",
                                selection: Binding(
                                    get: { draft.dueDate ?? Date() },
                                    set: { draft.dueDate = $0 }
                                ),
                                in: Date()...,
                                displayedComponents: [.date, .hourAndMinute]
                            )
                            .datePickerStyle(.compact)
                            .labelsHidden()
                        }
                    } else {
                        // View mode with matching style
                        VStack(alignment: .leading, spacing: 16) {
                            // Title
                            Text(task.title)
                                .font(.body)
                                .padding(10)
                                .frame(maxWidth: .infinity, alignment: .leading)
                                .background(Color(.secondarySystemBackground))
                                .cornerRadius(8)
                                .overlay(
                                    RoundedRectangle(cornerRadius: 8)
                                        .stroke(Color(.separator), lineWidth: 1)
                                )
                            
                            // Description
                            Text(task.taskDescription.isEmpty ? LocalizedString("No description", comment: "Empty description placeholder") : task.taskDescription)
                                .font(.body)
                                .foregroundColor(task.taskDescription.isEmpty ? .secondary : .primary)
                                .padding(10)
                                .frame(minHeight: 100, alignment: .topLeading)
                                .frame(maxWidth: .infinity, alignment: .leading)
                                .background(Color(.secondarySystemBackground))
                                .cornerRadius(8)
                                .overlay(
                                    RoundedRectangle(cornerRadius: 8)
                                        .stroke(Color(.separator), lineWidth: 1)
                                )
                            
                            // Important Toggle with Icon
                            HStack(spacing: 6) {
                                Image(systemName: "star.fill")
                                    .foregroundColor(.red)
                                    .frame(width: 20)
                                Toggle(LocalizedString("Important", comment: "Important toggle"), isOn: .constant(task.isImportant))
                                    .disabled(true)
                            }
                            
                            // Urgent Toggle with Icon
                            HStack(spacing: 6) {
                                Image(systemName: "timer")
                                    .foregroundColor(.orange)
                                    .frame(width: 20)
                                Toggle(LocalizedString("Urgent", comment: "Urgent toggle"), isOn: .constant(task.isUrgent))
                                    .disabled(true)
                            }
                            
                            // Due Date Section
                            VStack(alignment: .leading, spacing: 8) {
                                HStack(spacing: 6) {
                                    Image(systemName: "calendar")
                                        .foregroundColor(.blue)
                                        .frame(width: 20)
                                    Toggle(LocalizedString("Due Date", comment: "Due date toggle"), isOn: .constant(task.dueDate != nil))
                                        .disabled(true)
                                }
                                
                                if let dueDate = task.dueDate {
                                    HStack {
                                        Spacer()
                                            .frame(width: 26)
                                        DatePicker(
                                            "",
                                            selection: .constant(dueDate),
                                            displayedComponents: [.date, .hourAndMinute]
                                        )
                                        .datePickerStyle(.compact)
                                        .labelsHidden()
                                        .disabled(true)
                                    }
                                }
                            }
                        }
                    }
                    
                    Text(LocalizedString("Created", comment: "Created date label") + ": " + task.dateCreated.formatted())
                        .font(.footnote)
                        .foregroundColor(.secondary)
                }
                .padding(16)
                .background(Color(.tertiarySystemBackground))
                .cornerRadius(12)
                .shadow(color: Color.black.opacity(0.1), radius: 6, x: 0, y: 0)
                
                // AI Analysis Section
                if let analysis = task.aiAnalysis {
                    // Analysis Card
                    VStack(alignment: .leading, spacing: 12) {
                        HStack {
                            Image(systemName: "brain")
                                .foregroundColor(.white)
                            Text(LocalizedString("Analysis", comment: "Analysis subsection title"))
                                .font(.headline)
                                .foregroundColor(.white)
                        }
                        Text(analysis.explanation)
                            .font(.body)
                            .foregroundColor(.white)
                    }
                    .padding(16)
                    .frame(maxWidth: .infinity, alignment: .leading)
                    .background(Color.analysisCard)
                    .cornerRadius(12)
                    
                    // Hidden Value Card
                    VStack(alignment: .leading, spacing: 12) {
                        HStack {
                            Image(systemName: "sparkles")
                                .foregroundColor(.white)
                            Text(LocalizedString("Hidden Value", comment: "Hidden value subsection title"))
                                .font(.headline)
                                .foregroundColor(.white)
                        }
                        Text(analysis.hiddenValue)
                            .font(.body)
                            .foregroundColor(.white)
                    }
                    .padding(16)
                    .frame(maxWidth: .infinity, alignment: .leading)
                    .background(Color.hiddenValueCard)
                    .cornerRadius(12)
                    
                    // Suggestions Card
                    VStack(alignment: .leading, spacing: 12) {
                        HStack {
                            Image(systemName: "lightbulb")
                                .foregroundColor(.white)
                            Text(LocalizedString("Suggestions", comment: "Suggestions subsection title"))
                                .font(.headline)
                                .foregroundColor(.white)
                        }
                        ForEach(analysis.suggestions, id: \.self) { suggestion in
                            HStack(alignment: .top, spacing: 8) {
                                Circle()
                                    .fill(Color.white.opacity(0.8))
                                    .frame(width: 4, height: 4)
                                    .padding(.top, 8)
                                Text(suggestion)
                                    .font(.body)
                                    .foregroundColor(.white)
                                    .fixedSize(horizontal: false, vertical: true)
                            }
                        }
                    }
                    .padding(16)
                    .frame(maxWidth: .infinity, alignment: .leading)
                    .background(Color.suggestionsCard)
                    .cornerRadius(12)
                }
            }
            .padding(16)
        }
        .navigationBarBackButtonHidden(true)
        .toolbar {
            ToolbarItem(placement: .navigationBarLeading) {
                if isEditing {
                    Button(LocalizedString("Cancel", comment: "Cancel edit button")) {
                        cancelEdit()
                    }
                } else {
                    Button(action: handleDismiss) {
                        HStack(spacing: 4) {
                            Image(systemName: "chevron.left")
                            Text(LocalizedString("Back", comment: "Back button"))
                        }
                    }
                }
            }
            
            ToolbarItem(placement: .navigationBarTrailing) {
                if isEditing {
                    Button(LocalizedString("Save", comment: "Save changes button")) {
                        saveEdit()
                    }
                    .disabled(editDraft?.title.isEmpty ?? true)
                } else {
                    Button(LocalizedString("Edit", comment: "Edit task button")) {
                        startEditing()
                    }
                }
            }
        }
        .onAppear {
            setTabBarVisibility(false)
        }
        .onChange(of: task.dueDate) { oldValue, newValue in
            if oldValue != newValue {
                print("截止时间发生变化")
                print("旧值: \(oldValue?.formatted() ?? "无")")
                print("新值: \(newValue?.formatted() ?? "无")")
                updateTaskNotifications()
            }
        }
        .onChange(of: task.isImportant) { oldValue, newValue in
            if oldValue != newValue {
                print("重要性发生变化: \(oldValue) -> \(newValue)")
                updateTaskNotifications()
            }
        }
        .onChange(of: task.isUrgent) { oldValue, newValue in
            if oldValue != newValue {
                print("紧急性发生变化: \(oldValue) -> \(newValue)")
                updateTaskNotifications()
            }
        }
    }
    
    private func formatDueDate(_ date: Date) -> String {
        let calendar = Calendar.current
        let timeStr = date.formatted(.dateTime.hour().minute())
        
        if calendar.isDateInToday(date) {
            return String(format: LocalizedString("Today %@", comment: "Today with time"), timeStr)
        } else if calendar.isDateInTomorrow(date) {
            return String(format: LocalizedString("Tomorrow %@", comment: "Tomorrow with time"), timeStr)
        } else {
            return date.formatted(.dateTime.month().day().hour().minute())
        }
    }
} 
