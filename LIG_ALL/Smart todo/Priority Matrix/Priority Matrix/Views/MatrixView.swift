import SwiftUI
import SwiftData

struct MatrixView: View {
    @Query(sort: \UserTask.dateCreated, order: .reverse) private var tasks: [UserTask]
    @Bindable var filterManager: TaskFilterManager = .shared
    @Bindable var sortManager: TaskSortManager = .shared
    @State private var hoveredTaskId: PersistentIdentifier?
    @State private var expandedQuadrant: Int? // nil means no quadrant is expanded
    @State private var showingCompletedTasks = false
    @State private var showingMatrixExplanation = false // 新增：控制解释弹窗的显示
    
    // 过滤未完成的任务
    private var uncompletedTasks: [UserTask] {
        let filtered = tasks.filter { !$0.isCompleted }
        return sortManager.sortTasks(filterManager.applyFilters(to: filtered))
    }
    
    // 根据过滤器确定要显示的象限
    private var visibleQuadrants: [Int] {
        switch filterManager.selectedFilter {
        case .all:
            return [1, 2, 3, 4]
        case .allImportant:
            return [1, 2]
        case .allUrgent:
            return [1, 3]
        case .quadrant1:
            return [1]
        case .quadrant2:
            return [2]
        case .quadrant3:
            return [3]
        case .quadrant4:
            return [4]
        case .dueToday, .dueTomorrow, .dueThisWeek, .overdue:
            return [1, 2, 3, 4]
        }
    }
    
    // 确定网格列数
    private var gridColumns: [GridItem] {
        switch filterManager.selectedFilter {
        case .all, .dueToday, .dueTomorrow, .dueThisWeek, .overdue:
            return [GridItem(.flexible(), spacing: 8), GridItem(.flexible(), spacing: 8)]
        case .allImportant, .allUrgent:
            return [GridItem(.flexible(), spacing: 8), GridItem(.flexible(), spacing: 8)]
        default:
            return [GridItem(.flexible())]
        }
    }
    
    // 确定象限高度
    private func getQuadrantHeight() -> CGFloat {
        switch filterManager.selectedFilter {
        case .all, .dueToday, .dueTomorrow, .dueThisWeek, .overdue:
            return 300
        default:
            return 600
        }
    }
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Filter chips
                ScrollView(.horizontal, showsIndicators: false) {
                    HStack(spacing: 8) {
                        // 主过滤器
                        if filterManager.selectedFilter != .all {
                            FilterChip(
                                text: filterManager.selectedFilter.shortDescription,
                                isSelected: true
                            ) {
                                filterManager.selectedFilter = .all
                            }
                        }
                        
                        // 截止日期过滤器
                        if let dueDateFilter = filterManager.selectedDueDateFilter {
                            FilterChip(
                                text: dueDateFilter.shortDescription,
                                isSelected: true
                            ) {
                                filterManager.selectedDueDateFilter = nil
                            }
                        }
                    }
                    .padding(.horizontal)
                }
                .padding(.vertical, 8)
                
                // Matrix content
                if let expanded = expandedQuadrant {
                    // Show only the expanded quadrant
                    QuadrantView(
                        title: quadrantTitle(for: expanded),
                        tasks: uncompletedTasks.filter { $0.quadrant == expanded },
                        taskColor: quadrantColor(for: expanded),
                        hoveredTaskId: $hoveredTaskId,
                        isExpanded: true,
                        onTitleTap: { expandedQuadrant = nil },
                        height: 600,
                        quadrant: expanded,
                        filterManager: filterManager
                    )
                    .padding(8)
                } else {
                    // Show filtered quadrants in grid
                    ScrollView {
                        LazyVGrid(columns: gridColumns, spacing: 8) {
                            ForEach(visibleQuadrants, id: \.self) { quadrant in
                                QuadrantView(
                                    title: quadrantTitle(for: quadrant),
                                    tasks: uncompletedTasks.filter { $0.quadrant == quadrant },
                                    taskColor: quadrantColor(for: quadrant),
                                    hoveredTaskId: $hoveredTaskId,
                                    isExpanded: false,
                                    onTitleTap: { expandedQuadrant = quadrant },
                                    height: getQuadrantHeight(),
                                    quadrant: quadrant,
                                    filterManager: filterManager
                                )
                            }
                        }
                        .padding(8)
                    }
                }
            }
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Menu {
                        // 主过滤器菜单
                        Menu(LocalizedString("Filter by Priority", comment: "Priority filter menu")) {
                            Picker("Priority Filter", selection: $filterManager.selectedFilter) {
                                Group {
                                    Text(TaskFilter.all.description).tag(TaskFilter.all)
                                    Text(TaskFilter.allImportant.description).tag(TaskFilter.allImportant)
                                    Text(TaskFilter.allUrgent.description).tag(TaskFilter.allUrgent)
                                }
                                
                                Divider()
                                
                                Group {
                                    Text(TaskFilter.quadrant1.description).tag(TaskFilter.quadrant1)
                                    Text(TaskFilter.quadrant2.description).tag(TaskFilter.quadrant2)
                                    Text(TaskFilter.quadrant3.description).tag(TaskFilter.quadrant3)
                                    Text(TaskFilter.quadrant4.description).tag(TaskFilter.quadrant4)
                                }
                            }
                        }
                        
                        // 截止日期过滤器菜单
                        Menu(LocalizedString("Filter by Due Date", comment: "Due date filter menu")) {
                            Button(role: filterManager.selectedDueDateFilter == nil ? .destructive : nil) {
                                filterManager.selectedDueDateFilter = nil
                            } label: {
                                Text(LocalizedString("None", comment: "No filter option"))
                            }
                            
                            Divider()
                            
                            Group {
                                Button {
                                    filterManager.selectedDueDateFilter = .overdue
                                } label: {
                                    if filterManager.selectedDueDateFilter == .overdue {
                                        Label(TaskFilter.overdue.description, systemImage: "checkmark")
                                    } else {
                                        Text(TaskFilter.overdue.description)
                                    }
                                }
                                
                                Button {
                                    filterManager.selectedDueDateFilter = .dueToday
                                } label: {
                                    if filterManager.selectedDueDateFilter == .dueToday {
                                        Label(TaskFilter.dueToday.description, systemImage: "checkmark")
                                    } else {
                                        Text(TaskFilter.dueToday.description)
                                    }
                                }
                                
                                Button {
                                    filterManager.selectedDueDateFilter = .dueTomorrow
                                } label: {
                                    if filterManager.selectedDueDateFilter == .dueTomorrow {
                                        Label(TaskFilter.dueTomorrow.description, systemImage: "checkmark")
                                    } else {
                                        Text(TaskFilter.dueTomorrow.description)
                                    }
                                }
                                
                                Button {
                                    filterManager.selectedDueDateFilter = .dueThisWeek
                                } label: {
                                    if filterManager.selectedDueDateFilter == .dueThisWeek {
                                        Label(TaskFilter.dueThisWeek.description, systemImage: "checkmark")
                                    } else {
                                        Text(TaskFilter.dueThisWeek.description)
                                    }
                                }
                            }
                        }
                        
                        Divider()
                        
                        Button(role: .destructive) {
                            filterManager.resetFilters()
                        } label: {
                            Text(LocalizedString("Reset Filters", comment: "Reset filters button"))
                        }
                    } label: {
                        HStack(spacing: 4) {
                            Text(LocalizedString("Filter", comment: "Filter menu button"))
                            Image(systemName: "chevron.down")
                                .font(.caption)
                        }
                        .foregroundColor(.blue)
                    }
                }
                
                ToolbarItem(placement: .principal) {
                    Text(LocalizedString("Priority Matrix", comment: "Navigation title"))
                        .font(.headline)
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    HStack(spacing: 12) {
                        Button {
                            showingMatrixExplanation.toggle()
                        } label: {
                            Image(systemName: "questionmark.circle.fill")
                                .font(.footnote)
                                .foregroundColor(.secondary)
                                .offset(x: -12)
                        }
                        
                        Menu {
                            Picker("Sort", selection: $sortManager.selectedSortOption) {
                                ForEach(TaskSortOption.allCases, id: \.self) { option in
                                    Text(option.description).tag(option)
                                }
                            }
                            
                            Divider()
                            
                            Button {
                                sortManager.sortOrder = sortManager.sortOrder == .ascending ? .descending : .ascending
                            } label: {
                                Label(
                                    sortManager.sortOrder == .ascending ? "Sort Descending" : "Sort Ascending",
                                    systemImage: sortManager.sortOrder == .ascending ? "arrow.up" : "arrow.down"
                                )
                            }
                        } label: {
                            HStack(spacing: 4) {
                                Text(LocalizedString("Sort By", comment: "Sort menu button"))
                                Image(systemName: "chevron.down")
                                    .font(.caption)
                            }
                            .foregroundColor(.blue)
                        }
                    }
                }
            }
            .sheet(isPresented: $showingMatrixExplanation) {
                MatrixExplanationView()
            }
            .frame(maxWidth: .infinity, maxHeight: .infinity)
            .background(Color(UIColor.systemBackground))
            .animation(.easeInOut, value: filterManager.selectedFilter)
        }
    }
    
    private func quadrantTitle(for quadrant: Int) -> String {
        switch quadrant {
        case 1: return LocalizedString("Important & Urgent", comment: "Matrix quadrant title")
        case 2: return LocalizedString("Important & Not Urgent", comment: "Matrix quadrant title")
        case 3: return LocalizedString("Not Important & Urgent", comment: "Matrix quadrant title")
        case 4: return LocalizedString("Not Important & Not Urgent", comment: "Matrix quadrant title")
        default: return ""
        }
    }
    
    private func quadrantColor(for quadrant: Int) -> Color {
        switch quadrant {
        case 1: return Color.quadrant1 // 重要且紧急
        case 2: return Color.quadrant2 // 重要不紧急
        case 3: return Color.quadrant3 // 不重要但紧急
        case 4: return Color.quadrant4 // 不重要不紧急
        default: return Color(.systemGray)
        }
    }
}

// 新增：矩阵解释视图
struct MatrixExplanationView: View {
    @Environment(\.dismiss) private var dismiss
    @State private var isBasicConceptExpanded = true
    @State private var isQuadrantDetailsExpanded = true
    @State private var isUsageTipsExpanded = true
    @State private var isNotificationRulesExpanded = false
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(alignment: .leading, spacing: 16) {
                    // Basic Concept
                    DisclosureGroup(
                        isExpanded: $isBasicConceptExpanded,
                        content: {
                            Text(LocalizedString("The Eisenhower Matrix (also known as Priority Matrix) is a time management tool developed by Dwight D. Eisenhower. As a successful military commander and politician, he was known for his exceptional ability to manage time and prioritize tasks. This tool helped him maintain efficiency and organization while handling complex military and political affairs.", comment: "Matrix introduction"))
                                .font(.subheadline)
                                .foregroundColor(.secondary)
                                .padding(.vertical, 8)
                        },
                        label: {
                            Label(
                                LocalizedString("Basic Concept", comment: "Basic concept section"),
                                systemImage: "square.grid.2x2"
                            )
                            .font(.headline)
                        }
                    )
                    .padding(.horizontal)
                    
                    // Quadrant Details
                    DisclosureGroup(
                        isExpanded: $isQuadrantDetailsExpanded,
                        content: {
                            VStack(alignment: .leading, spacing: 16) {
                                quadrantExplanation(
                                    title: LocalizedString("Quadrant 1: Important & Urgent", comment: "Quadrant 1 title"),
                                    color: Color.quadrant1,
                                    description: LocalizedString("Crises and problems that need immediate attention. Examples: approaching deadlines, urgent meetings, emergencies, health issues. These tasks typically create stress and should be minimized through better planning.", comment: "Quadrant 1 description")
                                )
                                
                                quadrantExplanation(
                                    title: LocalizedString("Quadrant 2: Important & Not Urgent", comment: "Quadrant 2 title"),
                                    color: Color.quadrant2,
                                    description: LocalizedString("Activities focused on long-term development. Examples: strategic planning, self-improvement, relationship building, health management, preventive maintenance. This quadrant deserves most of your time as it helps prevent crises and creates lasting value.", comment: "Quadrant 2 description")
                                )
                                
                                quadrantExplanation(
                                    title: LocalizedString("Quadrant 3: Not Important & Urgent", comment: "Quadrant 3 title"),
                                    color: Color.quadrant3,
                                    description: LocalizedString("Distracting activities. Examples: some meetings, certain calls and emails, others' urgent requests. These tasks often appear important (due to urgency) but contribute little to your goals. Try to delegate or automate them.", comment: "Quadrant 3 description")
                                )
                                
                                quadrantExplanation(
                                    title: LocalizedString("Quadrant 4: Not Important & Not Urgent", comment: "Quadrant 4 title"),
                                    color: Color.quadrant4,
                                    description: LocalizedString("Wasting time. Examples: excessive social media, entertainment, aimless browsing. These activities not only fail to create value but also consume your time and energy. Minimize them or consciously transform them into genuine rest and relaxation.", comment: "Quadrant 4 description")
                                )
                            }
                            .padding(.vertical, 8)
                        },
                        label: {
                            Label(
                                LocalizedString("Quadrant Details", comment: "Quadrant details section"),
                                systemImage: "square.grid.2x2.fill"
                            )
                            .font(.headline)
                        }
                    )
                    .padding(.horizontal)
                    
                    // Usage Tips
                    DisclosureGroup(
                        isExpanded: $isUsageTipsExpanded,
                        content: {
                            VStack(alignment: .leading, spacing: 12) {
                                Text(LocalizedString("Daily Habits:", comment: "Daily habits title"))
                                    .font(.headline)
                                Text(LocalizedString("• Check first quadrant tasks every morning", comment: "Daily habit 1"))
                                Text(LocalizedString("• Reserve ample time for second quadrant tasks", comment: "Daily habit 2"))
                                Text(LocalizedString("• Limit time spent on third quadrant tasks", comment: "Daily habit 3"))
                                Text(LocalizedString("• Be mindful of time spent on fourth quadrant activities", comment: "Daily habit 4"))
                                
                                Text(LocalizedString("Long-term Strategies:", comment: "Long-term strategies title"))
                                    .font(.headline)
                                    .padding(.top, 8)
                                Text(LocalizedString("• Reduce urgent tasks through planning", comment: "Strategy 1"))
                                Text(LocalizedString("• Create systems to automate repetitive tasks", comment: "Strategy 2"))
                                Text(LocalizedString("• Learn to say \"no\" to unimportant tasks", comment: "Strategy 3"))
                                Text(LocalizedString("• Regularly review and adjust task categorization", comment: "Strategy 4"))
                            }
                            .padding(.vertical, 8)
                        },
                        label: {
                            Label(
                                LocalizedString("Usage Tips", comment: "Usage tips section"),
                                systemImage: "lightbulb"
                            )
                            .font(.headline)
                        }
                    )
                    .padding(.horizontal)
                    
                    // Notification Rules
                    DisclosureGroup(
                        isExpanded: $isNotificationRulesExpanded,
                        content: {
                            VStack(alignment: .leading, spacing: 12) {
                                Text(LocalizedString("Tasks with Due Date:", comment: "Tasks with due date section"))
                                    .font(.headline)
                                
                                Group {
                                    HStack(alignment: .top, spacing: 8) {
                                        Text("Q1:")
                                            .foregroundColor(Color.quadrant1)
                                            .fontWeight(.bold)
                                        Text(LocalizedString("Every 4 hours, with an extra reminder in the last hour", comment: "Q1 notification rule"))
                                    }
                                    
                                    HStack(alignment: .top, spacing: 8) {
                                        Text("Q2:")
                                            .foregroundColor(Color.quadrant2)
                                            .fontWeight(.bold)
                                        Text(LocalizedString("48 hours and 24 hours before deadline", comment: "Q2 notification rule"))
                                    }
                                    
                                    HStack(alignment: .top, spacing: 8) {
                                        Text("Q3:")
                                            .foregroundColor(Color.quadrant3)
                                            .fontWeight(.bold)
                                        Text(LocalizedString("Single reminder 24 hours before deadline", comment: "Q3 notification rule"))
                                    }
                                    
                                    HStack(alignment: .top, spacing: 8) {
                                        Text("Q4:")
                                            .foregroundColor(Color.quadrant4)
                                            .fontWeight(.bold)
                                        Text(LocalizedString("No reminders", comment: "Q4 notification rule"))
                                    }
                                }
                                
                                Text(LocalizedString("Tasks without Due Date:", comment: "Tasks without due date section"))
                                    .font(.headline)
                                    .padding(.top, 8)
                                
                                Group {
                                    HStack(alignment: .top, spacing: 8) {
                                        Text(LocalizedString("Important Tasks:", comment: "Important tasks label"))
                                            .fontWeight(.bold)
                                        Text(LocalizedString("Reminder after 24 hours to set a due date", comment: "Important tasks notification rule"))
                                    }
                                    
                                    HStack(alignment: .top, spacing: 8) {
                                        Text(LocalizedString("Other Tasks:", comment: "Other tasks label"))
                                            .fontWeight(.bold)
                                        Text(LocalizedString("Smart reminders based on quadrant priority", comment: "Other tasks notification rule"))
                                    }
                                }
                            }
                            .padding(.vertical, 8)
                        },
                        label: {
                            Label(
                                LocalizedString("Notification Rules", comment: "Notification rules section"),
                                systemImage: "bell.badge"
                            )
                            .font(.headline)
                        }
                    )
                    .padding(.horizontal)
                }
                .padding(.vertical)
            }
            .navigationTitle(LocalizedString("User Guide", comment: "Guide title"))
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button {
                        dismiss()
                    } label: {
                        Image(systemName: "xmark.circle.fill")
                            .foregroundColor(.secondary)
                    }
                }
            }
        }
    }
    
    private func quadrantExplanation(title: String, color: Color, description: String) -> some View {
        VStack(alignment: .leading, spacing: 4) {
            HStack {
                Circle()
                    .fill(color)
                    .frame(width: 12, height: 12)
                Text(title)
                    .font(.headline)
            }
            Text(description)
                .font(.subheadline)
                .foregroundColor(.secondary)
        }
    }
}

// 新增：过滤器标签组件
struct FilterChip: View {
    let text: String
    let isSelected: Bool
    let onTap: () -> Void
    
    var body: some View {
        Button(action: onTap) {
            HStack(spacing: 4) {
                Text(text)
                    .font(.subheadline)
                
                if isSelected {
                    Image(systemName: "xmark")
                        .font(.caption2)
                }
            }
            .padding(.horizontal, 12)
            .padding(.vertical, 6)
            .background(Color.blue.opacity(0.1))
            .foregroundColor(.blue)
            .cornerRadius(16)
        }
    }
}

#Preview {
    do {
        let config = ModelConfiguration(isStoredInMemoryOnly: true)
        let container = try ModelContainer(for: UserTask.self, configurations: config)
        return MatrixView()
            .modelContainer(container)
    } catch {
        return Text("Failed to create preview: \(error.localizedDescription)")
    }
} 