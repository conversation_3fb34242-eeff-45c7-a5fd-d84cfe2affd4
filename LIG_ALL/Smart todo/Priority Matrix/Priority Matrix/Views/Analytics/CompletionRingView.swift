import SwiftUI

struct CompletionRingView: View {
    let completionRates: [CompletionRate]
    let isCompact: Bool
    @State private var selectedQuadrant: Int?
    
    private var overallRate: CompletionRate? {
        completionRates.first { $0.quadrant == 0 }
    }
    
    private var quadrantRates: [CompletionRate] {
        completionRates.filter { $0.quadrant != 0 }
    }
    
    var body: some View {
        if isCompact {
            compactView
        } else {
            expandedView
        }
    }
    
    private var compactView: some View {
        VStack(spacing: 8) {
            Text(LocalizedString("Task Completion", comment: "Chart title"))
                .font(.subheadline)
                .foregroundColor(.secondary)
            
            ZStack {
                // 绘制象限环形
                ForEach(quadrantRates) { rate in
                    let lineWidth: CGFloat = {
                        switch rate.quadrant {
                        case 1: return 8  // Q1 最宽
                        case 2: return 6  // Q2 次宽
                        case 3: return 4  // Q3 较窄
                        case 4: return 2  // Q4 最窄
                        default: return 2
                        }
                    }()
                    
                    let size: CGFloat = {
                        switch rate.quadrant {
                        case 1: return 100  // Q1 最大
                        case 2: return 85   // Q2 次大
                        case 3: return 70   // Q3 较小
                        case 4: return 55   // Q4 最小
                        default: return 55
                        }
                    }()
                    
                    RingShape(progress: rate.percentage / 100)
                        .stroke(rate.color.opacity(0.8),
                               style: StrokeStyle(lineWidth: lineWidth, lineCap: .round))
                        .frame(width: size, height: size)
                }
                
                // 只显示完成率文字
                if let overall = overallRate {
                    Text(String(format: "%.0f%%", overall.percentage))
                        .font(.system(size: 20, weight: .bold))
                        .foregroundColor(.primary)
                }
            }
            .frame(height: 100)
        }
    }
    
    private var expandedView: some View {
        VStack(spacing: 20) {
            ZStack {
                // 绘制象限环形
                ForEach(quadrantRates) { rate in
                    let isSelected = selectedQuadrant == rate.quadrant
                    let lineWidth: CGFloat = {
                        switch rate.quadrant {
                        case 1: return isSelected ? 24 : 20  // Q1 最宽
                        case 2: return isSelected ? 20 : 16  // Q2 次宽
                        case 3: return isSelected ? 16 : 12  // Q3 较窄
                        case 4: return isSelected ? 12 : 8   // Q4 最窄
                        default: return 8
                        }
                    }()
                    
                    let size: CGFloat = {
                        switch rate.quadrant {
                        case 1: return 240  // Q1 最大
                        case 2: return 200  // Q2 次大
                        case 3: return 160  // Q3 较小
                        case 4: return 120  // Q4 最小
                        default: return 120
                        }
                    }()
                    
                    RingShape(progress: rate.percentage / 100)
                        .stroke(
                            rate.color.opacity(isSelected ? 1 : 0.8),
                            style: StrokeStyle(lineWidth: lineWidth, lineCap: .round)
                        )
                        .frame(width: size, height: size)
                        .scaleEffect(isSelected ? 1.05 : 1.0)
                        .animation(.spring(), value: isSelected)
                }
                
                // 只显示完成率文字和标签
                if let overall = overallRate {
                    VStack(spacing: 4) {
                        Text(String(format: "%.1f%%", overall.percentage))
                            .font(.system(size: 32, weight: .bold))
                            .foregroundColor(.primary)
                        Text(LocalizedString("Total Complete", comment: "Completion rate label"))
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
            }
            .frame(height: 250)
            .contentShape(Rectangle())
            
            VStack(spacing: 12) {
                ForEach(quadrantRates) { rate in
                    HStack(spacing: 12) {
                        Circle()
                            .fill(rate.color)
                            .frame(width: 12, height: 12)
                        
                        Text(rate.title)
                            .font(.subheadline)
                        
                        Spacer()
                        
                        Text("\(rate.completed)/\(rate.total)")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                        
                        Text("(\(String(format: "%.1f%%", rate.percentage)))")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    .padding(.horizontal)
                    .contentShape(Rectangle())
                    .onTapGesture {
                        withAnimation(.spring()) {
                            selectedQuadrant = selectedQuadrant == rate.quadrant ? nil : rate.quadrant
                        }
                    }
                }
            }
        }
        .padding()
    }
}

struct RingShape: Shape {
    var progress: Double
    var startAngle: Angle = .degrees(-90)  // 统一从顶部开始
    
    var animatableData: Double {
        get { progress }
        set { progress = newValue }
    }
    
    func path(in rect: CGRect) -> Path {
        var path = Path()
        let center = CGPoint(x: rect.midX, y: rect.midY)
        let radius = min(rect.width, rect.height) / 2
        let endAngle = startAngle + .degrees(360 * progress)  // 使用完整的360度
        
        path.addArc(
            center: center,
            radius: radius,
            startAngle: startAngle,
            endAngle: endAngle,
            clockwise: false
        )
        
        return path
    }
} 