import SwiftUI

struct CompletionRate: Identifiable {
    let id = UUID()
    let quadrant: Int
    let completed: Int
    let total: Int
    
    var percentage: Double {
        total > 0 ? Double(completed) / Double(total) * 100 : 0
    }
    
    var title: String {
        switch quadrant {
        case 0: return LocalizedString("Overall", comment: "Overall completion rate")
        case 1: return LocalizedString("Q1", comment: "Quadrant 1")
        case 2: return LocalizedString("Q2", comment: "Quadrant 2")
        case 3: return LocalizedString("Q3", comment: "Quadrant 3")
        case 4: return LocalizedString("Q4", comment: "Quadrant 4")
        default: return ""
        }
    }
    
    var color: Color {
        switch quadrant {
        case 0: return .blue  // 总体完成率使用蓝色
        case 1: return Color.quadrant1
        case 2: return Color.quadrant2
        case 3: return Color.quadrant3
        case 4: return Color.quadrant4
        default: return Color(.systemGray)
        }
    }
} 