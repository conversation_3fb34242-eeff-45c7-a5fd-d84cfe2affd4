import SwiftUI
import SwiftData

struct AnalyticsView: View {
    @Query(sort: \UserTask.dateCreated, order: .reverse) private var tasks: [UserTask]
    @Environment(\.modelContext) private var modelContext
    @State private var expandedChart: AnalyticsChart?
    
    enum AnalyticsChart: Identifiable {
        case distribution
        case completion
        case trend
        
        var id: Int {
            switch self {
            case .distribution: return 1
            case .completion: return 2
            case .trend: return 3
            }
        }
        
        var title: String {
            switch self {
            case .distribution:
                return LocalizedString("Task Distribution Details", comment: "Detail view title for distribution")
            case .completion:
                return LocalizedString("Task Completion Details", comment: "Detail view title for completion")
            case .trend:
                return LocalizedString("Task Trend Details", comment: "Detail view title for trend")
            }
        }
    }
    
    // 计算趋势数据
    private var trendData: [DayTrendData] {
        let calendar = Calendar.current
        let now = Date()
        // 使用今天的结束时间作为结束日期
        let endDate = calendar.date(bySettingHour: 23, minute: 59, second: 59, of: now)!
        let startDate = calendar.date(byAdding: .day, value: -29, to: calendar.startOfDay(for: now))! // 获取30天数据
        var data: [DayTrendData] = []
        
        // 按日期分组任务
        let dailyTasks = Dictionary(grouping: tasks) { task in
            calendar.startOfDay(for: task.dateCreated)
        }
        
        // 生成每天的数据
        var currentDate = startDate
        while currentDate <= endDate {
            let dayStart = calendar.startOfDay(for: currentDate)
            
            // 获取当天的任务
            let dayTasks = dailyTasks[dayStart] ?? []
            
            // 统计每个象限的任务数
            var quadrantCounts: [Int: Int] = [:]
            for quadrant in 1...4 {
                let count = dayTasks.filter { $0.quadrant == quadrant }.count
                if count > 0 {
                    quadrantCounts[quadrant] = count
                }
            }
            
            data.append(DayTrendData(date: currentDate, taskCounts: quadrantCounts))
            currentDate = calendar.date(byAdding: .day, value: 1, to: currentDate)!
        }
        
        return data
    }
    
    private var quadrantDistributions: [QuadrantDistribution] {
        let totalTasks = tasks.count
        var distributions: [QuadrantDistribution] = []
        
        for quadrant in 1...4 {
            let count = tasks.filter { $0.quadrant == quadrant }.count
            distributions.append(QuadrantDistribution(quadrant: quadrant, count: count, total: totalTasks))
        }
        
        return distributions
    }
    
    private var completionRates: [CompletionRate] {
        var rates: [CompletionRate] = []
        
        // Overall completion rate
        let totalCompleted = tasks.filter { $0.isCompleted }.count
        rates.append(CompletionRate(quadrant: 0, completed: totalCompleted, total: tasks.count))
        
        // Per quadrant completion rates
        for quadrant in 1...4 {
            let quadrantTasks = tasks.filter { $0.quadrant == quadrant }
            let completed = quadrantTasks.filter { $0.isCompleted }.count
            rates.append(CompletionRate(quadrant: quadrant, completed: completed, total: quadrantTasks.count))
        }
        
        return rates
    }
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 12) {
                    // 第一行：分布图和完成率图
                    HStack(spacing: 12) {
                        // Distribution Chart
                        QuadrantPieChartView(distributions: quadrantDistributions, isCompact: true)
                            .frame(height: 160)
                            .frame(maxWidth: .infinity)
                            .background(Color(.systemBackground))
                            .cornerRadius(12)
                            .shadow(color: Color.black.opacity(0.1), radius: 5, x: 0, y: 2)
                            .onTapGesture {
                                withAnimation(.spring()) {
                                    expandedChart = .distribution
                                }
                            }
                        
                        // Completion Chart
                        CompletionRingView(completionRates: completionRates, isCompact: true)
                            .frame(height: 160)
                            .frame(maxWidth: .infinity)
                            .background(Color(.systemBackground))
                            .cornerRadius(12)
                            .shadow(color: Color.black.opacity(0.1), radius: 5, x: 0, y: 2)
                            .onTapGesture {
                                withAnimation(.spring()) {
                                    expandedChart = .completion
                                }
                            }
                    }
                    
                    // 第二行：趋势图（占满宽度）
                    TimeTrendView(data: trendData, isCompact: true)
                        .frame(height: 120)  // 调整高度以适应一行
                        .frame(maxWidth: .infinity)
                        .background(Color(.systemBackground))
                        .cornerRadius(12)
                        .shadow(color: Color.black.opacity(0.1), radius: 5, x: 0, y: 2)
                        .onTapGesture {
                            withAnimation(.spring()) {
                                expandedChart = .trend
                            }
                        }
                }
                .padding(.horizontal, 12)
                .padding(.top, -20)
            }
            .background(Color(.systemGroupedBackground))
            .toolbar {
                ToolbarItem(placement: .principal) {
                    Text(LocalizedString("Analytics", comment: "Navigation title"))
                        .font(.headline)
                }
            }
            .sheet(item: $expandedChart) { chart in
                NavigationView {
                    ScrollView {
                        VStack {
                            switch chart {
                            case .distribution:
                                QuadrantPieChartView(distributions: quadrantDistributions, isCompact: false)
                                    .frame(height: 300)
                            case .completion:
                                CompletionRingView(completionRates: completionRates, isCompact: false)
                                    .frame(height: 400)
                            case .trend:
                                TimeTrendView(data: trendData, isCompact: false)
                                    .frame(height: 400)
                            }
                        }
                        .padding()
                    }
                    .navigationTitle(chart.title)
                    .navigationBarTitleDisplayMode(.inline)
                    .toolbar {
                        ToolbarItem(placement: .navigationBarTrailing) {
                            Button {
                                expandedChart = nil
                            } label: {
                                Image(systemName: "xmark.circle.fill")
                                    .foregroundColor(.secondary)
                            }
                        }
                    }
                }
            }
        }
    }
} 