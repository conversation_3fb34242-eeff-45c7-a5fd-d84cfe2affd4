import SwiftUI
import SwiftData

struct QuadrantPieChartView: View {
    let distributions: [QuadrantDistribution]
    let isCompact: Bool
    @State private var selectedQuadrant: Int?
    
    private var totalTasks: Int {
        distributions.reduce(0) { $0 + $1.count }
    }
    
    private func startAngle(for dist: QuadrantDistribution) -> <PERSON><PERSON> {
        let precedingTotal = distributions
            .prefix(while: { $0.id != dist.id })
            .reduce(0) { $0 + $1.count }
        return .degrees(Double(precedingTotal) / Double(max(1, totalTasks)) * 360)
    }
    
    private func endAngle(for dist: QuadrantDistribution) -> <PERSON>le {
        let precedingTotal = distributions
            .prefix(while: { $0.id != dist.id })
            .reduce(0) { $0 + $1.count }
        return .degrees(Double(precedingTotal + dist.count) / Double(max(1, totalTasks)) * 360)
    }
    
    var body: some View {
        if isCompact {
            compactView
        } else {
            expandedView
        }
    }
    
    private var compactView: some View {
        VStack(spacing: 8) {
            Text(LocalizedString("Task Distribution", comment: "Chart title"))
                .font(.subheadline)
                .foregroundColor(.secondary)
            
            ZStack {
                ForEach(distributions) { dist in
                    PieSliceShape(
                        startAngle: startAngle(for: dist),
                        endAngle: endAngle(for: dist)
                    )
                    .fill(dist.color)
                }
                
                Circle()
                    .fill(Color(.systemBackground))
                    .frame(width: 60, height: 60)
                
                Text("\(totalTasks)")
                    .font(.system(size: 20, weight: .bold))
                    .foregroundColor(.primary)
            }
            .frame(height: 100)
        }
    }
    
    private var expandedView: some View {
        VStack(spacing: 20) {
            Color.clear
                .frame(height: 140)
            
            ZStack {
                ForEach(distributions) { dist in
                    let isSelected = selectedQuadrant == dist.quadrant
                    PieSliceShape(
                        startAngle: startAngle(for: dist),
                        endAngle: endAngle(for: dist)
                    )
                    .fill(dist.color.opacity(isSelected ? 1 : 0.7))
                    .scaleEffect(isSelected ? 1.05 : 1.0)
                    .animation(.spring(), value: isSelected)
                }
                
                Circle()
                    .fill(Color(.systemBackground))
                    .frame(width: 140, height: 140)
                
                VStack(spacing: 4) {
                    Text("\(totalTasks)")
                        .font(.system(size: 32, weight: .bold))
                        .foregroundColor(.primary)
                    Text(LocalizedString("Tasks", comment: "Task count label"))
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            .frame(height: 300)
            .contentShape(Rectangle())
            
            VStack(spacing: 12) {
                ForEach(distributions) { dist in
                    HStack(spacing: 12) {
                        Circle()
                            .fill(dist.color)
                            .frame(width: 12, height: 12)
                        
                        Text(dist.title)
                            .font(.subheadline)
                        
                        Spacer()
                        
                        Text("\(dist.count)")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                        
                        Text("(\(String(format: "%.1f%%", dist.percentage)))")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    .contentShape(Rectangle())
                    .onTapGesture {
                        withAnimation(.spring()) {
                            selectedQuadrant = selectedQuadrant == dist.quadrant ? nil : dist.quadrant
                        }
                    }
                }
            }
            .padding(.horizontal)
        }
        .padding()
    }
}

struct PieSliceShape: Shape {
    var startAngle: Angle
    var endAngle: Angle
    
    func path(in rect: CGRect) -> Path {
        var path = Path()
        let center = CGPoint(x: rect.midX, y: rect.midY)
        let radius = min(rect.width, rect.height) / 2
        
        path.move(to: center)
        path.addArc(
            center: center,
            radius: radius,
            startAngle: startAngle - .degrees(90),
            endAngle: endAngle - .degrees(90),
            clockwise: false
        )
        path.closeSubpath()
        
        return path
    }
} 