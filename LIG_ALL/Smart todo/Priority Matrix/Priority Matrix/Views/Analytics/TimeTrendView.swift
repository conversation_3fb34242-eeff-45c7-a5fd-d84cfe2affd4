import SwiftUI
import SwiftData
import Charts

struct TimeTrendView: View {
    let data: [DayTrendData]
    let isCompact: Bool
    @State private var selectedStartDate: Date
    @State private var hiddenQuadrants: Set<Int> = []
    
    init(data: [DayTrendData], isCompact: Bool) {
        self.data = data
        self.isCompact = isCompact
        // 默认选择最近7天的起始日期
        let calendar = Calendar.current
        let endDate = calendar.startOfDay(for: Date())  // 使用今天的开始时间
        let defaultStartDate = calendar.date(byAdding: .day, value: -6, to: endDate)!
        _selectedStartDate = State(initialValue: defaultStartDate)
    }
    
    // 获取7天窗口的数据
    private var windowData: [DayTrendData] {
        let calendar = Calendar.current
        let endDate = calendar.date(byAdding: .day, value: 6, to: selectedStartDate)!
        let startOfDay = calendar.startOfDay(for: selectedStartDate)
        let endOfDay = calendar.date(byAdding: .day, value: 1, to: calendar.startOfDay(for: endDate))!
        
        return data.filter { point in
            let pointStartOfDay = calendar.startOfDay(for: point.date)
            return pointStartOfDay >= startOfDay && pointStartOfDay < endOfDay
        }
    }
    
    // 计算可选择的日期范围
    private var dateRange: ClosedRange<Date> {
        let calendar = Calendar.current
        let endDate = calendar.startOfDay(for: Date())  // 使用今天的开始时间
        let startDate = calendar.date(byAdding: .day, value: -6, to: endDate)!
        return startDate...endDate
    }
    
    var body: some View {
        VStack(spacing: 8) {
            if !isCompact {
                datePicker
            }
            
            chartView
                .frame(height: isCompact ? 100 : 200)
            
            if !isCompact {
                legendView
            }
        }
        .padding(.vertical, 8)
    }
    
    private var datePicker: some View {
        DatePicker(
            LocalizedString("Start Date", comment: "Date picker label"),
            selection: $selectedStartDate,
            in: dateRange,
            displayedComponents: .date
        )
        .datePickerStyle(.compact)
        .padding(.horizontal)
    }
    
    private var chartView: some View {
        Chart {
            ForEach(Array(1...4), id: \.self) { quadrant in
                if !hiddenQuadrants.contains(quadrant) {
                    ForEach(windowData, id: \.date) { point in
                        LineMark(
                            x: .value(LocalizedString("Date", comment: "Chart x-axis label"), point.date),
                            y: .value(LocalizedString("Tasks", comment: "Chart y-axis label"), point.taskCounts[quadrant] ?? 0)
                        )
                        .foregroundStyle(by: .value(LocalizedString("Quadrant", comment: "Chart legend label"), LocalizedString("Q\(quadrant)", comment: "Quadrant title")))
                        .lineStyle(StrokeStyle(lineWidth: 2))
                        .interpolationMethod(.catmullRom)
                        
                        PointMark(
                            x: .value(LocalizedString("Date", comment: "Chart x-axis label"), point.date),
                            y: .value(LocalizedString("Tasks", comment: "Chart y-axis label"), point.taskCounts[quadrant] ?? 0)
                        )
                        .foregroundStyle(by: .value(LocalizedString("Quadrant", comment: "Chart legend label"), LocalizedString("Q\(quadrant)", comment: "Quadrant title")))
                    }
                }
            }
        }
        .chartForegroundStyleScale([
            LocalizedString("Q1", comment: "Quadrant 1"): quadrantColor(1),
            LocalizedString("Q2", comment: "Quadrant 2"): quadrantColor(2),
            LocalizedString("Q3", comment: "Quadrant 3"): quadrantColor(3),
            LocalizedString("Q4", comment: "Quadrant 4"): quadrantColor(4)
        ])
        .chartXAxis {
            AxisMarks(values: .automatic) { value in
                if let date = value.as(Date.self) {
                    AxisValueLabel {
                        Text(date.shortDayString)
                            .font(.caption2)
                    }
                }
            }
        }
        .chartYAxis {
            AxisMarks(position: .leading)
        }
        .padding(.horizontal, 16)
    }
    
    private var legendView: some View {
        HStack(spacing: 16) {
            ForEach(Array(1...4), id: \.self) { quadrant in
                Button {
                    withAnimation {
                        if hiddenQuadrants.contains(quadrant) {
                            hiddenQuadrants.remove(quadrant)
                        } else {
                            hiddenQuadrants.insert(quadrant)
                        }
                    }
                } label: {
                    HStack(spacing: 4) {
                        Circle()
                            .fill(quadrantColor(quadrant))
                            .frame(width: 8, height: 8)
                        Text(LocalizedString("Q\(quadrant)", comment: "Quadrant title"))
                            .font(.caption)
                            .foregroundColor(hiddenQuadrants.contains(quadrant) ? .secondary : .primary)
                    }
                }
            }
        }
    }
    
    private func quadrantColor(_ quadrant: Int) -> Color {
        switch quadrant {
        case 1: return Color(hex: "FF5D7B")
        case 2: return Color(hex: "4B8BF4")
        case 3: return Color(hex: "8C8C8C")
        case 4: return Color(hex: "CCCCCC")
        default: return .gray
        }
    }
}

struct DayTrendData: Identifiable {
    let id = UUID()
    let date: Date
    let taskCounts: [Int: Int]  // [quadrant: count]
}

private extension Date {
    var shortDayString: String {
        let formatter = DateFormatter()
        formatter.dateFormat = "d"
        return formatter.string(from: self)
    }
}

#Preview {
    let sampleData = generateSampleData()
    return VStack(spacing: 20) {
        VStack(alignment: .leading) {
            Text("完整趋势图")
                .font(.headline)
            Text("包含时间范围选择和图例，用于详细分析")
                .font(.caption)
                .foregroundColor(.secondary)
            TimeTrendView(data: sampleData, isCompact: false)
                .padding()
                .background(Color(.systemBackground))
        }
        
        VStack(alignment: .leading) {
            Text("简洁趋势图")
                .font(.headline)
            Text("用于仪表盘展示，只显示近期趋势")
                .font(.caption)
                .foregroundColor(.secondary)
            TimeTrendView(data: sampleData, isCompact: true)
                .padding()
                .background(Color(.systemBackground))
        }
    }
    .padding()
}

// Helper function to generate sample data
private func generateSampleData() -> [DayTrendData] {
    let calendar = Calendar.current
    let endDate = Date()
    var data: [DayTrendData] = []
    
    // Generate 30 days of sample data
    for day in 0..<30 {
        let date = calendar.date(byAdding: .day, value: -day, to: endDate)!
        
        // Create random task counts for each quadrant
        var counts: [Int: Int] = [:]
        for quadrant in 1...4 {
            // More realistic random distribution
            let baseCount = quadrant == 2 ? 3 : (quadrant == 1 ? 2 : 1) // Q2 tasks typically more numerous
            let randomVariation = Int.random(in: 0...2)
            counts[quadrant] = max(0, baseCount + randomVariation - (day % 3)) // Add some variation over time
        }
        
        data.append(DayTrendData(date: date, taskCounts: counts))
    }
    
    return data.reversed() // Return in chronological order
} 