import SwiftUI
import SwiftData

struct AddTaskSheet: View {
    @Environment(\.modelContext) private var modelContext
    @Environment(\.dismiss) private var dismiss
    @FocusState private var isFocused: Bool
    
    // Keyboard states
    @State private var keyboardHeight: CGFloat = 0
    @State private var isKeyboardVisible = false
    
    // AI analysis states
    @State private var isAnalyzing: Bool = false
    @Bindable private var draft = TaskDraft.shared
    
    var body: some View {
        NavigationView {
            ZStack(alignment: .bottom) {
                ScrollView {
                    VStack(spacing: 16) {
                        // Task Basic Info Card
                        VStack(alignment: .leading, spacing: 12) {
                            Text(LocalizedString("Task Details", comment: "Task details section header"))
                                .font(.headline)
                                .foregroundColor(.secondary)
                            
                            TextField(LocalizedString("Title", comment: "Task title"), text: $draft.title)
                                .padding(10)
                                .background(Color(.secondarySystemBackground))
                                .cornerRadius(8)
                                .overlay(
                                    RoundedRectangle(cornerRadius: 8)
                                        .stroke(Color(.separator), lineWidth: 1)
                                )
                            
                            TextField(LocalizedString("Description", comment: "Task description"), text: $draft.description, axis: .vertical)
                                .lineLimit(3...3)
                                .padding(10)
                                .frame(height: 80)
                                .background(Color(.secondarySystemBackground))
                                .cornerRadius(8)
                                .overlay(
                                    RoundedRectangle(cornerRadius: 8)
                                        .stroke(Color(.separator), lineWidth: 1)
                                )
                            
                            // Important Toggle with Icon
                            HStack(spacing: 6) {
                                Image(systemName: "star.fill")
                                    .foregroundColor(.red)
                                    .frame(width: 20)
                                Toggle(isOn: $draft.isImportant) {
                                    Text(LocalizedString("Important", comment: "Important toggle"))
                                }
                            }
                            
                            // Urgent Toggle with Icon
                            HStack(spacing: 6) {
                                Image(systemName: "timer")
                                    .foregroundColor(.orange)
                                    .frame(width: 20)
                                Toggle(isOn: $draft.isUrgent) {
                                    Text(LocalizedString("Urgent", comment: "Urgent toggle"))
                                }
                            }
                            
                            // Due Date Section
                            HStack(spacing: 6) {
                                Image(systemName: "calendar")
                                    .foregroundColor(.blue)
                                    .frame(width: 20)
                                Toggle(isOn: $draft.hasDueDate) {
                                    Text(LocalizedString("Due Date", comment: "Due date toggle"))
                                }
                            }
                            
                            if draft.hasDueDate {
                                DatePicker(
                                    "",
                                    selection: Binding(
                                        get: { draft.dueDate ?? Date() },
                                        set: { draft.dueDate = $0 }
                                    ),
                                    in: Date()...,
                                    displayedComponents: [.date, .hourAndMinute]
                                )
                                .datePickerStyle(.compact)
                                .labelsHidden()
                            }
                        }
                        .padding(16)
                        .background(Color(.tertiarySystemBackground))
                        .cornerRadius(12)
                        .shadow(color: Color.black.opacity(0.1), radius: 6, x: 0, y: 0)
                        
                        // AI Analysis Results
                        if !draft.taskInput.isEmpty {
                            if isAnalyzing {
                                VStack(spacing: 8) {
                                    ProgressView()
                                        .progressViewStyle(.circular)
                                    Text(LocalizedString("Analyzing...", comment: "Analysis in progress"))
                                        .font(.subheadline)
                                        .foregroundColor(.secondary)
                                }
                                .frame(maxWidth: .infinity)
                                .padding(.vertical, 32)
                                .background(Color(.secondarySystemBackground))
                                .cornerRadius(12)
                            } else if let analysis = draft.aiResponse {
                                // Analysis Card
                                VStack(alignment: .leading, spacing: 12) {
                                    HStack {
                                        Image(systemName: "brain")
                                            .foregroundColor(.white)
                                        Text(LocalizedString("Analysis", comment: "Analysis subsection title"))
                                            .font(.headline)
                                            .foregroundColor(.white)
                                    }
                                    Text(analysis.explanation)
                                        .font(.body)
                                        .foregroundColor(.white)
                                }
                                .padding(16)
                                .frame(maxWidth: .infinity, alignment: .leading)
                                .background(Color.analysisCard)
                                .cornerRadius(12)
                                
                                // Hidden Value Card
                                VStack(alignment: .leading, spacing: 12) {
                                    HStack {
                                        Image(systemName: "sparkles")
                                            .foregroundColor(.white)
                                        Text(LocalizedString("Hidden Value", comment: "Hidden value subsection title"))
                                            .font(.headline)
                                            .foregroundColor(.white)
                                    }
                                    Text(analysis.hiddenValue)
                                        .font(.body)
                                        .foregroundColor(.white)
                                }
                                .padding(16)
                                .frame(maxWidth: .infinity, alignment: .leading)
                                .background(Color.hiddenValueCard)
                                .cornerRadius(12)
                                
                                // Suggestions Card
                                VStack(alignment: .leading, spacing: 12) {
                                    HStack {
                                        Image(systemName: "lightbulb")
                                            .foregroundColor(.white)
                                        Text(LocalizedString("Suggestions", comment: "Suggestions subsection title"))
                                            .font(.headline)
                                            .foregroundColor(.white)
                                    }
                                    ForEach(analysis.suggestions, id: \.self) { suggestion in
                                        HStack(alignment: .top, spacing: 8) {
                                            Circle()
                                                .fill(Color.white.opacity(0.8))
                                                .frame(width: 4, height: 4)
                                                .padding(.top, 8)
                                            Text(suggestion)
                                                .font(.body)
                                                .foregroundColor(.white)
                                                .fixedSize(horizontal: false, vertical: true)
                                        }
                                    }
                                }
                                .padding(16)
                                .frame(maxWidth: .infinity, alignment: .leading)
                                .background(Color.suggestionsCard)
                                .cornerRadius(12)
                            }
                        }
                    }
                    .padding(16)
                }
                .scrollDismissesKeyboard(.interactively)
                .dismissKeyboardOnTap()
                .animation(.spring(response: 0.3, dampingFraction: 0.8), value: keyboardHeight)
                .safeAreaInset(edge: .bottom) {
                    VStack(spacing: 0) {
                        HStack(spacing: 12) {
                            // Background capsule
                            ZStack {
                                Capsule()
                                    .fill(Color(.secondarySystemBackground))
                                    .shadow(color: Color.black.opacity(0.1), radius: 3, x: 0, y: 1)
                                
                                HStack(spacing: 8) {
                                    // Voice input button
                                    Button(action: {
                                        ToastManager.shared.show(LocalizedString("Voice input coming soon", comment: "Feature in development message"))
                                    }) {
                                        Image(systemName: "mic.fill")
                                            .font(.title3)
                                            .foregroundColor(.gray)
                                    }
                                    .frame(width: 32, height: 32)
                                    
                                    // Text input
                                    ZStack(alignment: .leading) {
                                        if draft.taskInput.isEmpty {
                                            Text(LocalizedString("Describe your task, then tap", comment: "Task input placeholder"))
                                                .foregroundColor(.gray)
                                                .padding(.horizontal, 4)
                                                .lineLimit(1)
                                        }
                                        TextEditor(text: $draft.taskInput)
                                            .frame(minHeight: 36, maxHeight: 100)
                                            .scrollContentBackground(.hidden)
                                            .background(Color.clear)
                                            .focused($isFocused)
                                            .submitLabel(.done)
                                            .onSubmit {
                                                isFocused = false
                                            }
                                            .onChange(of: draft.taskInput) { _, newValue in
                                                // 如果文本超过最大高度，自动滚动到底部
                                                if calculateHeight(for: newValue) >= 110 {
                                                    withAnimation {
                                                        draft.taskInput = String(newValue.prefix(500))  // 限制最大字符数
                                                    }
                                                }
                                            }
                                    }
                                }
                                .padding(.horizontal, 8)
                            }
                            .frame(height: calculateHeight(for: draft.taskInput))
                            
                            // Magic wand button
                            Button(action: {
                                isFocused = false
                                if draft.taskInput.isEmpty {
                                    ToastManager.shared.show(LocalizedString("Please enter some text to analyze", comment: "Empty input message"))
                                } else {
                                    analyzeTask()
                                }
                            }) {
                                if isAnalyzing {
                                    ProgressView()
                                        .progressViewStyle(.circular)
                                        .tint(.white)
                                } else {
                                    Image(systemName: "wand.and.stars")
                                        .font(.title3)
                                        .foregroundColor(.white)
                                }
                            }
                            .frame(width: 44, height: 44)
                            .background(Color.blue.opacity(draft.taskInput.isEmpty ? 0.6 : 1))
                            .clipShape(Circle())
                            .shadow(color: Color.black.opacity(0.1), radius: 3, x: 0, y: 1)
                            .disabled(isAnalyzing)
                        }
                        .padding(.horizontal)
                        .padding(.vertical, 8)
                        .background(Color.clear)
                    }
                    .background(Color(.systemBackground).opacity(0.8))
                }
            }
            .navigationTitle(LocalizedString("New Task", comment: "Add task sheet title"))
            .navigationBarTitleDisplayMode(.inline)
            .onAppear {
                setupKeyboardNotifications()
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                    isFocused = true
                }
            }
            .onDisappear {
                removeKeyboardNotifications()
            }
            .toolbar {
                ToolbarItem(placement: .cancellationAction) {
                    Button(LocalizedString("Cancel", comment: "Cancel button")) {
                        isFocused = false
                        dismiss()
                    }
                }
                ToolbarItem(placement: .confirmationAction) {
                    Button(LocalizedString("Add", comment: "Add task button")) {
                        isFocused = false
                        addTask()
                    }
                    .disabled(draft.title.isEmpty)
                }
                
                ToolbarItem(placement: .navigationBarLeading) {
                    Menu {
                        Button(role: .destructive) {
                            draft.clear()
                        } label: {
                            Text(LocalizedString("Clear All", comment: "Clear all content"))
                        }
                        
                        Button(role: .destructive) {
                            draft.clearAnalysis()
                        } label: {
                            Text(LocalizedString("Clear Analysis", comment: "Clear analysis only"))
                        }
                    } label: {
                        Text(LocalizedString("Clear", comment: "Clear menu"))
                    }
                }
            }
        }
        .toast()
    }
    
    private func setupKeyboardNotifications() {
        NotificationCenter.default.addObserver(
            forName: UIResponder.keyboardWillShowNotification,
            object: nil,
            queue: .main
        ) { notification in
            guard let keyboardFrame = notification.userInfo?[UIResponder.keyboardFrameEndUserInfoKey] as? CGRect else { return }
            withAnimation(.spring(response: 0.3, dampingFraction: 0.8)) {
                keyboardHeight = keyboardFrame.height
                isKeyboardVisible = true
            }
        }
        
        NotificationCenter.default.addObserver(
            forName: UIResponder.keyboardWillHideNotification,
            object: nil,
            queue: .main
        ) { _ in
            withAnimation(.spring(response: 0.3, dampingFraction: 0.8)) {
                keyboardHeight = 0
                isKeyboardVisible = false
            }
        }
    }
    
    private func removeKeyboardNotifications() {
        NotificationCenter.default.removeObserver(self)
    }
    
    private func analyzeTask() {
        isAnalyzing = true
        
        Task { @MainActor in
            do {
                let service = try DeepSeekService()
                let analysis = try await service.analyzeTaskWithRetry(draft.taskInput)
                
                // Update UI with analysis results
                draft.aiResponse = analysis
                // Auto-fill form
                draft.title = analysis.title
                draft.description = analysis.taskDescription
                draft.isImportant = analysis.importance
                draft.isUrgent = analysis.urgency
                
                ToastManager.shared.show(LocalizedString("Analysis completed", comment: "Analysis success message"))
            } catch let error as AIServiceError {
                ToastManager.shared.show(error.localizedDescription)
                print("AI analysis failed: \(error)")
            } catch {
                ToastManager.shared.show(LocalizedString("Failed to analyze task", comment: "Analysis failure message"))
                print("Unexpected error: \(error)")
            }
            
            isAnalyzing = false
        }
    }
    
    private func addTask() {
        let task = UserTask(
            title: draft.title,
            description: draft.description,
            isImportant: draft.isImportant,
            isUrgent: draft.isUrgent,
            dueDate: draft.hasDueDate ? draft.dueDate : nil
        )
        if let analysis = draft.aiResponse {
            task.updateFromAIAnalysis(analysis)
        }
        modelContext.insert(task)
        
        // 设置任务通知
        Task {
            do {
                print("开始设置新任务的通知...")
                print("任务ID: \(task.id)")
                print("象限: \(task.quadrant)")
                if let dueDate = task.dueDate {
                    print("截止时间: \(dueDate.formatted())")
                } else {
                    print("无截止时间，将根据象限设置提醒")
                }
                
                try await NotificationManager.shared.scheduleNotification(for: task)
                print("✅ 新任务通知设置成功")
            } catch {
                print("❌ 设置任务通知失败: \(error.localizedDescription)")
            }
        }
        
        draft.clear()  // 清除所有内容
        dismiss()
    }
    
    // 添加计算高度的辅助函数
    private func calculateHeight(for text: String) -> CGFloat {
        if text.isEmpty { return 44 }  // 默认高度
        
        // 计算换行符的数量
        let lineCount = text.components(separatedBy: "\n").count
        
        // 如果只有一行，返回默认高度
        if lineCount <= 1 { return 44 }
        
        // 每行额外增加20点高度，最大限制为110
        return min(44 + CGFloat(lineCount - 1) * 20, 110)
    }
}

struct FilterMenuView: View {
    @Bindable var filterManager: TaskFilterManager
    
    var body: some View {
        Menu {
            PriorityFilterMenu(filterManager: filterManager)
            DueDateFilterMenu(filterManager: filterManager)
        } label: {
            HStack(spacing: 4) {
                Text(LocalizedString("Filter", comment: "Filter menu button"))
                Image(systemName: "chevron.down")
                    .font(.caption)
            }
            .foregroundColor(.blue)
        }
    }
}

struct PriorityFilterMenu: View {
    @Bindable var filterManager: TaskFilterManager
    
    var body: some View {
        Menu(LocalizedString("Filter by Priority", comment: "Priority filter menu")) {
            Picker("Priority Filter", selection: $filterManager.selectedFilter) {
                Group {
                    Text(TaskFilter.all.description).tag(TaskFilter.all)
                    Text(TaskFilter.allImportant.description).tag(TaskFilter.allImportant)
                    Text(TaskFilter.allUrgent.description).tag(TaskFilter.allUrgent)
                }
                
                Divider()
                
                Group {
                    Text(TaskFilter.quadrant1.description).tag(TaskFilter.quadrant1)
                    Text(TaskFilter.quadrant2.description).tag(TaskFilter.quadrant2)
                    Text(TaskFilter.quadrant3.description).tag(TaskFilter.quadrant3)
                    Text(TaskFilter.quadrant4.description).tag(TaskFilter.quadrant4)
                }
            }
        }
    }
}

struct DueDateFilterMenu: View {
    @Bindable var filterManager: TaskFilterManager
    
    var body: some View {
        Menu(LocalizedString("Filter by Due Date", comment: "Due date filter menu")) {
            Button(role: filterManager.selectedDueDateFilter == nil ? .destructive : nil) {
                filterManager.selectedDueDateFilter = nil
            } label: {
                Text(LocalizedString("None", comment: "No filter option"))
            }
            
            Divider()
            
            ForEach([
                TaskFilter.overdue,
                TaskFilter.dueToday,
                TaskFilter.dueTomorrow,
                TaskFilter.dueThisWeek
            ], id: \.self) { filter in
                Button {
                    filterManager.selectedDueDateFilter = filter
                } label: {
                    if filterManager.selectedDueDateFilter == filter {
                        Label(filter.description, systemImage: "checkmark")
                    } else {
                        Text(filter.description)
                    }
                }
            }
        }
    }
}

struct SortMenuView: View {
    @Bindable var sortManager: TaskSortManager
    
    var body: some View {
        Menu {
            Picker("Sort", selection: $sortManager.selectedSortOption) {
                ForEach(TaskSortOption.allCases, id: \.self) { option in
                    Text(option.description).tag(option)
                }
            }
            
            Divider()
            
            Button {
                sortManager.sortOrder = sortManager.sortOrder == .ascending ? .descending : .ascending
            } label: {
                Label(
                    sortManager.sortOrder == .ascending ? LocalizedString("Sort Descending", comment: "Sort descending") : LocalizedString("Sort Ascending", comment: "Sort ascending"),
                    systemImage: sortManager.sortOrder == .ascending ? "arrow.up" : "arrow.down"
                )
            }
        } label: {
            HStack(spacing: 4) {
                Text(LocalizedString("Sort By", comment: "Sort menu button"))
                Image(systemName: "chevron.down")
                    .font(.caption)
            }
            .foregroundColor(.blue)
        }
    }
}

struct TaskListView: View {
    @Environment(\.modelContext) private var modelContext
    @Query(
        filter: #Predicate<UserTask> { task in
            !task.isCompleted
        },
        sort: \UserTask.dateCreated,
        order: .reverse
    ) private var tasks: [UserTask]
    @Bindable var filterManager: TaskFilterManager = .shared
    @Bindable var sortManager: TaskSortManager = .shared
    @State private var showingCompletedTasks = false
    
    // 过滤未完成的任务
    private var filteredTasks: [UserTask] {
        let uncompletedTasks = tasks.filter { !$0.isCompleted }
        return sortManager.sortTasks(filterManager.applyFilters(to: uncompletedTasks))
    }
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Filter chips
                ScrollView(.horizontal, showsIndicators: false) {
                    HStack(spacing: 8) {
                        // 主过滤器
                        if filterManager.selectedFilter != .all {
                            FilterChip(
                                text: filterManager.selectedFilter.shortDescription,
                                isSelected: true
                            ) {
                                filterManager.selectedFilter = .all
                            }
                        }
                        
                        // 截止日期过滤器
                        if let dueDateFilter = filterManager.selectedDueDateFilter {
                            FilterChip(
                                text: dueDateFilter.shortDescription,
                                isSelected: true
                            ) {
                                filterManager.selectedDueDateFilter = nil
                            }
                        }
                    }
                    .padding(.horizontal)
                }
                .padding(.vertical, 8)
                
                List {
                    ForEach(filteredTasks) { task in
                        NavigationLink(destination: TaskDetailView(task: task)) {
                            TaskRow(task: task, onImportantToggle: {
                                task.isImportant.toggle()
                            }, onUrgentToggle: {
                                task.isUrgent.toggle()
                            }, onRowTap: { })
                        }
                        .buttonStyle(.plain)
                        .swipeActions(edge: .leading) {
                            Button {
                                task.isCompleted.toggle()
                            } label: {
                                Label(LocalizedString("Complete", comment: "Complete task action"), systemImage: "checkmark.circle")
                            }
                            .tint(.green)
                        }
                        .swipeActions(edge: .trailing, allowsFullSwipe: true) {
                            Button(role: .destructive) {
                                modelContext.delete(task)
                            } label: {
                                Label(LocalizedString("Delete", comment: "Delete task action"), systemImage: "trash")
                            }
                        }
                    }
                }
                .overlay {
                    if filteredTasks.isEmpty {
                        ContentUnavailableView(
                            LocalizedString("No Tasks", comment: "Empty state title"),
                            systemImage: "list.bullet",
                            description: Text(LocalizedString("Add a task to get started", comment: "Empty state description"))
                        )
                    }
                }
            }
            .navigationTitle(LocalizedString("Tasks", comment: "Navigation title"))
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    FilterMenuView(filterManager: filterManager)
                }
                
                ToolbarItem(placement: .principal) {
                    Text(LocalizedString("Tasks", comment: "Navigation title"))
                        .font(.headline)
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    SortMenuView(sortManager: sortManager)
                }
            }
            .sheet(isPresented: $showingCompletedTasks) {
                CompletedTasksView()
            }
        }
        .background(Color(hex: "#F7F7F7"))
    }
}

#Preview {
    do {
        let config = ModelConfiguration(isStoredInMemoryOnly: true)
        let container = try ModelContainer(for: UserTask.self, configurations: config)
        
        // Add sample data
        let task1 = UserTask(title: "Complete project deadline", isImportant: true, isUrgent: true)
        let task2 = UserTask(title: "Learn SwiftUI", isImportant: true, isUrgent: false)
        container.mainContext.insert(task1)
        container.mainContext.insert(task2)
        
        return TaskListView()
            .modelContainer(container)
    } catch {
        return Text("Failed to create preview: \(error.localizedDescription)")
    }
}

struct TaskRow: View {
    let task: UserTask
    let onImportantToggle: () -> Void
    let onUrgentToggle: () -> Void
    let onRowTap: () -> Void
    
    var body: some View {
        HStack(alignment: .center, spacing: 12) {
            // 左侧状态图标
            if !task.isCompleted {
                VStack(alignment: .center, spacing: 4) {
                    // Important 图标
                    Label("", systemImage: "star.fill")
                        .labelStyle(.iconOnly)
                        .foregroundColor(task.isImportant ? .red : Color(.systemGray4))
                        .font(.system(size: 14))
                        .onTapGesture {
                            onImportantToggle()
                        }
                    
                    // Urgent 图标
                    Label("", systemImage: "timer")
                        .labelStyle(.iconOnly)
                        .foregroundColor(task.isUrgent ? .orange : Color(.systemGray4))
                        .font(.system(size: 14))
                        .onTapGesture {
                            onUrgentToggle()
                        }
                }
                .frame(width: 20)
            } else {
                Image(systemName: "checkmark.circle.fill")
                    .foregroundColor(.green)
                    .frame(width: 20)
            }
            
            // 标题和描述/日期
            VStack(alignment: .leading, spacing: 4) {
                Text(task.title)
                    .font(.headline)
                    .lineLimit(1)
                    .strikethrough(task.isCompleted)
                    .foregroundColor(task.isCompleted ? .gray : .primary)
                
                if let dueDate = task.dueDate {
                    // 有截止日期时显示日期
                    HStack(spacing: 4) {
                        Image(systemName: "calendar")
                            .font(.system(size: 12))
                            .foregroundColor(isOverdue(dueDate) ? .red : .blue)
                        Text(formatDueDate(dueDate))
                            .foregroundColor(isOverdue(dueDate) ? .red : .blue)
                    }
                    .font(.subheadline)
                } else if !task.taskDescription.isEmpty {
                    // 没有截止日期时显示描述
                    Text(task.taskDescription)
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                        .lineLimit(1)
                        .truncationMode(.tail)
                }
            }
            
            Spacer()
        }
        .frame(minHeight: 44)
    }
    
    private func formatDueDate(_ date: Date) -> String {
        let calendar = Calendar.current
        if calendar.isDateInToday(date) {
            return String(format: LocalizedString("Today %@", comment: "Today with time"), date.formatted(.dateTime.hour().minute()))
        } else if calendar.isDateInTomorrow(date) {
            return String(format: LocalizedString("Tomorrow %@", comment: "Tomorrow with time"), date.formatted(.dateTime.hour().minute()))
        } else {
            return date.formatted(.dateTime.month().day().hour().minute())
        }
    }
    
    private func isOverdue(_ date: Date) -> Bool {
        return date < Date()
    }
}

