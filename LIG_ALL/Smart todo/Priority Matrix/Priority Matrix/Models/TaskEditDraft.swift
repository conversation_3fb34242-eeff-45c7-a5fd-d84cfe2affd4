import Foundation

@Observable
final class TaskEditDraft {
    var title: String
    var description: String
    var isImportant: Bool
    var isUrgent: Bool
    var dueDate: Date?
    var hasDueDate: Bool
    
    init(from task: UserTask) {
        self.title = task.title
        self.description = task.taskDescription
        self.isImportant = task.isImportant
        self.isUrgent = task.isUrgent
        self.dueDate = task.dueDate
        self.hasDueDate = task.dueDate != nil
    }
    
    func applyTo(_ task: UserTask) {
        task.title = title
        task.taskDescription = description
        task.isImportant = isImportant
        task.isUrgent = isUrgent
        task.dueDate = hasDueDate ? dueDate : nil
    }
} 