import Foundation
import SwiftData

@Model
@preconcurrency
final class UserTask {
    var title: String
    var taskDescription: String
    var isImportant: Bool
    var isUrgent: Bool
    var isCompleted: Bool {
        didSet {
            if isCompleted {
                // 任务完成时取消通知
                Task { @MainActor in
                    await NotificationManager.shared.cancelNotification(for: self)
                }
            } else if oldValue && !isCompleted {
                // 任务从完成变为未完成时，重新设置通知
                Task { @MainActor in
                    try? await NotificationManager.shared.scheduleNotification(for: self)
                }
            }
        }
    }
    var dateCreated: Date
    var dueDate: Date?  // 添加可选的截止日期
    
    // AI 分析结果
    var aiAnalysis: AITaskResponse?
    
    init(title: String, description: String = "", isImportant: Bool = false, isUrgent: Bool = false, dueDate: Date? = nil) {
        self.title = title
        self.taskDescription = description
        self.isImportant = isImportant
        self.isUrgent = isUrgent
        self.isCompleted = false
        self.dateCreated = Date()
        self.dueDate = dueDate
    }
    
    var quadrant: Int {
        switch (isImportant, isUrgent) {
        case (true, true):   return 1 // Important & Urgent
        case (true, false):  return 2 // Important & Not Urgent
        case (false, true):  return 3 // Not Important & Urgent
        case (false, false): return 4 // Not Important & Not Urgent
        }
    }
    
    /// 根据 AI 分析更新任务
    @MainActor func updateFromAIAnalysis(_ analysis: AITaskResponse) {
        self.title = analysis.title
        self.taskDescription = analysis.taskDescription
        self.isImportant = analysis.importance
        self.isUrgent = analysis.urgency
        self.aiAnalysis = analysis
    }
    
    // 在对象被删除前自动取消通知
    @MainActor
    func delete() {
        Task { @MainActor in
            await NotificationManager.shared.cancelNotification(for: self)
        }
    }
} 
