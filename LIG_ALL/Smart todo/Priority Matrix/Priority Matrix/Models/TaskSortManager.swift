import SwiftUI
import Observation

enum TaskSortOption: String, CaseIterable {
    case dateCreated = "Date Created"
    case title = "Title"
    case quadrant = "Quadrant"
    case dueDate = "Due Date"
    
    var description: String {
        switch self {
        case .dateCreated:
            return LocalizedString("Date Created", comment: "Sort option")
        case .title:
            return LocalizedString("Title", comment: "Sort option")
        case .quadrant:
            return LocalizedString("Quadrant", comment: "Sort option")
        case .dueDate:
            return LocalizedString("Due Date", comment: "Sort option")
        }
    }
}

enum SortOrder {
    case ascending
    case descending
}

@Observable final class TaskSortManager {
    var selectedSortOption: TaskSortOption = .dateCreated
    var sortOrder: SortOrder = .descending
    
    static let shared: TaskSortManager = .init()
    
    init() {}
    
    func sortTasks(_ tasks: [UserTask]) -> [UserTask] {
        switch selectedSortOption {
        case .dateCreated:
            return tasks.sorted { 
                sortOrder == .ascending ? 
                    $0.dateCreated < $1.dateCreated :
                    $0.dateCreated > $1.dateCreated 
            }
        case .title:
            return tasks.sorted { 
                sortOrder == .ascending ? 
                    $0.title < $1.title :
                    $0.title > $1.title 
            }
        case .quadrant:
            return tasks.sorted { 
                sortOrder == .ascending ? 
                    $0.quadrant < $1.quadrant :
                    $0.quadrant > $1.quadrant 
            }
        case .dueDate:
            return tasks.sorted { task1, task2 in
                // 将没有截止日期的任务放在最后
                switch (task1.dueDate, task2.dueDate) {
                case (nil, nil):
                    return false  // 都没有截止日期，保持原顺序
                case (nil, _):
                    return sortOrder == .ascending  // 没有截止日期的放在后面
                case (_, nil):
                    return sortOrder == .descending  // 没有截止日期的放在后面
                case (let date1?, let date2?):
                    return sortOrder == .ascending ? 
                        date1 < date2 :
                        date1 > date2
                }
            }
        }
    }
} 
