# Models

## Core Models

### UserTask
- 核心任务数据模型
- 管理任务的基本属性（标题、描述、重要性、紧急性）
- 存储 AI 分析结果（建议、隐藏价值等）
- 支持任务状态追踪（完成状态、创建时间）

### AITaskResponse
- AI 分析结果的数据结构
- 包含任务分析、建议和隐藏价值
- 用于 AI 服务和任务模型之间的数据转换

## State Management

### TaskFilterManager
- 任务过滤管理器（单例）
- 控制任务列表的显示过滤
- 支持按象限和状态筛选

### TaskSortManager
- 任务排序管理器（单例）
- 管理任务列表的排序逻辑
- 支持多种排序选项和排序方向

### ToastManager
- 全局消息提示管理器（单例）
- 处理应用内的消息提示显示
- 控制提示的显示时间和位置

# Models Examples

## UserTask Example
```swift
// 创建新任务
let task = UserTask(title: "完成项目文档", 
                   description: "更新项目的技术文档和使用说明",
                   isImportant: true,
                   isUrgent: true)

// 更新任务状态
task.isCompleted = true

// 获取任务象限
let quadrant = task.quadrant // 第一象限 (重要且紧急)

// 使用 AI 分析结果
if let analysis = task.aiAnalysis {
    print("AI 建议: \(analysis.suggestions)")
    print("隐藏价值: \(analysis.hiddenValue)")
}
```

## AITaskResponse Example
```swift
// AI 分析结果结构
let analysisResult = AITaskResponse(
    explanation: "这是一个高优先级的文档任务",
    suggestions: ["建议分解成小任务", "设置截止日期"],
    hiddenValue: "完善的文档有助于团队协作"
)

// 解析 AI 返回的 JSON
let jsonData = // ... API 返回的 JSON 数据
let response = try JSONDecoder().decode(AITaskResponse.self, from: jsonData)
```

## TaskFilterManager Example
```swift
// 设置过滤条件
TaskFilterManager.shared.selectedQuadrant = .first
TaskFilterManager.shared.showCompletedTasks = false

// 应用过滤器
let filteredTasks = tasks.filter { task in
    TaskFilterManager.shared.shouldShow(task)
}
```

## ToastManager Example
```swift
// 显示普通消息
ToastManager.shared.show("任务已创建")

// 显示较长时间的消息
ToastManager.shared.show("分析完成", duration: 5.0)
``` 