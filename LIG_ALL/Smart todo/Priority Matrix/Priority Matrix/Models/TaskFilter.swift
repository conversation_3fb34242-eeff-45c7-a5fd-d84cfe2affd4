import Foundation

enum TaskFilter: Hashable {
    case all
    case allImportant
    case allUrgent 
    case quadrant1  // 重要且紧急
    case quadrant2  // 重要不紧急
    case quadrant3  // 不重要但紧急
    case quadrant4  // 不重要不紧急
    case dueToday   // 今天到期
    case dueTomorrow // 明天到期
    case dueThisWeek // 本周到期
    case overdue    // 已逾期
    
    var description: String {
        switch self {
        case .all:
            return LocalizedString("All Tasks", comment: "Filter option")
        case .allImportant:
            return LocalizedString("All Important", comment: "Filter option")
        case .allUrgent:
            return LocalizedString("All Urgent", comment: "Filter option")
        case .quadrant1:
            return LocalizedString("Important & Urgent", comment: "Filter option")
        case .quadrant2:
            return LocalizedString("Important & Not Urgent", comment: "Filter option")
        case .quadrant3:
            return LocalizedString("Not Important & Urgent", comment: "Filter option")
        case .quadrant4:
            return LocalizedString("Not Important & Not Urgent", comment: "Filter option")
        case .dueToday:
            return LocalizedString("Due Today", comment: "Filter option")
        case .dueTomorrow:
            return LocalizedString("Due Tomorrow", comment: "Filter option")
        case .dueThisWeek:
            return LocalizedString("Due This Week", comment: "Filter option")
        case .overdue:
            return LocalizedString("Overdue", comment: "Filter option")
        }
    }
    
    var shortDescription: String {
        switch self {
        case .all:
            return LocalizedString("All", comment: "Short filter description")
        case .allImportant:
            return LocalizedString("Important", comment: "Short filter description")
        case .allUrgent:
            return LocalizedString("Urgent", comment: "Short filter description")
        case .quadrant1:
            return LocalizedString("Q1", comment: "Short filter description")
        case .quadrant2:
            return LocalizedString("Q2", comment: "Short filter description")
        case .quadrant3:
            return LocalizedString("Q3", comment: "Short filter description")
        case .quadrant4:
            return LocalizedString("Q4", comment: "Short filter description")
        case .dueToday:
            return LocalizedString("Today", comment: "Short filter description")
        case .dueTomorrow:
            return LocalizedString("Tomorrow", comment: "Short filter description")
        case .dueThisWeek:
            return LocalizedString("This Week", comment: "Short filter description")
        case .overdue:
            return LocalizedString("Overdue", comment: "Short filter description")
        }
    }
} 