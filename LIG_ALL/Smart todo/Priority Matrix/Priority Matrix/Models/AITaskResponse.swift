// Models/AITaskResponse.swift
import Foundation
import SwiftData

/// AI 任务分析响应模型
@Model
@preconcurrency
final class AITaskResponse: Codable {
    /// 分析后的任务标题
    var title: String
    
    /// 任务详细描述
    var taskDescription: String
    
    /// 重要性评估 (true = 重要)
    var importance: Bool
    
    /// 紧急性评估 (true = 紧急)
    var urgency: Bool
    
    /// 任务蕴含的潜在价值和机会
    var hiddenValue: String
    
    /// 任务处理建议 - 存储为JSON字符串
    private var suggestionsString: String
    
    /// AI 分析解释
    var explanation: String
    
    /// 任务处理建议的访问器
    var suggestions: [String] {
        get {
            guard let data = suggestionsString.data(using: .utf8),
                  let array = try? JSONDecoder().decode([String].self, from: data) else {
                return []
            }
            return array
        }
        set {
            guard let data = try? JSONEncoder().encode(newValue),
                  let json = String(data: data, encoding: .utf8) else {
                suggestionsString = "[]"
                return
            }
            suggestionsString = json
        }
    }
    
    init(title: String, description: String, importance: Bool, urgency: Bool, hiddenValue: String, suggestions: [String], explanation: String) {
        self.title = title
        self.taskDescription = description
        self.importance = importance
        self.urgency = urgency
        self.hiddenValue = hiddenValue
        self.explanation = explanation
        
        // 将suggestions数组转换为JSON字符串
        if let data = try? JSONEncoder().encode(suggestions),
           let json = String(data: data, encoding: .utf8) {
            self.suggestionsString = json
        } else {
            self.suggestionsString = "[]"
        }
    }
    
    enum CodingKeys: String, CodingKey {
        case title
        case taskDescription = "description"  // 映射到JSON中的"description"字段
        case importance, urgency, explanation
        case hiddenValue = "hidden_value"
        case suggestions
    }
    
    required init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        title = try container.decode(String.self, forKey: .title)
        taskDescription = try container.decode(String.self, forKey: .taskDescription)
        importance = try container.decode(Bool.self, forKey: .importance)
        urgency = try container.decode(Bool.self, forKey: .urgency)
        hiddenValue = try container.decode(String.self, forKey: .hiddenValue)
        explanation = try container.decode(String.self, forKey: .explanation)
        
        // 将解码的suggestions数组转换为JSON字符串
        let decodedSuggestions = try container.decode([String].self, forKey: .suggestions)
        if let data = try? JSONEncoder().encode(decodedSuggestions),
           let json = String(data: data, encoding: .utf8) {
            suggestionsString = json
        } else {
            suggestionsString = "[]"
        }
    }
    
    func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)
        try container.encode(title, forKey: .title)
        try container.encode(taskDescription, forKey: .taskDescription)
        try container.encode(importance, forKey: .importance)
        try container.encode(urgency, forKey: .urgency)
        try container.encode(hiddenValue, forKey: .hiddenValue)
        try container.encode(suggestions, forKey: .suggestions)
        try container.encode(explanation, forKey: .explanation)
    }
}