import SwiftUI

/// A singleton manager for showing toast messages
@MainActor
final class ToastManager: ObservableObject {
    /// Shared instance
    static let shared = ToastManager()
    
    /// Current toast message
    @Published private(set) var message: String?
    
    /// Whether the toast is currently showing
    @Published private(set) var isShowing = false
    
    /// Default toast duration
    private let defaultDuration: TimeInterval = 3.0
    
    /// Prevent external instantiation
    private init() {}
    
    /// Show a toast message
    /// - Parameters:
    ///   - message: The message to display
    ///   - duration: How long to show the message (defaults to 3 seconds)
    func show(_ message: String, duration: TimeInterval? = nil) {
        self.message = message
        self.isShowing = true
        
        Task { @MainActor in
            try await Task.sleep(nanoseconds: UInt64((duration ?? defaultDuration) * 1_000_000_000))
            withAnimation {
                self.isShowing = false
                self.message = nil
            }
        }
    }
} 