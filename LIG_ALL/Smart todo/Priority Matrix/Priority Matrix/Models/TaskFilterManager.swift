import SwiftUI
import Observation

@Observable final class TaskFilterManager {
    var selectedFilter: TaskFilter = .all
    var selectedDueDateFilter: TaskFilter? = nil  // 新增：截止日期过滤器
    
    static let shared: TaskFilterManager = .init()
    
    init() {}
    
    // 新增：重置所有过滤器
    func resetFilters() {
        selectedFilter = .all
        selectedDueDateFilter = nil
    }
    
    // 新增：检查过滤器是否为截止日期相关
    func isDueDateFilter(_ filter: TaskFilter) -> Bool {
        switch filter {
        case .dueToday, .dueTomorrow, .dueThisWeek, .overdue:
            return true
        default:
            return false
        }
    }
    
    // 新增：应用过滤器
    func applyFilters(to tasks: [UserTask]) -> [UserTask] {
        var filtered = tasks
        
        // 应用主过滤器
        filtered = switch selectedFilter {
        case .all:
            filtered
        case .allImportant:
            filtered.filter { $0.isImportant }
        case .allUrgent:
            filtered.filter { $0.isUrgent }
        case .quadrant1:
            filtered.filter { $0.isImportant && $0.isUrgent }
        case .quadrant2:
            filtered.filter { $0.isImportant && !$0.isUrgent }
        case .quadrant3:
            filtered.filter { !$0.isImportant && $0.isUrgent }
        case .quadrant4:
            filtered.filter { !$0.isImportant && !$0.isUrgent }
        case .dueToday, .dueTomorrow, .dueThisWeek, .overdue:
            filtered  // 这些将由 selectedDueDateFilter 处理
        }
        
        // 应用截止日期过滤器
        if let dueDateFilter = selectedDueDateFilter {
            filtered = switch dueDateFilter {
            case .dueToday:
                filtered.filter { task in
                    guard let dueDate = task.dueDate else { return false }
                    return Calendar.current.isDateInToday(dueDate)
                }
            case .dueTomorrow:
                filtered.filter { task in
                    guard let dueDate = task.dueDate else { return false }
                    return Calendar.current.isDateInTomorrow(dueDate)
                }
            case .dueThisWeek:
                filtered.filter { task in
                    guard let dueDate = task.dueDate else { return false }
                    let calendar = Calendar.current
                    let today = calendar.startOfDay(for: Date())
                    let weekEnd = calendar.date(byAdding: .weekOfYear, value: 1, to: today)!
                    return dueDate >= today && dueDate < weekEnd
                }
            case .overdue:
                filtered.filter { task in
                    guard let dueDate = task.dueDate else { return false }
                    return dueDate < Date()
                }
            default:
                filtered
            }
        }
        
        return filtered
    }
} 