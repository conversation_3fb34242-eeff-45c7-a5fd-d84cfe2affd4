import SwiftUI

struct QuadrantDistribution: Identifiable {
    let quadrant: Int
    let count: Int
    let total: Int
    
    var id: Int { quadrant }
    
    var percentage: Double {
        total > 0 ? Double(count) / Double(total) * 100 : 0
    }
    
    var title: String {
        switch quadrant {
        case 1:
            return NSLocalizedString("Q1", comment: "Quadrant 1 title")
        case 2:
            return NSLocalizedString("Q2", comment: "Quadrant 2 title")
        case 3:
            return NSLocalizedString("Q3", comment: "Quadrant 3 title")
        case 4:
            return NSLocalizedString("Q4", comment: "Quadrant 4 title")
        default:
            return ""
        }
    }
    
    var color: Color {
        switch quadrant {
        case 1:
            return Color.quadrant1
        case 2:
            return Color.quadrant2
        case 3:
            return Color.quadrant3
        case 4:
            return Color.quadrant4
        default:
            return .clear
        }
    }
} 