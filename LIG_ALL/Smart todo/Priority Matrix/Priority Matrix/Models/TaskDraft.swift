import Foundation

@Observable
final class TaskDraft {
    // Basic task info
    var title: String = ""
    var description: String = ""
    var isImportant: Bool = false
    var isUrgent: Bool = false
    var dueDate: Date? = nil
    var hasDueDate: Bool = false
    
    // AI analysis content
    var taskInput: String = ""
    var aiResponse: AITaskResponse? = nil
    
    static let shared = TaskDraft()
    
    private init() {}
    
    func clear() {
        // Clear basic info
        title = ""
        description = ""
        isImportant = false
        isUrgent = false
        dueDate = nil
        hasDueDate = false
        
        // Clear AI analysis
        taskInput = ""
        aiResponse = nil
    }
    
    func clearAnalysis() {
        taskInput = ""
        aiResponse = nil
    }
} 