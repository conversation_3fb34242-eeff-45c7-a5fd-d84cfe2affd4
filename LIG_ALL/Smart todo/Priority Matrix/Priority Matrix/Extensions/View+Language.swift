import SwiftUI

extension View {
    func localizedText(_ key: String, comment: String = "") -> Text {
        Text(LocalizedString(key, comment: comment))
    }
    
    func localizedString(_ key: String, comment: String = "") -> String {
        LocalizedString(key, comment: comment)
    }
}

// 为了向后兼容，创建一个 wrapper view modifier
struct LocalizationUpdater: ViewModifier {
    @EnvironmentObject private var languageManager: LanguageManager
    
    func body(content: Content) -> some View {
        content
            .id(languageManager.currentLanguage) // 当语言改变时重新渲染视图
    }
}

extension View {
    func updateOnLanguageChange() -> some View {
        self.modifier(LocalizationUpdater())
    }
}