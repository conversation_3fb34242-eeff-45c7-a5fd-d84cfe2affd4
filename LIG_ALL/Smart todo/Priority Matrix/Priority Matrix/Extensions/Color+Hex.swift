import SwiftUI
import Foundation

/// Color extension that adds support for hex color codes and dark mode
/// Usage: Just import SwiftUI in your view files, no need to import this extension separately
/// Example: Color(hex: "#2C3E50")
extension Color {
    init(hex: String) {
        let hex = hex.trimmingCharacters(in: CharacterSet.alphanumerics.inverted)
        var int: UInt64 = 0
        Scanner(string: hex).scanHexInt64(&int)
        let a, r, g, b: UInt64
        switch hex.count {
        case 3: // RGB (12-bit)
            (a, r, g, b) = (255, (int >> 8) * 17, (int >> 4 & 0xF) * 17, (int & 0xF) * 17)
        case 6: // RGB (24-bit)
            (a, r, g, b) = (255, int >> 16, int >> 8 & 0xFF, int & 0xFF)
        case 8: // ARGB (32-bit)
            (a, r, g, b) = (int >> 24, int >> 16 & 0xFF, int >> 8 & 0xFF, int & 0xFF)
        default:
            (a, r, g, b) = (255, 0, 0, 0)
        }

        self.init(
            .sRGB,
            red: Double(r) / 255,
            green: Double(g) / 255,
            blue:  Double(b) / 255,
            opacity: Double(a) / 255
        )
    }
    
    /// Create a color that adapts to light and dark mode
    /// - Parameters:
    ///   - light: Hex color for light mode
    ///   - dark: Hex color for dark mode
    init(lightHex: String, darkHex: String) {
        self.init(uiColor: UIColor { traitCollection in
            let hex = traitCollection.userInterfaceStyle == .dark ? darkHex : lightHex
            return UIColor(Color(hex: hex))
        })
    }
    
    // Predefined adaptive brand colors
    static let analysisCard = Color(lightHex: "#4184F0", darkHex: "#2B5BA8")
    static let hiddenValueCard = Color(lightHex: "#A160C8", darkHex: "#6B4087")
    static let suggestionsCard = Color(lightHex: "#FBB347", darkHex: "#A67830")
    
    // Predefined quadrant colors with dark mode support
    static let quadrant1 = Color(lightHex: "#FF4765", darkHex: "#D43F58")  // 紧急重要
    static let quadrant2 = Color(lightHex: "#6BA2FF", darkHex: "#3A7AE6")  // 重要不紧急
    static let quadrant3 = Color(lightHex: "#A6A6A6", darkHex: "#707070")  // 紧急不重要
    static let quadrant4 = Color(lightHex: "#B3B3B3", darkHex: "#999999")  // 不紧急不重要
} 