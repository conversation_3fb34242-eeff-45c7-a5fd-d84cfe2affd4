// Tabs
"List" = "列表";
"Matrix" = "矩阵";
"Calendar" = "日历";
"Completed" = "已完成";
"Settings" = "设置";

// TaskListView
"Tasks" = "任务";
"Enter new task" = "输入新任务";
"Task description (optional)" = "任务描述（可选）";
"Important" = "重要";
"Urgent" = "紧急";
"Add Task" = "添加任务";
"Complete" = "完成";
"Delete" = "删除";
"No Tasks" = "没有任务";
"Add a task to get started" = "添加一个任务开始使用";

// Filter Options
"All" = "所有";
"All Tasks" = "所有任务";
"All Important" = "所有重要";
"All Urgent" = "所有紧急";
"Q1" = "象限一";
"Q2" = "象限二";
"Q3" = "象限三";
"Q4" = "象限四";

// Sort Options
"Sort By" = "排序方式";
"Date Created" = "创建日期";
"Title" = "标题";
"Quadrant" = "象限";
"Sort Ascending" = "升序排序";
"Sort Descending" = "降序排序";

// CompletedTasksView
"Completed Tasks" = "已完成任务";
"No Completed Tasks" = "没有已完成的任务";
"Tasks you complete will appear here" = "完成的任务将显示在这里";
"Uncomplete" = "取消完成";

// MatrixView
"Priority Matrix" = "优先级矩阵";
"Important & Urgent" = "重要\n紧急";
"Important & Not Urgent" = "重要\n不紧急";
"Not Important & Urgent" = "不重要\n紧急";
"Not Important & Not Urgent" = "不重要\n不紧急";

// CalendarView
"Calendar View - Coming Soon" = "日历视图 - 即将推出";

// SettingsView
"Language" = "语言";
"Follow System" = "跟随系统";
"Current System Language" = "当前系统语言";
"Change Language" = "更改语言";
"Change" = "更改";
"The language setting will be saved. Please restart the app manually to apply the changes." = "语言设置已保存。请手动重启应用以应用更改。";
"Language Changed" = "语言已更改";
"Please restart the app to apply language changes." = "请重启应用以应用语言更改。";
"OK" = "好的";
"The app needs to restart to change the language. Do you want to proceed?" = "需要重启应用来更改语言。是否继续？";
"Data Management" = "数据管理";
"Delete All Tasks" = "删除所有任务";
"About" = "关于";
"Version" = "版本";
"Cancel" = "取消";
"Are you sure you want to delete all tasks? This action cannot be undone." = "你确定要删除所有任务吗？此操作无法撤消。";

// Matrix Explanation View
"Done" = "完成";
"Eisenhower Matrix" = "艾森豪威尔矩阵";
"The Eisenhower Matrix (also known as Priority Matrix) is a time management tool developed by Dwight D. Eisenhower, the 34th President of the United States. As a successful military commander and politician, he was known for his exceptional ability to manage time and prioritize tasks. This tool helped him maintain efficiency and organization while handling complex military and political affairs." = "艾森豪威尔矩阵（也称为优先级矩阵）是由美国第34任总统德怀特·艾森豪威尔开发的一个时间管理工具。作为一位成功的军事统帅和政治家，他以出色的时间管理和任务优先级分配能力而闻名。这个工具帮助他在处理复杂的军事和政治事务时，始终保持高效和条理。";

"Matrix Principle" = "矩阵原理";
"The matrix evaluates and categorizes tasks based on two key dimensions:" = "该矩阵通过两个关键维度来评估和分类任务：";
"• Importance: Measures the task's impact on your goals, values, and long-term development" = "• 重要性：衡量任务对您的目标、价值观和长期发展的影响程度";
"• Urgency: Indicates time pressure, how quickly a response is needed" = "• 紧急性：表示任务的时间压力，即需要多快做出响应";

"Usage Tips" = "使用建议";
"1. Regularly check and update task status" = "1. 定期检查和更新任务状态";
"2. Prioritize tasks in the first quadrant" = "2. 优先处理第一象限的任务";
"3. Invest more time in the second quadrant" = "3. 投入更多时间在第二象限";
"4. Delegate or automate third quadrant tasks when possible" = "4. 尽可能委派或自动化第三象限的任务";
"5. Minimize or eliminate fourth quadrant activities" = "5. 减少或消除第四象限的活动";

"Quadrant Details" = "四个象限详解";
"Quadrant 1: Important & Urgent" = "第一象限：重要且紧急";
"Crises and problems that need immediate attention. Examples: approaching deadlines, urgent meetings, emergencies, health issues. These tasks typically create stress and should be minimized through better planning." = "需要立即处理的危机和问题。例如：截止日期临近的任务、紧急会议、突发危机、健康问题等。这些任务通常会带来压力，应该尽量通过好的规划来减少此类任务。";

"Quadrant 2: Important & Not Urgent" = "第二象限：重要但不紧急";
"Activities focused on long-term development. Examples: strategic planning, self-improvement, relationship building, health management, preventive maintenance. This quadrant deserves most of your time as it helps prevent crises and creates lasting value." = "关注长期发展的活动。例如：战略规划、自我提升、建立关系、健康管理、预防性维护等。这是最应该投入时间的象限，它能帮助你预防危机，创造持久价值。";

"Quadrant 3: Not Important & Urgent" = "第三象限：紧急但不重要";
"Distracting activities. Examples: some meetings, certain calls and emails, others' urgent requests. These tasks often appear important (due to urgency) but contribute little to your goals. Try to delegate or automate them." = "干扰性的活动。例如：某些会议、部分电话和邮件、他人的紧急请求等。这些任务常常看起来重要（因为紧急），但实际上对达成目标帮助不大。应该尽可能委派或找到自动化方案。";

"Quadrant 4: Not Important & Not Urgent" = "第四象限：既不紧急也不重要";
"Time-wasting activities. Examples: excessive social media, entertainment, aimless browsing. These activities not only fail to create value but also consume your time and energy. Minimize them or consciously transform them into genuine rest and relaxation." = "浪费时间的活动。例如：无效社交、过度娱乐、无目的的网络浏览等。这些活动不仅不产生价值，还会消耗你的时间和精力。应该尽量减少这类活动，或者有意识地将其转化为真正的休息和放松。";

"Best Practices" = "最佳实践";
"Daily Habits:" = "每日习惯：";
"• Check first quadrant tasks every morning" = "• 每天早上检查第一象限的任务";
"• Reserve ample time for second quadrant tasks" = "• 预留充足时间处理第二象限的任务";
"• Limit time spent on third quadrant tasks" = "• 限制处理第三象限任务的时间";
"• Be mindful of time spent on fourth quadrant activities" = "• 警惕第四象限活动的时间消耗";

"Long-term Strategies:" = "长期策略：";
"• Reduce urgent tasks through planning" = "• 通过规划减少紧急任务";
"• Create systems to automate repetitive tasks" = "• 建立系统和流程自动化重复性任务";
"• Learn to say \"no\" to unimportant tasks" = "• 学会说\"不\"，避免承担不重要的任务";
"• Regularly review and adjust task categorization" = "• 定期回顾和调整任务分类";

// TaskDetailView
"Task Details" = "任务详情";
"Description" = "描述";
"Created" = "创建时间";
"AI Analysis" = "AI 分析";
"Analysis" = "分析说明";
"Suggestions" = "建议";
"Hidden Value" = "潜在价值";
"Due Date" = "截止日期";
"Title" = "标题";
"No description" = "暂无描述";

// New Task View
"New Task" = "新建任务";
"Task Description" = "任务描述";
"AI Analyze" = "AI 分析";
"Analyzing..." = "正在分析...";
"AI Suggestions" = "AI 建议";
"Click 'AI Analyze' for suggestions" = "点击\"AI 分析\"获取建议";
"Add" = "添加";
"Cancel" = "取消";
"Describe your task, then tap" = "详细描述你的任务，点击";

// Error Messages
"Task description cannot be empty" = "任务描述不能为空";
"Invalid service URL" = "服务 URL 无效";
"Service response error: %@" = "服务响应错误：%@";
"Invalid response format" = "响应格式无效";
"Network error: %@" = "网络错误：%@";
"Claude API key is not configured. Please check your settings." = "Claude API 密钥未配置，请检查设置。";
"Invalid configuration: %@" = "配置无效：%@";

// Feature Status Messages
"Voice input coming soon" = "语音输入功能开发中";

// Analytics View
"Analytics" = "数据分析";
"Task Distribution" = "任务分布";
"Tasks" = "任务";
"Task Distribution Details" = "任务分布详情";
"Task Completion Details" = "任务完成详情";
"Task Balance Details" = "任务平衡详情";
"Task Completion" = "任务完成率";
"Total Complete" = "总完成";
"Q1" = "象限一";
"Q2" = "象限二";
"Q3" = "象限三";
"Q4" = "象限四";
"Overall" = "总体";

// Time Trend View
"Last 7 Days" = "最近7天";
"Last 14 Days" = "最近14天";
"Last 30 Days" = "最近30天";
"Time Trend" = "时间趋势";
"Start Date" = "开始日期";
"Task Trend" = "任务趋势";
"Task Trend Details" = "任务趋势详情";

// Trend Analysis
"Q1 tasks are decreasing, showing improved planning. " = "第一象限任务在减少，说明规划能力在提升。";
"Q1 tasks are increasing, consider better planning. " = "第一象限任务在增加，建议改善规划。";
"More focus on Q2 tasks, good job! " = "更多关注第二象限任务，做得好！";
"Consider spending more time on Q2 tasks. " = "建议多投入时间在第二象限任务上。";
"Not enough data for trend analysis." = "数据不足，无法进行趋势分析。";

// Chart Labels
"Date" = "日期";
"Tasks" = "任务数";
"Quadrant" = "象限";

// Due Date Display
"Due Date: " = "截止日期：";
"today" = "今天";
"tomorrow" = "明天";

// Filter Menu
"Filter" = "筛选";
"Filter by Priority" = "按优先级筛选";
"Filter by Due Date" = "按截止日期筛选";
"None" = "无";
"Reset Filters" = "重置筛选";

// Due Date Filters
"Overdue" = "已过截止日期";
"Due Today" = "今天截止";
"Due Tomorrow" = "明天截止";
"Due This Week" = "本周截止";
"Today %@" = "今天 %@";
"Tomorrow %@" = "明天 %@";

// AddTaskSheet
"Clear" = "清除";
"Clear All" = "清除全部";
"Clear Analysis" = "清除分析";
"Describe your task, then tap ✨" = "详细描述你的任务，点击 ✨";

// Notification Rules Section
"Tasks with Due Date:" = "有截止日期的任务：";
"Every 4 hours, with an extra reminder in the last hour" = "每4小时提醒一次，最后1小时额外提醒";
"48 hours and 24 hours before deadline" = "截止前48小时和24小时提醒";
"Single reminder 24 hours before deadline" = "截止前24小时提醒一次";
"No reminders" = "不发送提醒";
"Tasks without Due Date:" = "无截止日期的任务：";
"Important Tasks:" = "重要任务：";
"Other Tasks:" = "其他任务：";
"Reminder after 24 hours to set a due date" = "创建24小时后提醒设置截止日期";
"Smart reminders based on quadrant priority" = "基于象限优先级的智能提醒";

// Basic Concept Section
"Basic Concept" = "基本概念";
"The Eisenhower Matrix (also known as Priority Matrix) is a time management tool developed by Dwight D. Eisenhower. As a successful military commander and politician, he was known for his exceptional ability to manage time and prioritize tasks. This tool helped him maintain efficiency and organization while handling complex military and political affairs." = "艾森豪威尔矩阵（也称为优先级矩阵）是由德怀特·艾森豪威尔开发的时间管理工具。作为一位成功的军事指挥官和政治家，他以出色的时间管理和任务优先级分配能力而闻名。这个工具帮助他在处理复杂的军事和政治事务时保持高效和有序。";

// Section Headers
"Quadrant Details" = "象限详情";
"Usage Tips" = "使用建议";
"Notification Rules" = "通知规则";

// Quadrant Descriptions
"Quadrant 1: Important & Urgent" = "第一象限：重要且紧急";
"Crises and problems that need immediate attention. Examples: approaching deadlines, urgent meetings, emergencies, health issues. These tasks typically create stress and should be minimized through better planning." = "需要立即处理的危机和问题。例如：临近截止日期的任务、紧急会议、突发事件、健康问题等。这类任务通常会带来压力，应该通过更好的规划来减少。";

"Quadrant 2: Important & Not Urgent" = "第二象限：重要不紧急";
"Activities focused on long-term development. Examples: strategic planning, self-improvement, relationship building, health management, preventive maintenance. This quadrant deserves most of your time as it helps prevent crises and creates lasting value." = "专注于长期发展的活动。例如：战略规划、自我提升、关系建立、健康管理、预防性维护等。这个象限最值得投入时间，因为它能预防危机并创造持久价值。";

"Quadrant 3: Not Important & Urgent" = "第三象限：不重要但紧急";
"Distracting activities. Examples: some meetings, certain calls and emails, others' urgent requests. These tasks often appear important (due to urgency) but contribute little to your goals. Try to delegate or automate them." = "容易分散注意力的活动。例如：某些会议、部分电话和邮件、他人的紧急请求等。这些任务看似重要（因为紧急），但对目标贡献较小。尽量委派或自动化处理。";

"Quadrant 4: Not Important & Not Urgent" = "第四象限：不重要不紧急";
"Wasting time. Examples: excessive social media, entertainment, aimless browsing. These activities not only fail to create value but also consume your time and energy. Minimize them or consciously transform them into genuine rest and relaxation." = "浪费时间的活动。例如：过度使用社交媒体、娱乐、漫无目的的浏览等。这些活动不仅不创造价值，还会消耗时间和精力。应该最小化或有意识地将其转化为真正的休息和放松。";

// User Guide
"User Guide" = "用户指南";

// 通知系统
"Important & Urgent" = "重要且紧急";
"Important Task" = "重要任务";
"Task Reminder" = "任务提醒";
"Important & Urgent Task" = "重要且紧急任务";
"Task Planning Reminder" = "任务规划提醒";

// 通知频率
"Aggressive" = "积极";
"Normal" = "正常";
"Gentle" = "温和";
"Minimal" = "最少";
"Aggressive notification frequency" = "更频繁的通知";
"Normal notification frequency" = "标准通知频率";
"Gentle notification frequency" = "较少的通知频率";
"Minimal notification frequency" = "最少的通知";

// 通知操作
"15 min later" = "15分钟后";
"1 hour later" = "1小时后";
"Mark Done" = "标记完成";
"View Details" = "查看详情";
"Snooze 15 minutes" = "延后15分钟";
"Snooze 1 hour" = "延后1小时";
"Mark task as completed" = "标记任务为已完成";
"View task details" = "查看任务详情";

// 通知内容
"Complete immediately!" = "立即完成！";
"Schedule for today" = "安排今日完成";
"Quick action needed" = "需要快速行动";
"Consider if needed" = "考虑是否必要";
"30-60 min" = "30-60分钟";
"1-2 hours" = "1-2小时";
"15-30 min" = "15-30分钟";
"Variable" = "时间不定";
"Urgent task suggestion" = "请立即完成这个紧急任务";
"Important task suggestion" = "建议今天安排这个重要任务";
"Quick task suggestion" = "这个任务需要快速处理";
"Optional task suggestion" = "考虑这个任务是否仍然需要";
"Estimated time for urgent task" = "预计完成时间：30-60分钟";
"Estimated time for important task" = "预计完成时间：1-2小时";
"Estimated time for quick task" = "预计完成时间：15-30分钟";
"Variable time estimate" = "完成时间不定";

// 通知内容模板
"%@ due in %@. Est. time: %@. %@" = "%@ 还有%@到期。预计用时：%@。%@";
"%@ needs a deadline. %@" = "%@ 需要设置截止时间。%@";
"Consider setting a deadline for %@. %@" = "考虑为%@设置截止时间。%@";
"Consider completing %@" = "考虑完成%@";
"Enhanced notification body template" = "带上下文的任务通知";
"Task needs deadline with context" = "任务需要截止时间及建议";
"Suggest deadline with context" = "带上下文的截止时间建议";
"Simple completion reminder" = "简单的完成提醒";

// 时间格式化
"%d days" = "%d天";
"%d hours" = "%d小时";
"%d minutes" = "%d分钟";
"Days format" = "天数格式";
"Hours format" = "小时格式";
"Minutes format" = "分钟格式";

// 通知分类
"Quadrant 1 title" = "紧急任务分类";
"Quadrant 2 title" = "重要任务分类";
"Quadrant 3 title" = "快速任务分类";
"General task reminder" = "标准任务提醒";

// 设置页面 - 通知
"Notifications" = "通知";
"Notification Preferences" = "通知偏好";
"Notification Frequency" = "通知频率";
"Test Notifications" = "测试通知";
"Test Notification" = "测试通知";
"This is a test notification from Priority Matrix." = "这是来自优先级矩阵的测试通知。";

// 通知设置页面
"Notification Settings" = "通知设置";
"Quadrant Notifications" = "象限通知";
"Quadrant %d" = "象限%d";
"Important & Not Urgent" = "重要不紧急";
"Not Important & Urgent" = "不重要但紧急";
"Not Important & Not Urgent" = "不重要不紧急";
"Q1 description" = "重要且紧急";
"Q2 description" = "重要不紧急";
"Q3 description" = "不重要但紧急";
"Q4 description" = "不重要不紧急";
"Frequency" = "频率";
"Frequency picker" = "选择通知频率";

// 频率描述
"More frequent notifications for all tasks" = "所有任务更频繁的通知";
"Standard notification timing" = "标准通知时间";
"Reduced frequency, less interruption" = "降低频率，减少干扰";
"Minimal notifications, only most important" = "最少通知，仅最重要的";

// 免打扰
"Do Not Disturb Periods" = "免打扰时间段";
"Disabled" = "已禁用";
"Add DND Period" = "添加免打扰时间";
"Add Do Not Disturb Period" = "添加免打扰时间段";
"Start Time" = "开始时间";
"End Time" = "结束时间";
"Enabled" = "已启用";
"Save" = "保存";

// 高级设置
"Advanced Settings" = "高级设置";
"Override DND for Urgent Tasks" = "紧急任务覆盖免打扰";
"Enable Notification Aggregation" = "启用通知聚合";
"Aggregation Threshold" = "聚合阈值";
"Notifications will be grouped when %d or more arrive within 5 minutes" = "当%d个或更多通知在5分钟内到达时将被分组";

// 延后的任务
"Snoozed Tasks" = "延后的任务";
"Snoozed until" = "延后至";

// 聚合通知
"%d Tasks Need Attention" = "%d个任务需要处理";
"Most urgent: %@" = "最紧急：%@";
"View All" = "查看全部";
"Snooze All 1h" = "全部延后1小时";
"View all tasks action" = "查看所有任务";
"Snooze all tasks action" = "将所有任务延后1小时";
"Quadrant 1 short" = "象限一";
"Quadrant 2 short" = "象限二";
"Quadrant 3 short" = "象限三";
"Quadrant 4 short" = "象限四";

// 系统状态
"System Status" = "系统状态";
"Refresh" = "刷新";
"Pending" = "待发送";
"Memory" = "内存";
"Refresh status" = "刷新系统状态";
