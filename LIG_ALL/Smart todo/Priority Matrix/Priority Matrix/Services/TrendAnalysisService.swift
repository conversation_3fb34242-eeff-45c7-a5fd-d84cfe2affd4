import Foundation
import SwiftData

class TrendAnalysisService {
    static func calculateTrend(tasks: [UserTask], days: Int) -> [DayTrendData] {
        let calendar = Calendar.current
        let endDate = Date()
        let startDate = calendar.date(byAdding: .day, value: -(days - 1), to: endDate)!
        
        var trendData: [DayTrendData] = []
        var currentDate = startDate
        
        while currentDate <= endDate {
            let dayStart = calendar.startOfDay(for: currentDate)
            let dayEnd = calendar.date(byAdding: .day, value: 1, to: dayStart)!
            
            // 获取当天的任务
            let dayTasks = (try? tasks.filter(#Predicate<UserTask> { task in
                return task.dateCreated >= dayStart && task.dateCreated < dayEnd
            })) ?? []
            
            // 按象限统计任务数量
            var quadrantCounts: [Int: Int] = [:]
            for quadrant in 1...4 {
                let count = (try? dayTasks.filter(#Predicate<UserTask> { task in
                    task.quadrant == quadrant
                }).count) ?? 0
                if count > 0 {
                    quadrantCounts[quadrant] = count
                }
            }
            
            trendData.append(DayTrendData(date: currentDate, taskCounts: quadrantCounts))
            currentDate = calendar.date(byAdding: .day, value: 1, to: currentDate)!
        }
        
        return trendData
    }
    
    static func analyzeTrend(_ data: [DayTrendData]) -> String {
        // 计算每个象限的趋势
        var trends: [Int: Double] = [:]
        
        for quadrant in 1...4 {
            let counts = data.map { $0.taskCounts[quadrant] ?? 0 }
            if counts.count >= 2 {
                let firstHalf = Array(counts[..<(counts.count/2)])
                let secondHalf = Array(counts[(counts.count/2)...])
                
                let firstAvg = Double(firstHalf.reduce(0, +)) / Double(firstHalf.count)
                let secondAvg = Double(secondHalf.reduce(0, +)) / Double(secondHalf.count)
                
                trends[quadrant] = secondAvg - firstAvg
            }
        }
        
        // 生成趋势描述
        var description = ""
        
        if let q1Trend = trends[1] {
            if q1Trend < 0 {
                description += NSLocalizedString("Q1 tasks are decreasing, showing improved planning. ", comment: "Trend analysis")
            } else if q1Trend > 0 {
                description += NSLocalizedString("Q1 tasks are increasing, consider better planning. ", comment: "Trend analysis")
            }
        }
        
        if let q2Trend = trends[2] {
            if q2Trend > 0 {
                description += NSLocalizedString("More focus on Q2 tasks, good job! ", comment: "Trend analysis")
            } else if q2Trend < 0 {
                description += NSLocalizedString("Consider spending more time on Q2 tasks. ", comment: "Trend analysis")
            }
        }
        
        return description.isEmpty ? 
            NSLocalizedString("Not enough data for trend analysis.", comment: "Trend analysis") : 
            description.trimmingCharacters(in: .whitespaces)
    }
} 