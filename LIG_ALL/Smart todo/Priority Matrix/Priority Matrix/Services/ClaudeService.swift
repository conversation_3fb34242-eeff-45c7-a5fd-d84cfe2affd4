import Foundation

/// Claude AI service implementation
final class ClaudeService: AIServiceProtocol {
    /// Available Claude models
    enum ClaudeModel: String {
        case sonnet_3_5 = "claude-3-5-sonnet-20241022"
        case haiku_3_5 = "claude-3-5-haiku-20241022"
        case haiku_3 = "claude-3-haiku-20240307"
        
        static var `default`: ClaudeModel { .sonnet_3_5 }
    }
    
    /// Service configuration
    let configuration: AIServiceConfiguration
    
    /// Base URL for the Claude API
    private let baseURL = "https://api.anthropic.com/v1"
    
    /// URLSession for network requests
    private let session: URLSession
    
    /// Initialize the service with configuration
    init(model: ClaudeModel = .default) throws {
        // First try to get API key from Keychain
        if let apiKey = try? KeychainManager.loadString(forKey: "CLAUDE_API_KEY") {
            self.configuration = AIServiceConfiguration(
                provider: .claude,
                model: model.rawValue,
                apiKey: apiKey
            )
        }
        // Then try to get from Info.plist
        else if let apiKey = Bundle.main.object(forInfoDictionaryKey: "CLAUDE_API_KEY") as? String,
                !apiKey.isEmpty {
            self.configuration = AIServiceConfiguration(
                provider: .claude,
                model: model.rawValue,
                apiKey: apiKey
            )
            try? KeychainManager.save(key: "CLAUDE_API_KEY", string: apiKey)
        }
        // Finally try environment variables (mainly for development)
        else if let apiKey = ProcessInfo.processInfo.environment["CLAUDE_API_KEY_VALUE"] {
            self.configuration = AIServiceConfiguration(
                provider: .claude,
                model: model.rawValue,
                apiKey: apiKey
            )
            try? KeychainManager.save(key: "CLAUDE_API_KEY", string: apiKey)
        }
        else {
            throw AIServiceError.missingAPIKey
        }
        
        // Configure URLSession
        let config = URLSessionConfiguration.default
        config.httpAdditionalHeaders = configuration.headers
        self.session = URLSession(configuration: config)
    }
    
    /// Analyzes a task using Claude AI
    /// - Parameter input: The task description to analyze
    /// - Returns: Structured task analysis response
    /// - Throws: AIServiceError if the analysis fails
    func analyzeTask(_ input: String) async throws -> AITaskResponse {
        // Validate input
        guard !input.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else {
            throw AIServiceError.emptyInput
        }
        
        // Build request URL
        guard let url = URL(string: "\(baseURL)/messages") else {
            throw AIServiceError.invalidURL
        }
        
        // Build request parameters with the shared prompt template
        var parameters = configuration.parameters
        parameters["messages"] = [
            ["role": "user", "content": generatePrompt(for: input)]
        ]
        
        // Create request
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        
        // Add headers to request
        configuration.headers.forEach { header in
            request.setValue(header.value, forHTTPHeaderField: header.key)
        }
        
        request.httpBody = try JSONSerialization.data(withJSONObject: parameters)
        
        do {
            // Send request
            let (data, response) = try await session.data(for: request)
            
            // Validate response
            guard let httpResponse = response as? HTTPURLResponse,
                  (200...299).contains(httpResponse.statusCode) else {
                // Try to parse error message if available
                if let errorDict = try? JSONSerialization.jsonObject(with: data) as? [String: Any],
                   let error = errorDict["error"] as? [String: Any],
                   let message = error["message"] as? String {
                    throw AIServiceError.invalidResponse(message)
                }
                throw AIServiceError.invalidResponse("Request failed")
            }
            
            // Parse response
            guard let responseDict = try JSONSerialization.jsonObject(with: data) as? [String: Any],
                  let content = (responseDict["content"] as? [[String: Any]])?.first?["text"] as? String,
                  let jsonData = content.data(using: .utf8) else {
                throw AIServiceError.invalidResponseFormat
            }
            
            // Decode to AITaskResponse
            return try JSONDecoder().decode(AITaskResponse.self, from: jsonData)
        } catch let error as AIServiceError {
            throw error
        } catch {
            throw AIServiceError.networkError(error)
        }
    }
} 
