# Services Examples

## 新增 AI 服务之后的api key配置步骤

1. 在 Debug.xcconfig 和 Release.xcconfig 中添加变量定义：
```
CLAUDE_API_KEY = $(CLAUDE_API_KEY_VALUE)
DEEPSEEK_API_KEY = $(DEEPSEEK_API_KEY_VALUE)
```

2. 在 Xcode target 的 Build Settings 中添加 User-Defined 变量：
- CLAUDE_API_KEY = $(CLAUDE_API_KEY_VALUE)
- DEEPSEEK_API_KEY = $(DEEPSEEK_API_KEY_VALUE)

3. 在 Info.plist 中引用这些变量：
```xml
<key>CLAUDE_API_KEY</key>
<string>$(CLAUDE_API_KEY)</string>
<key>DEEPSEEK_API_KEY</key>
<string>$(DEEPSEEK_API_KEY)</string>
```

4. 在 scheme 的环境变量中设置实际的 API Key 值：
- CLAUDE_API_KEY_VALUE = your-api-key
- DEEPSEEK_API_KEY_VALUE = your-api-key

5. 如果使用 Xcode Cloud 自动构建：
- 在 Xcode Cloud 工作流程设置中添加环境变量
- 导航到 Xcode Cloud > Workflow > Environment
- 添加以下环境变量：
  * CLAUDE_API_KEY_VALUE = your-api-key
  * DEEPSEEK_API_KEY_VALUE = your-api-key
- 确保变量名称与 xcconfig 文件中的定义完全匹配

## 配置链说明

完整的配置链路如下：
1. Xcode Cloud Environment / Scheme Environment Variables 设置实际的 API Key 值
2. xcconfig 文件通过 $(KEY_VALUE) 引用环境变量
3. Build Settings 中的 User-Defined 变量引用 xcconfig 中的定义
4. Info.plist 通过 $(KEY) 引用 Build Settings 中的变量
5. 代码中通过 Bundle.main.object(forInfoDictionaryKey:) 读取 Info.plist 的值

## 故障排查

如果遇到 "AI service needs configuration" 错误，请按以下步骤检查：

1. 确认 Build Settings 中的 User-Defined 变量是否正确设置
2. 检查 scheme 环境变量是否正确配置
3. 如果使用 Xcode Cloud，检查工作流程中的环境变量是否已添加
4. 验证 Info.plist 中的变量引用是否正确
5. 使用 print 语句在服务初始化时输出配置信息进行调试

常见问题：
- API Key 在本地运行正常但在 Xcode Cloud 构建失败：检查 Xcode Cloud 环境变量配置
- 环境变量未生效：确认变量名完全匹配，注意大小写
- 配置值为空：检查配置链中每一步的变量引用是否正确

## AI Service Usage

### Basic Task Analysis
```swift
// 使用 Claude 服务
let claudeService = try ClaudeService()
let claudeResult = try await claudeService.analyzeTask("完成项目文档")

// 使用 DeepSeek 服务
let deepseekService = try DeepSeekService()
let deepseekResult = try await deepseekService.analyzeTask("完成项目文档")

// 打印分析结果
print("解释: \(result.explanation)")
print("建议: \(result.suggestions.joined(separator: "\n"))")
print("隐藏价值: \(result.hiddenValue)")
```

### 选择不同的模型
```swift
// Claude 模型选项
let sonnetService = try ClaudeService(model: .sonnet_3_5)   // claude-3-5-sonnet-20241022
let haikuService = try ClaudeService(model: .haiku_3_5)    // claude-3-5-haiku-20241022
let haiku3Service = try ClaudeService(model: .haiku_3)      // claude-3-haiku-20240307

// DeepSeek 模型选项
let chatService = try DeepSeekService(model: .chat)      // deepseek-chat
let reasonerService = try DeepSeekService(model: .reasoner)  // deepseek-reasoner
```

### 错误处理
```swift
do {
    let result = try await service.analyzeTaskWithRetry("完成项目文档")
} catch AIServiceError.emptyInput {
    print("请输入任务描述")
} catch AIServiceError.invalidResponse {
    print("AI 返回格式有误，正在重试...")
} catch {
    print("分析遇到问题: \(error.localizedDescription)")
}
```

### 配置服务
```swift
// Claude 配置
let claudeConfig = AIServiceConfiguration(
    provider: .claude,
    model: "claude-3-5-sonnet-20241022",
    apiKey: "your-claude-api-key"
)

// DeepSeek 配置
let deepseekConfig = AIServiceConfiguration(
    provider: .deepseek,
    model: "deepseek-chat",
    apiKey: "your-deepseek-api-key"
)
```

## Claude Service Notes
- Default: sonnet_3_5 (claude-3-5-sonnet-20241022)
- 适合复杂任务分析和深度理解
- 响应格式稳定，直接返回 JSON
- 支持多个高性能模型选择

## DeepSeek Service Notes
- Default: chat (deepseek-chat)
- 响应速度快，成本较低
- 需要注意处理 Markdown 响应格式，特别是在模拟器环境中
- 响应处理示例：
```swift
// 处理可能的 Markdown 格式
if content.hasPrefix("```json") && content.hasSuffix("```") {
    // 移除代码块标记
    let start = content.index(content.startIndex, offsetBy: 7)
    let end = content.index(content.endIndex, offsetBy: -4)
    jsonContent = String(content[start..<end])
} else if content.contains("```") {
    // 使用正则表达式提取代码块内容
    let pattern = "```(?:json)?\\n(.+?)\\n```"
    let regex = try NSRegularExpression(pattern: pattern, options: [.dotMatchesLineSeparators])
    let range = NSRange(content.startIndex..., in: content)
    if let match = regex.firstMatch(in: content, options: [], range: range),
       let jsonRange = Range(match.range(at: 1), in: content) {
        jsonContent = String(content[jsonRange])
    }
}
```

### DeepSeek 调试建议
1. 添加响应内容打印：
```swift
print("Raw response content:")
print(content)
print("\nAttempting to parse JSON content:")
print(jsonContent)
```

2. 检查 token 使用情况：
```swift
if let usage = responseDict["usage"] as? [String: Any] {
    print("DeepSeek API Usage - Prompt tokens: \(usage["prompt_tokens"] ?? 0), Completion tokens: \(usage["completion_tokens"] ?? 0), Total tokens: \(usage["total_tokens"] ?? 0)")
}
```

## Service Selection Strategy
- Claude Service: 
  * 适用于需要深度分析的复杂任务
  * 需要高质量、详细的建议时
  * 预算充足的情况

- DeepSeek Service:
  * 适用于一般性任务分析
  * 需要快速响应时
  * 需要控制成本时
  * 作为 Claude 的备选服务

建议根据具体使用场景和需求选择合适的服务。可以实现服务自动切换机制，在主要服务不可用时切换到备选服务。

# AI 服务集成指南

## 版本发布配置指南

### 修改版本信息
1. 在 Xcode 项目设置中：
   - 选择项目 Priority Matrix
   - 选择主 target
   - 在 "General" 标签页的 "Identity" 部分：
     * Version: 语义化版本号（例如：1.0.0）
     * Build: 构建号（例如：1）

### 版本号规范
- 主版本号.次版本号.修订号（例如：1.2.3）
  * 主版本号：不兼容的 API 修改
  * 次版本号：向下兼容的功能性新增
  * 修订号：向下兼容的问题修正

### 发布流程
1. 更新版本号和构建号
2. 执行 Product > Archive
3. 在 Archive 窗口中：
   - 点击 "Distribute App"
   - 选择 "App Store Connect"
   - 配置发布选项：
     * 选择发布目标（App Store）
     * 选择发布方式（自动/手动）
     * 配置加密合规信息
     * 选择是否自动管理版本号

### 发布前检查清单
- [ ] 版本号已更新
- [ ] 构建号已递增
- [ ] API Keys 已在 Xcode Cloud 环境中配置
- [ ] 本地测试通过
- [ ] 更新日志已准备
- [ ] 截图和预览已更新
- [ ] App Store 描述已更新（如需）

### 注意事项
1. 每次发布前确保 Xcode Cloud 环境变量配置正确
2. 构建号应该递增
3. 遵循语义化版本规范
4. 保持更新日志清晰明确 

## 发布工作流程

### 准备发布
1. 更新 `RELEASE_NOTES.md`
   - 确认版本号
   - 更新发布日期
   - 添加新功能、改进和修复的详细说明
   - 提交更改并推送到主分支

### Xcode Cloud 配置
1. 在 Xcode Cloud 工作流程中添加触发条件：
   - 选择 "Start builds when" 下的 "Changes to files"
   - 添加 `RELEASE_NOTES.md` 作为触发文件
   - 配置工作流以在文件更改时自动开始构建

### 发布检查清单
1. 文档更新
   - [ ] RELEASE_NOTES.md 已更新
   - [ ] 版本号和日期已确认
   - [ ] 更新内容已经过审核

2. 代码准备
   - [ ] 所有功能测试通过
   - [ ] 所有 API Keys 配置正确
   - [ ] 本地构建测试通过

3. 构建配置
   - [ ] Xcode 版本号已更新
   - [ ] Build 号已递增
   - [ ] 签名证书有效

4. App Store 准备
   - [ ] 截图已更新
   - [ ] 应用描述已更新
   - [ ] 隐私政策已更新（如需）

### 发布后检查
1. 验证发布
   - 确认 TestFlight 版本可用
   - 测试关键功能
   - 验证 API Keys 配置

2. 监控
   - 检查崩溃报告
   - 监控用户反馈
   - 跟踪版本采用率 