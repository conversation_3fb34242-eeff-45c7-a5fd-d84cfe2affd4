import Foundation

/// DeepSeek AI service implementation
final class DeepSeekService: AIServiceProtocol {
    /// Available DeepSeek models
    enum DeepSeekModel: String {
        /// DeepSeek-V3 chat model
        case chat = "deepseek-chat"
        /// DeepSeek-R1 reasoning model
        case reasoner = "deepseek-reasoner"
        
        /// Default model is deepseek-chat (DeepSeek-V3)
        static var `default`: DeepSeekModel { .chat }
    }
    
    /// Service configuration
    let configuration: AIServiceConfiguration
    
    /// Base URL for the DeepSeek API
    private let baseURL = "https://api.deepseek.com"  // Remove /v1 to match official example
    
    /// URLSession for network requests
    private let session: URLSession
    
    /// Initialize the service with configuration
    init(model: DeepSeekModel = .default) throws {
        print("🔍 Checking API Key sources...")
        
        // First try to get API key from Keychain
        if let apiKey = try? KeychainManager.loadString(forKey: "DEEPSEEK_API_KEY") {
            print("✅ Using API Key from Keychain")
            self.configuration = AIServiceConfiguration(
                provider: .deepseek,
                model: model.rawValue,
                apiKey: apiKey
            )
        }
        // Then try to get from Info.plist
        else if let apiKey = Bundle.main.object(forInfoDictionaryKey: "DEEPSEEK_API_KEY") as? String,
                !apiKey.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
            print("✅ Using API Key from Info.plist")
            self.configuration = AIServiceConfiguration(
                provider: .deepseek,
                model: model.rawValue,
                apiKey: apiKey
            )
            try? KeychainManager.save(key: "DEEPSEEK_API_KEY", string: apiKey)
        }
        // Finally try environment variables (mainly for development)
        else if let apiKey = ProcessInfo.processInfo.environment["DEEPSEEK_API_KEY_VALUE"] {
            print("✅ Using API Key from Environment Variables")
            self.configuration = AIServiceConfiguration(
                provider: .deepseek,
                model: model.rawValue,
                apiKey: apiKey
            )
            try? KeychainManager.save(key: "DEEPSEEK_API_KEY", string: apiKey)
        }
        else {
            print("❌ API Key not found in any location:")
            
            // Check Keychain
            let keychainStatus = (try? KeychainManager.loadString(forKey: "DEEPSEEK_API_KEY")).map { _ in "exists" } ?? "not found"
            print("- Keychain: \(keychainStatus)")
            
            // Check Info.plist
            let plistStatus = Bundle.main.object(forInfoDictionaryKey: "DEEPSEEK_API_KEY") as? String ?? "not found"
            print("- Info.plist: \(plistStatus)")
            
            // Check Environment Variables
            let envStatus = ProcessInfo.processInfo.environment["DEEPSEEK_API_KEY_VALUE"] ?? "not found"
            print("- Environment: \(envStatus)")
            
            throw AIServiceError.missingAPIKey
        }
        
        // Configure URLSession with default headers
        let config = URLSessionConfiguration.default
        config.httpAdditionalHeaders = [
            "Content-Type": "application/json",
            "Authorization": "Bearer \(configuration.apiKey)"
        ]
        self.session = URLSession(configuration: config)
    }
    
    /// Protocol required method but not used in DeepSeek service
    func generatePrompt(for input: String) -> String {
        fatalError("DeepSeek service uses chat-specific prompts")
    }
    
    /// Analyzes a task using DeepSeek AI
    /// - Parameter input: The task description to analyze
    /// - Returns: Structured task analysis response
    /// - Throws: AIServiceError if the analysis fails
    func analyzeTask(_ input: String) async throws -> AITaskResponse {
        // Validate input
        guard !input.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else {
            throw AIServiceError.emptyInput
        }
        
        // Build request URL
        guard let url = URL(string: "\(baseURL)/chat/completions") else {
            throw AIServiceError.invalidURL
        }
        
        // Build request body with separate system and user prompts
        let requestBody: [String: Any] = [
            "model": configuration.model,
            "messages": [
                ["role": "system", "content": generateSystemPrompt(for: input)],
                ["role": "user", "content": generateUserPrompt(for: input)]
            ],
            "stream": false
        ]
        
        // Create request
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        request.setValue("Bearer \(configuration.apiKey)", forHTTPHeaderField: "Authorization")
        request.httpBody = try JSONSerialization.data(withJSONObject: requestBody)
        
        do {
            // Send request
            let (data, response) = try await session.data(for: request)
            
            // Validate response
            guard let httpResponse = response as? HTTPURLResponse,
                  (200...299).contains(httpResponse.statusCode) else {
                if let errorDict = try? JSONSerialization.jsonObject(with: data) as? [String: Any],
                   let message = (errorDict["error"] as? [String: Any])?["message"] as? String {
                    throw AIServiceError.invalidResponse(message)
                }
                throw AIServiceError.invalidResponse("Request failed with status: \(String(describing: (response as? HTTPURLResponse)?.statusCode))")
            }
            
            // Parse response according to DeepSeek API format
            guard let responseDict = try JSONSerialization.jsonObject(with: data) as? [String: Any],
                  let choices = responseDict["choices"] as? [[String: Any]],
                  let firstChoice = choices.first,
                  let message = firstChoice["message"] as? [String: Any],
                   let content = message["content"] as? String else {
                throw AIServiceError.invalidResponseFormat
            }
            
            // Log token usage if available
            if let usage = responseDict["usage"] as? [String: Any] {
                print("DeepSeek API Usage - Prompt tokens: \(usage["prompt_tokens"] ?? 0), Completion tokens: \(usage["completion_tokens"] ?? 0), Total tokens: \(usage["total_tokens"] ?? 0)")
            }
            
            // Extract JSON from markdown content if necessary
            let jsonContent: String
            if content.hasPrefix("```json") && content.hasSuffix("```") {
                // Remove markdown code block markers
                let start = content.index(content.startIndex, offsetBy: 7) // Skip "```json\n"
                let end = content.index(content.endIndex, offsetBy: -4)    // Skip "\n```"
                jsonContent = String(content[start..<end])
            } else if content.contains("```") {
                // Try to find JSON block in markdown
                let pattern = "```(?:json)?\\n(.+?)\\n```"
                let regex = try NSRegularExpression(pattern: pattern, options: [.dotMatchesLineSeparators])
                let range = NSRange(content.startIndex..., in: content)
                if let match = regex.firstMatch(in: content, options: [], range: range),
                   let jsonRange = Range(match.range(at: 1), in: content) {
                    jsonContent = String(content[jsonRange])
                } else {
                    // If no markdown block found, try to use the content as is
                    jsonContent = content
                }
            } else {
                // Use content as is
                jsonContent = content
            }
            
            print("Attempting to parse JSON content:")
            print(jsonContent)
            
            // Decode to AITaskResponse
            guard let jsonData = jsonContent.data(using: .utf8) else {
                throw AIServiceError.invalidResponseFormat
            }
            
            do {
                return try JSONDecoder().decode(AITaskResponse.self, from: jsonData)
            } catch {
                print("JSON Decoding Error: \(error)")
                throw AIServiceError.invalidResponseFormat
            }
        } catch let error as AIServiceError {
            throw error
        } catch {
            throw AIServiceError.networkError(error)
        }
    }
}

// MARK: - Prompt Generation
extension DeepSeekService {
    func generateSystemPrompt(for input: String) -> String {
        // 检测输入语言
        let isChineseInput = input.range(of: "\\p{Han}", options: .regularExpression) != nil
        
        if isChineseInput {
            return """
            你是一个专业的任务分析助手，使用艾森豪威尔矩阵（四象限法则）来分析任务。

            # 分析框架
            1. 重要性定义：
            - 对长期目标的贡献：
              * 职业发展（技能提升、人脉建设、知识积累）
              * 健康管理（习惯养成、预防保健、生活方式改善）
              * 人际关系（信任建立、理解加深、共同成长）
            - 持久价值（至少3个月以上）：
              * 可复用的技能或知识
              * 有益的系统或习惯
              * 未来机会的基础
            - 不处理的后果：
              * 显著阻碍目标达成
              * 未来需要更多资源解决
              * 限制未来选择空间

            2. 紧急性定义：
            - 明确的截止时间（参考当前时间）
            - 24小时内必须完成
            - 他人在等待响应
            - 推迟会造成直接负面影响
            - 需要立即行动

            请按以下 JSON 格式输出分析结果：
            {
                "title": "简短的任务标题",
                "description": "基于用户输入的任务分析，包括任务目标、关键要素、时间要求和潜在影响",
                "importance": true或false,
                "urgency": true或false,
                "hidden_value": "任务的潜在价值和机会",
                "suggestions": [
                    "具体可执行的建议1",
                    "具体可执行的建议2"
                ],
                "explanation": "基于以上定义，解释为什么这个任务被判定为重要/紧急或不重要/不紧急"
            }
            """
        } else {
            return """
            You are a professional task analysis assistant using the Eisenhower Matrix (Four Quadrants) method.

            # Analysis Framework
            1. Importance Definition:
            - Contribution to long-term goals:
              * Career (skill development, networking, knowledge building)
              * Health (habit formation, preventive care, lifestyle improvement)
              * Relationships (trust building, understanding, shared growth)
            - Lasting value (3+ months):
              * Reusable skills or knowledge
              * Beneficial systems or habits
              * Foundation for future opportunities
            - Consequences of not addressing:
              * Significantly blocks goal achievement
              * Requires more resources later
              * Limits future choices

            2. Urgency Definition:
            - Clear deadline (relative to current time)
            - Must be completed within 24 hours
            - Others waiting for response
            - Delay causes immediate negative impact
            - Requires immediate action

            Please provide analysis in the following JSON format:
            {
                "title": "Brief task title",
                "description": "Task analysis based on user input, including objectives, key elements, time requirements, and potential impacts",
                "importance": true or false,
                "urgency": true or false,
                "hidden_value": "Task's potential value and opportunities",
                "suggestions": [
                    "actionable suggestion 1",
                    "actionable suggestion 2"
                ],
                "explanation": "Based on the above definitions, why this task is classified as important/urgent or not"
            }
            """
        }
    }
    
    func generateUserPrompt(for input: String) -> String {
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd HH:mm:ss"
        let currentTime = dateFormatter.string(from: Date())
        
        // 检测输入语言
        let isChineseInput = input.range(of: "\\p{Han}", options: .regularExpression) != nil
        
        if isChineseInput {
            return """
            请分析以下任务（当前时间：\(currentTime)）：
            
            任务描述：
            \(input)
            
            请按照系统提示的JSON格式输出结果。
            """
        } else {
            return """
            Please analyze the following task (Current time: \(currentTime)):
            
            Task Description:
            \(input)
            
            Please follow the JSON format specified in the system prompt.
            """
        }
    }
} 
