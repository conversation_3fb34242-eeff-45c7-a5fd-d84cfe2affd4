import Foundation
import SwiftUI

// 全局语言状态存储（非 actor 隔离）
private final class LanguageState {
    static let shared = LanguageState()
    
    private var _currentLanguage: String = UserDefaults.standard.string(forKey: "UserSelectedLanguage") ?? "system"
    private var _bundle: Bundle = Bundle.main
    private let lock = NSLock()
    
    private init() {
        updateBundle()
    }
    
    var currentLanguage: String {
        get {
            lock.lock()
            defer { lock.unlock() }
            return _currentLanguage
        }
        set {
            lock.lock()
            defer { lock.unlock() }
            _currentLanguage = newValue
            UserDefaults.standard.set(newValue, forKey: "UserSelectedLanguage")
            updateBundle()
        }
    }
    
    var bundle: Bundle {
        lock.lock()
        defer { lock.unlock() }
        return _bundle
    }
    
    private func updateBundle() {
        let effectiveLanguage = getEffectiveLanguage()
        if let path = Bundle.main.path(forResource: effectiveLanguage, ofType: "lproj"),
           let newBundle = Bundle(path: path) {
            _bundle = newBundle
        } else {
            _bundle = Bundle.main
        }
    }
    
    private func getEffectiveLanguage() -> String {
        if _currentLanguage == "system" {
            let preferredLanguages = Bundle.main.preferredLocalizations
            let systemLanguage = preferredLanguages.first ?? "en"
            switch systemLanguage {
            case "zh-Hans", "zh-CN", "zh-Hans-CN":
                return "zh_CN"
            case let lang where lang.hasPrefix("zh"):
                return "zh_CN"
            default:
                return "en"
            }
        }
        return _currentLanguage == "zh_CN" ? "zh_CN" : "en"
    }
}

// MainActor 隔离的 LanguageManager
@MainActor
final class LanguageManager: ObservableObject {
    static let shared = LanguageManager()
    
    @Published var currentLanguage: String {
        didSet {
            LanguageState.shared.currentLanguage = currentLanguage
        }
    }
    
    private init() {
        self.currentLanguage = LanguageState.shared.currentLanguage
    }
    
    func setLanguage(_ language: String) {
        currentLanguage = language
        objectWillChange.send()
    }
    
    func localizedString(for key: String, comment: String = "") -> String {
        return LanguageState.shared.bundle.localizedString(forKey: key, value: nil, table: nil)
    }
    
    var isRTL: Bool {
        let language = LanguageState.shared.currentLanguage
        if language == "system" {
            let preferredLanguages = Bundle.main.preferredLocalizations
            let systemLanguage = preferredLanguages.first ?? "en"
            return systemLanguage == "ar" || systemLanguage == "he"
        }
        return language == "ar" || language == "he"
    }
}

// 全局本地化函数（可以从任何地方调用）
func LocalizedString(_ key: String, comment: String = "") -> String {
    return LanguageState.shared.bundle.localizedString(forKey: key, value: nil, table: nil)
}

// Nonisolated accessor for LanguageManager.shared
let globalLanguageManager: LanguageManager = {
    MainActor.assumeIsolated {
        LanguageManager.shared
    }
}()

// SwiftUI 环境扩展
struct LanguageEnvironmentKey: EnvironmentKey {
    nonisolated static let defaultValue: LanguageManager? = nil
}

extension EnvironmentValues {
    var languageManager: LanguageManager? {
        get { self[LanguageEnvironmentKey.self] }
        set { self[LanguageEnvironmentKey.self] = newValue }
    }
}