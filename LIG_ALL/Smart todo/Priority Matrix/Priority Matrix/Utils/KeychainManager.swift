import Foundation
import Security
import os.log

/// A utility class that provides secure storage and retrieval of sensitive information using iOS Keychain Services.
/// This class follows Apple's security best practices and provides type-safe access to the Keychain.
final class KeychainManager {
    
    // MARK: - Error Types
    
    /// Represents various errors that can occur during Keychain operations
    enum KeychainError: LocalizedError {
        case duplicateEntry
        case unknown(OSStatus)
        case itemNotFound
        case invalidItemFormat
        
        var errorDescription: String? {
            switch self {
            case .duplicateEntry:
                return "A duplicate item already exists in the keychain"
            case .unknown(let status):
                return "An unknown keychain error occurred: \(status)"
            case .itemNotFound:
                return "The requested item was not found in the keychain"
            case .invalidItemFormat:
                return "The item data is in an invalid format"
            }
        }
    }
    
    // MARK: - Logging
    
    private static let logger = Logger(subsystem: Bundle.main.bundleIdentifier ?? "Priority-Matrix",
                                     category: "KeychainManager")
    
    // MARK: - Public Methods
    
    /// Saves a string value securely in the Keychain.
    /// - Parameters:
    ///   - key: The unique identifier for the stored item
    ///   - string: The string value to be stored securely
    /// - Throws: `KeychainError` if the save operation fails
    static func save(key: String, string: String) throws {
        logger.debug("Attempting to save string for key: \(key)")
        
        let data = string.data(using: .utf8)!
        let query: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrAccount as String: key,
            kSecValueData as String: data,
            kSecAttrAccessible as String: kSecAttrAccessibleWhenUnlocked
        ]
        
        let status = SecItemAdd(query as CFDictionary, nil)
        
        if status == errSecDuplicateItem {
            logger.debug("Updating existing item for key: \(key)")
            try update(key: key, data: data)
        } else if status != errSecSuccess {
            logger.error("Failed to save item. Status: \(status)")
            throw KeychainError.unknown(status)
        }
        
        logger.debug("Successfully saved string for key: \(key)")
    }
    
    /// Retrieves a string value from the Keychain.
    /// - Parameter key: The unique identifier of the item to retrieve
    /// - Returns: The stored string value
    /// - Throws: `KeychainError` if the retrieval operation fails
    static func loadString(forKey key: String) throws -> String {
        logger.debug("Attempting to load string for key: \(key)")
        
        let query: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrAccount as String: key,
            kSecReturnData as String: true
        ]
        
        var result: AnyObject?
        let status = SecItemCopyMatching(query as CFDictionary, &result)
        
        guard status == errSecSuccess else {
            logger.error("Failed to load item. Status: \(status)")
            if status == errSecItemNotFound {
                throw KeychainError.itemNotFound
            }
            throw KeychainError.unknown(status)
        }
        
        guard let data = result as? Data,
              let string = String(data: data, encoding: .utf8) else {
            logger.error("Invalid data format for key: \(key)")
            throw KeychainError.invalidItemFormat
        }
        
        logger.debug("Successfully loaded string for key: \(key)")
        return string
    }
    
    /// Deletes an item from the Keychain.
    /// - Parameter key: The unique identifier of the item to delete
    /// - Throws: `KeychainError` if the deletion operation fails
    static func delete(key: String) throws {
        logger.debug("Attempting to delete item for key: \(key)")
        
        let query: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrAccount as String: key
        ]
        
        let status = SecItemDelete(query as CFDictionary)
        
        guard status == errSecSuccess || status == errSecItemNotFound else {
            logger.error("Failed to delete item. Status: \(status)")
            throw KeychainError.unknown(status)
        }
        
        logger.debug("Successfully deleted item for key: \(key)")
    }
    
    // MARK: - Private Methods
    
    /// Updates an existing item in the Keychain.
    /// - Parameters:
    ///   - key: The unique identifier of the item to update
    ///   - data: The new data to store
    /// - Throws: `KeychainError` if the update operation fails
    private static func update(key: String, data: Data) throws {
        let query: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrAccount as String: key
        ]
        
        let attributes: [String: Any] = [
            kSecValueData as String: data
        ]
        
        let status = SecItemUpdate(query as CFDictionary, attributes as CFDictionary)
        
        guard status == errSecSuccess else {
            logger.error("Failed to update item. Status: \(status)")
            throw KeychainError.unknown(status)
        }
    }
} 