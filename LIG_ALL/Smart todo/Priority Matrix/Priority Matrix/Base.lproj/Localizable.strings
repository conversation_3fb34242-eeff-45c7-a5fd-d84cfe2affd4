// Tabs
"List" = "List";
"Matrix" = "Matrix";
"Calendar" = "Calendar";
"Completed" = "Completed";
"Settings" = "Settings";

// TaskListView
"Tasks" = "Tasks";
"Enter new task" = "Enter new task";
"Task description (optional)" = "Task description (optional)";
"Important" = "Important";
"Urgent" = "Urgent";
"Add Task" = "Add Task";
"Complete" = "Complete";
"Delete" = "Delete";
"No Tasks" = "No Tasks";
"Add a task to get started" = "Add a task to get started";

// Filter Options
"All" = "All";
"All Tasks" = "All Tasks";
"All Important" = "All Important";
"All Urgent" = "All Urgent";
"Q1" = "Q1";
"Q2" = "Q2";
"Q3" = "Q3";
"Q4" = "Q4";
"Due Today" = "Due Today";
"Due Tomorrow" = "Due Tomorrow";
"Due This Week" = "Due This Week";
"Overdue" = "Overdue";
"Today" = "Today";
"Tomorrow" = "Tomorrow";
"This Week" = "This Week";

// Sort Options
"Date Created" = "Date Created";
"Title" = "Title";
"Quadrant" = "Quadrant";
"Due Date" = "Due Date";
"Sort Ascending" = "Sort Ascending";
"Sort Descending" = "Sort Descending";

// CompletedTasksView
"Completed Tasks" = "Completed Tasks";
"No Completed Tasks" = "No Completed Tasks";
"Tasks you complete will appear here" = "Tasks you complete will appear here";
"Uncomplete" = "Uncomplete";

// MatrixView
"Priority Matrix" = "Priority Matrix";
"Important & Urgent" = "Important\nUrgent";
"Important & Not Urgent" = "Important\nNot Urgent";
"Not Important & Urgent" = "Not Important\nUrgent";
"Not Important & Not Urgent" = "Not Important\nNot Urgent";

// CalendarView
"Calendar View - Coming Soon" = "Calendar View - Coming Soon";

// SettingsView
"Language" = "Language";
"Follow System" = "Follow System";
"Current System Language" = "Current System Language";
"Change Language" = "Change Language";
"Change" = "Change";
"The language setting will be saved. Please restart the app manually to apply the changes." = "The language setting will be saved. Please restart the app manually to apply the changes.";
"Language Changed" = "Language Changed";
"Please restart the app to apply language changes." = "Please restart the app to apply language changes.";
"OK" = "OK";
"Data Management" = "Data Management";
"Delete All Tasks" = "Delete All Tasks";
"About" = "About";
"Version" = "Version";
"Cancel" = "Cancel";
"The app needs to restart to change the language. Do you want to proceed?" = "The app needs to restart to change the language. Do you want to proceed?";
"Are you sure you want to delete all tasks? This action cannot be undone." = "Are you sure you want to delete all tasks? This action cannot be undone.";

// TaskDetailView
"Due Date" = "Due Date";
"Title" = "Title";
"Description" = "Description";
"No description" = "No description";

// AddTaskSheet
"Clear" = "Clear";
"Clear All" = "Clear All";
"Clear Analysis" = "Clear Analysis";
"Describe your task, then tap ✨" = "Describe your task, then tap ✨"; 

// User Guide
"User Guide" = "User Guide";

// Notification System
"Important & Urgent" = "Important & Urgent";
"Important Task" = "Important Task";
"Task Reminder" = "Task Reminder";
"Important & Urgent Task" = "Important & Urgent Task";
"Task Planning Reminder" = "Task Planning Reminder";

// Notification Frequency
"Aggressive" = "Aggressive";
"Normal" = "Normal";
"Gentle" = "Gentle";
"Minimal" = "Minimal";
"Aggressive notification frequency" = "More frequent notifications";
"Normal notification frequency" = "Standard notification timing";
"Gentle notification frequency" = "Reduced notification frequency";
"Minimal notification frequency" = "Fewer notifications";

// Notification Actions
"15 min later" = "15 min later";
"1 hour later" = "1 hour later";
"Mark Done" = "Mark Done";
"View Details" = "View Details";
"Snooze 15 minutes" = "Snooze 15 minutes";
"Snooze 1 hour" = "Snooze 1 hour";
"Mark task as completed" = "Mark task as completed";
"View task details" = "View task details";

// Notification Content
"Complete immediately!" = "Complete immediately!";
"Schedule for today" = "Schedule for today";
"Quick action needed" = "Quick action needed";
"Consider if needed" = "Consider if needed";
"30-60 min" = "30-60 min";
"1-2 hours" = "1-2 hours";
"15-30 min" = "15-30 min";
"Variable" = "Variable";
"Urgent task suggestion" = "Complete this urgent task immediately";
"Important task suggestion" = "Schedule this important task for today";
"Quick task suggestion" = "This task needs quick action";
"Optional task suggestion" = "Consider if this task is still needed";
"Estimated time for urgent task" = "Estimated completion time: 30-60 minutes";
"Estimated time for important task" = "Estimated completion time: 1-2 hours";
"Estimated time for quick task" = "Estimated completion time: 15-30 minutes";
"Variable time estimate" = "Completion time varies";

// Notification Body Templates
"%@ due in %@. Est. time: %@. %@" = "%@ due in %@. Est. time: %@. %@";
"%@ needs a deadline. %@" = "%@ needs a deadline. %@";
"Consider setting a deadline for %@. %@" = "Consider setting a deadline for %@. %@";
"Consider completing %@" = "Consider completing %@";
"Enhanced notification body template" = "Task notification with context";
"Task needs deadline with context" = "Task requires deadline with suggestions";
"Suggest deadline with context" = "Deadline suggestion with context";
"Simple completion reminder" = "Simple task completion reminder";

// Time Formatting
"%d days" = "%d days";
"%d hours" = "%d hours";
"%d minutes" = "%d minutes";
"Days format" = "Day count format";
"Hours format" = "Hour count format";
"Minutes format" = "Minute count format";

// Notification Categories
"Quadrant 1 title" = "Urgent task category";
"Quadrant 2 title" = "Important task category";
"Quadrant 3 title" = "Quick task category";
"General task reminder" = "Standard task reminder";

// Settings View - Notifications
"Notifications" = "Notifications";
"Notification Preferences" = "Notification Preferences";
"Notification Frequency" = "Notification Frequency";
"Test Notifications" = "Test Notifications";
"Test Notification" = "Test Notification";
"This is a test notification from Priority Matrix." = "This is a test notification from Priority Matrix.";

// Notification Settings View
"Notification Settings" = "Notification Settings";
"Quadrant Notifications" = "Quadrant Notifications";
"Quadrant %d" = "Quadrant %d";
"Important & Not Urgent" = "Important & Not Urgent";
"Not Important & Urgent" = "Not Important & Urgent";
"Not Important & Not Urgent" = "Not Important & Not Urgent";
"Q1 description" = "Important & Urgent";
"Q2 description" = "Important & Not Urgent";
"Q3 description" = "Not Important & Urgent";
"Q4 description" = "Not Important & Not Urgent";
"Frequency" = "Frequency";
"Frequency picker" = "Choose notification frequency";

// Frequency Descriptions
"More frequent notifications for all tasks" = "More frequent notifications for all tasks";
"Standard notification timing" = "Standard notification timing";
"Reduced frequency, less interruption" = "Reduced frequency, less interruption";
"Minimal notifications, only most important" = "Minimal notifications, only most important";

// Do Not Disturb
"Do Not Disturb Periods" = "Do Not Disturb Periods";
"Disabled" = "Disabled";
"Add DND Period" = "Add DND Period";
"Add Do Not Disturb Period" = "Add Do Not Disturb Period";
"Start Time" = "Start Time";
"End Time" = "End Time";
"Enabled" = "Enabled";
"Save" = "Save";

// Advanced Settings
"Advanced Settings" = "Advanced Settings";
"Override DND for Urgent Tasks" = "Override DND for Urgent Tasks";
"Enable Notification Aggregation" = "Enable Notification Aggregation";
"Aggregation Threshold" = "Aggregation Threshold";
"Notifications will be grouped when %d or more arrive within 5 minutes" = "Notifications will be grouped when %d or more arrive within 5 minutes";

// Snoozed Tasks
"Snoozed Tasks" = "Snoozed Tasks";
"Snoozed until" = "Snoozed until";

// Aggregated Notifications
"%d Tasks Need Attention" = "%d Tasks Need Attention";
"Most urgent: %@" = "Most urgent: %@";
"View All" = "View All";
"Snooze All 1h" = "Snooze All 1h";
"View all tasks action" = "View all tasks";
"Snooze all tasks action" = "Snooze all tasks for 1 hour";
"Quadrant 1 short" = "Q1";
"Quadrant 2 short" = "Q2";
"Quadrant 3 short" = "Q3";
"Quadrant 4 short" = "Q4";

// System Status
"System Status" = "System Status";
"Refresh" = "Refresh";
"Pending" = "Pending";
"Memory" = "Memory";
"Refresh status" = "Refresh system status";