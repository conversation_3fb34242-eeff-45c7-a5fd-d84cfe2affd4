import SwiftUI

private struct TabBarVisibilityKey: EnvironmentKey {
    static let defaultValue: Bool = true
}

private struct TabBarVisibilitySetterKey: EnvironmentKey {
    static let defaultValue: (Bool) -> Void = { _ in }
}

extension EnvironmentValues {
    var showTabBar: Bool {
        get { self[TabBarVisibilityKey.self] }
        set { self[TabBarVisibilityKey.self] = newValue }
    }
    
    var setTabBarVisibility: (Bool) -> Void {
        get { self[TabBarVisibilitySetterKey.self] }
        set { self[TabBarVisibilitySetterKey.self] = newValue }
    }
} 
