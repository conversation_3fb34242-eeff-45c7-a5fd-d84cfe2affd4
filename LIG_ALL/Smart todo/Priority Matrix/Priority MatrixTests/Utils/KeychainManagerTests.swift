import XCTest
@testable import Priority_Matrix

/// Test cases for the KeychainManager class to verify secure storage and retrieval of sensitive information.
final class KeychainManagerTests: XCTestCase {
    
    // MARK: - Test Lifecycle
    
    override func setUp() {
        super.setUp()
        // Clean up any existing test data
        try? KeychainManager.delete(key: "test_key")
    }
    
    override func tearDown() {
        // Clean up test data after each test
        try? KeychainManager.delete(key: "test_key")
        super.tearDown()
    }
    
    // MARK: - Test Cases
    
    /// Tests the basic save and load functionality of the KeychainManager
    func testSaveAndLoadString() throws {
        // Given
        let testKey = "test_key"
        let testString = "sensitive_data_123"
        
        // When
        try KeychainManager.save(key: testKey, string: testString)
        let loadedString = try KeychainManager.loadString(forKey: testKey)
        
        // Then
        XCTAssertEqual(loadedString, testString, "Loaded string should match saved string")
        XCTAssertNil(UserDefaults.standard.string(forKey: testKey), "Data should not be visible in UserDefaults")
    }
    
    /// Tests updating an existing key with a new value
    func testUpdateExistingKey() throws {
        // Given
        let testKey = "test_key"
        let initialString = "initial_value"
        let updatedString = "updated_value"
        
        // When
        try KeychainManager.save(key: testKey, string: initialString)
        try KeychainManager.save(key: testKey, string: updatedString)
        let loadedString = try KeychainManager.loadString(forKey: testKey)
        
        // Then
        XCTAssertEqual(loadedString, updatedString, "Should return updated value")
        XCTAssertNotEqual(loadedString, initialString, "Should not return initial value")
    }
    
    /// Tests deleting a key and verifying it cannot be accessed afterward
    func testDeleteKey() throws {
        // Given
        let testKey = "test_key"
        let testString = "test_value"
        
        // When
        try KeychainManager.save(key: testKey, string: testString)
        try KeychainManager.delete(key: testKey)
        
        // Then
        XCTAssertThrowsError(try KeychainManager.loadString(forKey: testKey)) { error in
            guard let keychainError = error as? KeychainManager.KeychainError else {
                XCTFail("Expected KeychainError type")
                return
            }
            
            switch keychainError {
            case .itemNotFound:
                // Expected error
                break
            default:
                XCTFail("Expected itemNotFound error, got \(keychainError)")
            }
        }
    }
    
    /// Tests attempting to load a non-existent key
    func testNonExistentKey() {
        // Given
        let nonExistentKey = "non_existent_key"
        
        // When/Then
        XCTAssertThrowsError(try KeychainManager.loadString(forKey: nonExistentKey)) { error in
            guard let keychainError = error as? KeychainManager.KeychainError else {
                XCTFail("Expected KeychainError type")
                return
            }
            
            switch keychainError {
            case .itemNotFound:
                // Expected error
                break
            default:
                XCTFail("Expected itemNotFound error, got \(keychainError)")
            }
        }
    }
    
    /// Tests that data persists between app launches
    func testDataPersistence() throws {
        // Given
        let testKey = "test_key"
        let testString = "persistent_data"
        
        // When
        try KeychainManager.save(key: testKey, string: testString)
        
        // Simulate app restart by creating a new load request
        let loadedString = try KeychainManager.loadString(forKey: testKey)
        
        // Then
        XCTAssertEqual(loadedString, testString, "Data should persist between app launches")
    }
    
    /// Tests handling of empty strings
    func testEmptyString() throws {
        // Given
        let testKey = "test_key"
        let emptyString = ""
        
        // When
        try KeychainManager.save(key: testKey, string: emptyString)
        let loadedString = try KeychainManager.loadString(forKey: testKey)
        
        // Then
        XCTAssertEqual(loadedString, emptyString, "Should handle empty strings correctly")
    }
} 