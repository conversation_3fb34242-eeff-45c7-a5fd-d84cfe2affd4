import XCTest
import SwiftUI
@testable import Priority_Matrix

@MainActor
final class ToastManagerTests: XCTestCase {
    var toastManager: ToastManager!
    var window: UIWindow!
    
    override func setUpWithError() throws {
        toastManager = ToastManager.shared
        window = UIWindow(frame: UIScreen.main.bounds)
        window.makeKeyAndVisible()
    }
    
    override func tearDownWithError() throws {
        window = nil
        toastManager = nil
    }
    
    func testShowToast() {
        // 测试显示 Toast
        toastManager.show("Test Message")
        
        // 验证 Toast 状态
        XCTAssertTrue(toastManager.isShowing)
        XCTAssertEqual(toastManager.message, "Test Message")
        
        // 等待 Toast 自动消失
        let expectation = XCTestExpectation(description: "Toast should disappear")
        Task { @MainActor in
            try await Task.sleep(nanoseconds: 2_100_000_000)
            XCTAssertFalse(self.toastManager.isShowing)
            expectation.fulfill()
        }
        wait(for: [expectation], timeout: 3.0)
    }
    
    func testMultipleToasts() {
        // 测试连续显示多个 Toast
        toastManager.show("First Message")
        XCTAssertEqual(toastManager.message, "First Message")
        
        // 立即显示第二个 Toast
        toastManager.show("Second Message")
        XCTAssertEqual(toastManager.message, "Second Message")
        
        // 验证最后一个 Toast 的状态
        XCTAssertTrue(toastManager.isShowing)
    }
    
    func testLongMessage() {
        // 测试长消息
        let longMessage = String(repeating: "Test ", count: 100)
        toastManager.show(longMessage)
        XCTAssertTrue(toastManager.isShowing)
        XCTAssertEqual(toastManager.message, longMessage)
    }
    
    func testToastDismissalTiming() {
        // 测试 Toast 消失时机
        toastManager.show("Test Message")
        XCTAssertTrue(toastManager.isShowing)
        
        // 等待 Toast 自动消失
        let expectation = XCTestExpectation(description: "Toast should auto-dismiss")
        Task { @MainActor in
            try await Task.sleep(nanoseconds: 2_100_000_000)
            XCTAssertFalse(self.toastManager.isShowing)
            expectation.fulfill()
        }
        wait(for: [expectation], timeout: 3.0)
    }
    
    func testToastReshow() {
        // 测试重复显示相同消息
        toastManager.show("Test Message")
        XCTAssertTrue(toastManager.isShowing)
        
        // 等待一小段时间后重新显示
        let expectation = XCTestExpectation(description: "Toast should reshow")
        Task { @MainActor in
            try await Task.sleep(nanoseconds: 500_000_000)
            self.toastManager.show("Test Message")
            XCTAssertTrue(self.toastManager.isShowing)
            XCTAssertEqual(self.toastManager.message, "Test Message")
            expectation.fulfill()
        }
        wait(for: [expectation], timeout: 1.0)
    }
    
    func testNetworkError() {
        // 模拟网络错误
        let networkError = NSError(domain: NSURLErrorDomain, code: NSURLErrorNotConnectedToInternet, userInfo: nil)
        let errorMessage = "Network connection failed. Please check your network settings."
        // TODO: Use localized string
        // let errorMessage = NSLocalizedString("NETWORK_ERROR", comment: "Network connection error message")
        
        toastManager.show(errorMessage)
        
        XCTAssertTrue(toastManager.isShowing)
        XCTAssertEqual(toastManager.message, errorMessage)
        
        let expectation = XCTestExpectation(description: "Error toast should stay longer")
        Task { @MainActor in
            try await Task.sleep(nanoseconds: 3_100_000_000)
            XCTAssertFalse(self.toastManager.isShowing)
            expectation.fulfill()
        }
        wait(for: [expectation], timeout: 4.0)
    }
    
    func testJSONParsingError() {
        let parsingError = "Failed to parse AI analysis result. Please try again."
        // TODO: Use localized string
        // let parsingError = NSLocalizedString("JSON_PARSE_ERROR", comment: "JSON parsing error message")
        
        toastManager.show(parsingError)
        
        XCTAssertTrue(toastManager.isShowing)
        XCTAssertEqual(toastManager.message, parsingError)
    }
    
    func testAIServiceError() {
        let aiError = "AI service is temporarily unavailable. Please try again later."
        // TODO: Use localized string
        // let aiError = NSLocalizedString("AI_SERVICE_ERROR", comment: "AI service error message")
        
        toastManager.show(aiError)
        
        XCTAssertTrue(toastManager.isShowing)
        XCTAssertEqual(toastManager.message, aiError)
    }
    
    func testConcurrentErrors() {
        let error1 = "First error occurred"
        let error2 = "Second error occurred"
        // TODO: Use localized strings
        // let error1 = NSLocalizedString("ERROR_ONE", comment: "First error message")
        // let error2 = NSLocalizedString("ERROR_TWO", comment: "Second error message")
        
        toastManager.show(error1)
        XCTAssertEqual(toastManager.message, error1)
        
        Task { @MainActor in
            self.toastManager.show(error2)
            XCTAssertEqual(self.toastManager.message, error2)
        }
        
        let expectation = XCTestExpectation(description: "Last error should be visible")
        Task { @MainActor in
            try await Task.sleep(nanoseconds: 100_000_000)
            XCTAssertTrue(self.toastManager.isShowing)
            XCTAssertEqual(self.toastManager.message, error2)
            expectation.fulfill()
        }
        wait(for: [expectation], timeout: 1.0)
    }
    
    func testErrorMessageLocalization() {
        // Error messages with their keys for future localization
        let errors = [
            "NETWORK_ERROR": "Network connection failed",
            "SERVICE_ERROR": "Service is unavailable",
            "INVALID_INPUT": "Invalid input provided",
            "PARSE_ERROR": "Failed to parse data",
            "TIMEOUT_ERROR": "Request timed out"
        ]
        
        // TODO: Replace with actual localization
        // let localizedErrors = errors.mapValues { NSLocalizedString($0, comment: "Error message") }
        
        for (_, message) in errors {
            toastManager.show(message)
            XCTAssertEqual(toastManager.message, message)
        }
    }
    
    func testVisualToastDisplay() {
        // Create a test view that shows toast
        let testView = ContentView()
        let hostingController = UIHostingController(rootView: testView)
        window.rootViewController = hostingController
        
        // Show different types of messages with delays
        let expectation = XCTestExpectation(description: "Visual toast test")
        
        // Test normal message
        Task { @MainActor in
            try await Task.sleep(nanoseconds: 500_000_000)
            self.toastManager.show("This is a normal message")
        }
        
        // Test error message
        Task { @MainActor in
            try await Task.sleep(nanoseconds: 3_000_000_000)
            self.toastManager.show("Network connection failed. Please check your network settings.")
        }
        
        // Test long message
        Task { @MainActor in
            try await Task.sleep(nanoseconds: 6_000_000_000)
            self.toastManager.show("This is a very long message that should be displayed properly in the toast view without breaking the layout or becoming unreadable")
        }
        
        // Test multiple quick messages
        Task { @MainActor in
            try await Task.sleep(nanoseconds: 9_000_000_000)
            self.toastManager.show("First quick message")
            try await Task.sleep(nanoseconds: 500_000_000)
            self.toastManager.show("Second quick message")
            try await Task.sleep(nanoseconds: 500_000_000)
            self.toastManager.show("Third quick message")
            expectation.fulfill()
        }
        
        wait(for: [expectation], timeout: 12.0)
    }
    
    func testVisualErrorSequence() {
        // Create a test view that shows toast
        let testView = ContentView()
        let hostingController = UIHostingController(rootView: testView)
        window.rootViewController = hostingController
        
        // Show a sequence of error messages
        let expectation = XCTestExpectation(description: "Visual error sequence test")
        
        Task { @MainActor in
            // Network error
            try await Task.sleep(nanoseconds: 500_000_000)
            self.toastManager.show("Network connection failed")
            
            // Service error
            try await Task.sleep(nanoseconds: 2_500_000_000)
            self.toastManager.show("Service is unavailable")
            
            // Parse error
            try await Task.sleep(nanoseconds: 2_500_000_000)
            self.toastManager.show("Failed to parse data")
            
            // Timeout error
            try await Task.sleep(nanoseconds: 2_500_000_000)
            self.toastManager.show("Request timed out")
            expectation.fulfill()
        }
        
        wait(for: [expectation], timeout: 10.0)
    }
} 
