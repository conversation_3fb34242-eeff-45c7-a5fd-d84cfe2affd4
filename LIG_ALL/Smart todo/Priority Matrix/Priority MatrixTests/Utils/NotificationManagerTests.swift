import XCTest
import UserNotifications
@testable import Priority_Matrix

@MainActor
final class NotificationManagerTests: XCTestCase {
    var notificationManager: NotificationManager!
    var mockTask: UserTask!
    
    override func setUpWithError() throws {
        notificationManager = NotificationManager.shared
        
        // 创建测试任务
        mockTask = UserTask(
            title: "Test Task", 
            description: "This is a test task for notification testing",
            isImportant: true,
            isUrgent: true,
            dueDate: Date().addingTimeInterval(24 * 3600) // 24小时后
        )
    }
    
    override func tearDownWithError() throws {
        // 清理测试通知
        await UNUserNotificationCenter.current().removeAllPendingNotificationRequests()
        await UNUserNotificationCenter.current().removeAllDeliveredNotifications()
        
        notificationManager = nil
        mockTask = nil
    }
    
    // MARK: - 基础功能测试
    
    func testNotificationAuthorizationRequest() async throws {
        // 测试通知权限请求
        do {
            let granted = try await notificationManager.requestAuthorization()
            XCTAssertTrue(granted || !granted, "Authorization request should complete without error")
        } catch {
            XCTFail("Authorization request failed: \\(error.localizedDescription)")
        }
    }
    
    func testNotificationAuthorizationStatus() async {
        // 测试通知权限状态检查
        let status = await notificationManager.checkAuthorizationStatus()
        XCTAssertTrue([.notDetermined, .denied, .authorized, .provisional, .ephemeral].contains(status))
    }
    
    // MARK: - 通知偏好测试
    
    func testNotificationPreferencesDefaultValues() {
        let preferences = NotificationPreferences()
        
        // 测试默认值
        XCTAssertEqual(preferences.notificationFrequency, .normal)
        XCTAssertTrue(preferences.quadrantEnabled[1] == true)
        XCTAssertTrue(preferences.quadrantEnabled[2] == true)
        XCTAssertTrue(preferences.quadrantEnabled[3] == true)
        XCTAssertFalse(preferences.quadrantEnabled[4] == true)
        XCTAssertTrue(preferences.urgentTasksOverrideDND)
        XCTAssertTrue(preferences.enableNotificationAggregation)
        XCTAssertEqual(preferences.aggregationThreshold, 3)
    }
    
    func testUpdateNotificationPreferences() {
        var preferences = NotificationPreferences()
        preferences.notificationFrequency = .aggressive
        preferences.quadrantEnabled[4] = true
        preferences.urgentTasksOverrideDND = false
        
        notificationManager.updatePreferences(preferences)
        
        let updatedPreferences = notificationManager.getPreferences()
        XCTAssertEqual(updatedPreferences.notificationFrequency, .aggressive)
        XCTAssertTrue(updatedPreferences.quadrantEnabled[4] == true)
        XCTAssertFalse(updatedPreferences.urgentTasksOverrideDND)
    }
    
    // MARK: - 象限通知测试
    
    func testQuadrant1NotificationScheduling() async throws {
        // 测试第一象限任务通知调度
        mockTask.isImportant = true
        mockTask.isUrgent = true
        
        try await notificationManager.scheduleNotification(for: mockTask)
        
        let pendingRequests = await UNUserNotificationCenter.current().pendingNotificationRequests()
        let taskNotifications = pendingRequests.filter { $0.identifier.contains(mockTask.id.uuidString) }
        
        // 第一象限应该有多个通知（每4小时一次 + 最后1小时）
        XCTAssertTrue(taskNotifications.count >= 2, "Q1 tasks should have multiple notifications")
    }
    
    func testQuadrant4NotificationDisabled() async throws {
        // 测试第四象限任务不发送通知
        mockTask.isImportant = false
        mockTask.isUrgent = false
        
        try await notificationManager.scheduleNotification(for: mockTask)
        
        let pendingRequests = await UNUserNotificationCenter.current().pendingNotificationRequests()
        let taskNotifications = pendingRequests.filter { $0.identifier.contains(mockTask.id.uuidString) }
        
        // 第四象限默认不发送通知
        XCTAssertEqual(taskNotifications.count, 0, "Q4 tasks should not have notifications by default")
    }
    
    // MARK: - 通知频率测试
    
    func testNotificationFrequencyAdjustment() {
        let baseIntervals: [TimeInterval] = [24 * 3600, 4 * 3600, 1 * 3600] // 24h, 4h, 1h
        
        // 测试不同频率的调整
        let aggressiveMultiplier = NotificationFrequency.aggressive.multiplier
        let gentleMultiplier = NotificationFrequency.gentle.multiplier
        
        XCTAssertLessThan(aggressiveMultiplier, 1.0, "Aggressive frequency should reduce intervals")
        XCTAssertGreaterThan(gentleMultiplier, 1.0, "Gentle frequency should increase intervals")
        
        // 测试调整后的时间间隔
        let aggressiveIntervals = baseIntervals.map { $0 * aggressiveMultiplier }
        let gentleIntervals = baseIntervals.map { $0 * gentleMultiplier }
        
        for i in 0..<baseIntervals.count {
            XCTAssertLessThan(aggressiveIntervals[i], baseIntervals[i])
            XCTAssertGreaterThan(gentleIntervals[i], baseIntervals[i])
        }
    }
    
    // MARK: - 延后功能测试
    
    func testTaskSnoozing() {
        let taskId = mockTask.id.uuidString
        let snoozeFor: TimeInterval = 60 * 60 // 1小时
        
        notificationManager.snoozeTask(taskId, for: snoozeFor)
        
        let preferences = notificationManager.getPreferences()
        let snoozeUntil = preferences.snoozeRecords[taskId]
        
        XCTAssertNotNil(snoozeUntil, "Task should be snoozed")
        XCTAssertTrue(snoozeUntil! > Date(), "Snooze time should be in the future")
        
        let expectedSnoozeTime = Date().addingTimeInterval(snoozeFor)
        let timeDifference = abs(snoozeUntil!.timeIntervalSince(expectedSnoozeTime))
        XCTAssertLessThan(timeDifference, 5, "Snooze time should be approximately correct")
    }
    
    func testCleanupExpiredSnoozeRecords() {
        let taskId = mockTask.id.uuidString
        
        // 添加一个已过期的延后记录
        var preferences = notificationManager.getPreferences()
        preferences.snoozeRecords[taskId] = Date().addingTimeInterval(-3600) // 1小时前
        notificationManager.updatePreferences(preferences)
        
        // 清理过期记录
        notificationManager.cleanupExpiredSnoozeRecords()
        
        let updatedPreferences = notificationManager.getPreferences()
        XCTAssertNil(updatedPreferences.snoozeRecords[taskId], "Expired snooze record should be removed")
    }
    
    // MARK: - 免打扰功能测试
    
    func testDoNotDisturbPeriod() {
        let dndPeriod = DoNotDisturbPeriod(
            startTime: DateComponents(hour: 22, minute: 0),
            endTime: DateComponents(hour: 8, minute: 0),
            isEnabled: true
        )
        
        // 测试当前时间在免打扰时间内
        let calendar = Calendar.current
        let now = Date()
        var nightTime = calendar.dateComponents([.year, .month, .day], from: now)
        nightTime.hour = 23
        nightTime.minute = 0
        
        if let testTime = calendar.date(from: nightTime) {
            // 由于我们无法轻易修改系统时间，这里只测试逻辑结构
            XCTAssertNotNil(dndPeriod.startTime.hour)
            XCTAssertNotNil(dndPeriod.endTime.hour)
            XCTAssertTrue(dndPeriod.isEnabled)
        }
    }
    
    // MARK: - 聚合通知测试
    
    func testNotificationAggregation() {
        let tasks = createMultipleMockTasks(count: 5)
        let aggregation = NotificationAggregation(tasks: tasks)
        
        XCTAssertEqual(aggregation.totalCount, 5)
        XCTAssertNotNil(aggregation.mostUrgentTask)
        XCTAssertFalse(aggregation.quadrantCounts.isEmpty)
        
        // 验证最紧急任务选择
        let mostUrgent = aggregation.mostUrgentTask!
        XCTAssertTrue(mostUrgent.quadrant <= aggregation.tasks.map { $0.quadrant }.min()!)
    }
    
    func testAggregationThreshold() async throws {
        // 测试聚合阈值
        var preferences = notificationManager.getPreferences()
        preferences.enableNotificationAggregation = true
        preferences.aggregationThreshold = 3
        notificationManager.updatePreferences(preferences)
        
        let tasks = createMultipleMockTasks(count: 2)
        
        // 创建少于阈值的任务，应该分别发送通知
        for task in tasks {
            try await notificationManager.scheduleNotification(for: task)
        }
        
        // 等待聚合处理
        try await Task.sleep(nanoseconds: 1_000_000_000) // 1秒
        
        let pendingRequests = await UNUserNotificationCenter.current().pendingNotificationRequests()
        let aggregatedNotifications = pendingRequests.filter { $0.identifier.hasPrefix("aggregated-") }
        
        // 由于任务数少于阈值，不应该有聚合通知
        XCTAssertEqual(aggregatedNotifications.count, 0, "Should not create aggregated notification when below threshold")
    }
    
    // MARK: - 通知取消测试
    
    func testNotificationCancellation() async throws {
        // 先调度通知
        try await notificationManager.scheduleNotification(for: mockTask)
        
        let initialRequests = await UNUserNotificationCenter.current().pendingNotificationRequests()
        let initialTaskNotifications = initialRequests.filter { $0.identifier.contains(mockTask.id.uuidString) }
        XCTAssertGreaterThan(initialTaskNotifications.count, 0, "Should have scheduled notifications")
        
        // 取消通知
        await notificationManager.cancelNotification(for: mockTask)
        
        let finalRequests = await UNUserNotificationCenter.current().pendingNotificationRequests()
        let finalTaskNotifications = finalRequests.filter { $0.identifier.contains(mockTask.id.uuidString) }
        XCTAssertEqual(finalTaskNotifications.count, 0, "All task notifications should be cancelled")
    }
    
    // MARK: - 错误处理测试
    
    func testNotificationWithoutDueDate() async throws {
        // 测试无截止日期的任务通知
        let noDueDateTask = UserTask(
            title: "No Due Date Task",
            description: "Task without due date",
            isImportant: true,
            isUrgent: false
        )
        
        try await notificationManager.scheduleNotification(for: noDueDateTask)
        
        let pendingRequests = await UNUserNotificationCenter.current().pendingNotificationRequests()
        let taskNotifications = pendingRequests.filter { $0.identifier.contains(noDueDateTask.id.uuidString) }
        
        // 应该有至少一个提醒设置截止日期的通知
        XCTAssertGreaterThan(taskNotifications.count, 0, "Should schedule reminder for tasks without due date")
        
        // 验证通知内容包含设置截止日期的提示
        if let notification = taskNotifications.first {
            XCTAssertTrue(notification.content.body.contains("deadline") || 
                         notification.content.body.contains("截止"), 
                         "Notification should mention deadline")
        }
    }
    
    // MARK: - 辅助方法
    
    private func createMultipleMockTasks(count: Int) -> [UserTask] {
        var tasks: [UserTask] = []
        
        for i in 0..<count {
            let task = UserTask(
                title: "Test Task \\(i + 1)",
                description: "Test task \\(i + 1) description",
                isImportant: i % 2 == 0,
                isUrgent: i % 3 == 0,
                dueDate: Date().addingTimeInterval(TimeInterval((i + 1) * 3600))
            )
            tasks.append(task)
        }
        
        return tasks
    }
    
    // MARK: - 性能测试
    
    func testNotificationSchedulingPerformance() throws {
        let tasks = createMultipleMockTasks(count: 100)
        
        measure {
            Task {
                for task in tasks {
                    try? await notificationManager.scheduleNotification(for: task)
                }
            }
        }
    }
    
    func testNotificationCancellationPerformance() throws {
        let tasks = createMultipleMockTasks(count: 50)
        
        // 首先调度所有通知
        Task {
            for task in tasks {
                try? await notificationManager.scheduleNotification(for: task)
            }
        }
        
        // 测试取消性能
        measure {
            Task {
                for task in tasks {
                    await notificationManager.cancelNotification(for: task)
                }
            }
        }
    }
}