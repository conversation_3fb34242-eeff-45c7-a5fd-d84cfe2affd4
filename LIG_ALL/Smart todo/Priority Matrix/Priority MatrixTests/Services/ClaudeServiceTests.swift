import XCTest
@testable import Priority_Matrix

final class ClaudeServiceTests: XCTestCase {
    var service: ClaudeService!
    
    override func setUpWithError() throws {
        try super.setUpWithError()
        
        // Set up test environment API key
        if let apiKey = ProcessInfo.processInfo.environment["CLAUDE_API_KEY_VALUE"] {
            try KeychainManager.save(key: "CLAUDE_API_KEY", string: apiKey)
        } else {
            XCTFail("Missing CLAUDE_API_KEY_VALUE in test environment")
        }
        
        service = try ClaudeService()
    }
    
    override func tearDownWithError() throws {
        // Clean up test data
        try? KeychainManager.delete(key: "CLAUDE_API_KEY")
        service = nil
        try super.tearDownWithError()
    }
    
    func testServiceConfiguration() throws {
        XCTAssertEqual(service.configuration.provider, .claude)
        XCTAssertEqual(service.configuration.model, "claude-3-sonnet-20240229")
        XCTAssertFalse(service.configuration.apiKey.isEmpty)
        
        // Test headers
        let headers = service.configuration.headers
        XCTAssertEqual(headers["Content-Type"], "application/json")
        XCTAssertEqual(headers["anthropic-version"], "2023-06-01")
        XCTAssertNotNil(headers["x-api-key"])
        
        // Test parameters
        let parameters = service.configuration.parameters
        XCTAssertEqual(parameters["model"] as? String, "claude-3-sonnet-20240229")
        XCTAssertEqual(parameters["max_tokens"] as? Int, 1024)
        XCTAssertNotNil(parameters["messages"])
    }
    
    func testPromptGeneration() {
        let input = "Test task"
        let prompt = service.generatePrompt(for: input)
        
        // Verify prompt contains all required components
        XCTAssertTrue(prompt.contains("Eisenhower Matrix"))
        XCTAssertTrue(prompt.contains("Importance Definition"))
        XCTAssertTrue(prompt.contains("Urgency Definition"))
        XCTAssertTrue(prompt.contains("Task Description: \(input)"))
        XCTAssertTrue(prompt.contains("Analysis Requirements"))
        XCTAssertTrue(prompt.contains("JSON format"))
    }
    
    func testAnalyzeTask() async throws {
        // Ensure API key is available
        guard ProcessInfo.processInfo.environment["CLAUDE_API_KEY_VALUE"] != nil else {
            XCTFail("Test requires CLAUDE_API_KEY_VALUE environment variable")
            return
        }
        
        // Test data
        let input = "明天下午3点需要准备项目演示PPT，这个演示对客户很重要，但是还有两天时间。"
        
        // Perform analysis
        let result = try await service.analyzeTask(input)
        
        // Verify basic fields
        XCTAssertFalse(result.title.isEmpty, "Title should not be empty")
        XCTAssertFalse(result.taskDescription.isEmpty, "Description should not be empty")
        XCTAssertFalse(result.hiddenValue.isEmpty, "Hidden value should not be empty")
        XCTAssertFalse(result.suggestions.isEmpty, "Suggestions should not be empty")
        XCTAssertGreaterThanOrEqual(result.suggestions.count, 1, "Should have at least one suggestion")
        XCTAssertFalse(result.explanation.isEmpty, "Explanation should not be empty")
        
        // Log results for manual verification
        print("Analysis Results:")
        print("Title: \(result.title)")
        print("Description: \(result.taskDescription)")
        print("Importance: \(result.importance)")
        print("Urgency: \(result.urgency)")
        print("Hidden Value: \(result.hiddenValue)")
        print("Suggestions:")
        result.suggestions.enumerated().forEach { index, suggestion in
            print("\(index + 1). \(suggestion)")
        }
        print("Explanation: \(result.explanation)")
    }
    
    func testEmptyInput() async {
        // Test empty input
        let emptyInput = ""
        
        do {
            _ = try await service.analyzeTask(emptyInput)
            XCTFail("Empty input should throw an error")
        } catch let error as AIServiceError {
            XCTAssertEqual(error, AIServiceError.emptyInput)
        } catch {
            XCTFail("Unexpected error type: \(error)")
        }
    }
    
    func testWhitespaceInput() async {
        // Test whitespace-only input
        let whitespaceInput = "   \n   \t   "
        
        do {
            _ = try await service.analyzeTask(whitespaceInput)
            XCTFail("Whitespace-only input should throw an error")
        } catch let error as AIServiceError {
            XCTAssertEqual(error, AIServiceError.emptyInput)
        } catch {
            XCTFail("Unexpected error type: \(error)")
        }
    }
} 
