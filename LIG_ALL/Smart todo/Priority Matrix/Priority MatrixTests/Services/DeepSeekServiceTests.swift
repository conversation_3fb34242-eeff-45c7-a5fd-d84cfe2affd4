import XCTest
@testable import Priority_Matrix

final class DeepSeekServiceTests: XCTestCase {
    var service: DeepSeekService!
    
    override func setUpWithError() throws {
        try super.setUpWithError()
        service = try DeepSeekService()
    }
    
    override func tearDownWithError() throws {
        service = nil
        try super.tearDownWithError()
    }
    
    // 测试提示生成
    func testPromptGeneration() {
        // 测试中文输入
        let chineseInput = "完成iOS项目开发"
        let chineseSystemPrompt = service.generateSystemPrompt(for: chineseInput)
        let chineseUserPrompt = service.generateUserPrompt(for: chineseInput)
        
        // 验证中文提示
        XCTAssertTrue(chineseSystemPrompt.contains("你是一个专业的任务分析助手"))
        XCTAssertTrue(chineseSystemPrompt.contains("艾森豪威尔矩阵"))
        XCTAssertTrue(chineseUserPrompt.contains("请分析以下任务"))
        
        // 测试英文输入
        let englishInput = "Complete iOS project development"
        let englishSystemPrompt = service.generateSystemPrompt(for: englishInput)
        let englishUserPrompt = service.generateUserPrompt(for: englishInput)
        
        // 验证英文提示
        XCTAssertTrue(englishSystemPrompt.contains("You are a professional task analysis assistant"))
        XCTAssertTrue(englishSystemPrompt.contains("Eisenhower Matrix"))
        XCTAssertTrue(englishUserPrompt.contains("Please analyze the following task"))
        
        // 打印生成的提示供检查
        print("\n=== Generated Prompts ===")
        print("Chinese System Prompt:", chineseSystemPrompt)
        print("\nChinese User Prompt:", chineseUserPrompt)
        print("\nEnglish System Prompt:", englishSystemPrompt)
        print("\nEnglish User Prompt:", englishUserPrompt)
        print("=== End of Prompts ===\n")
    }
    
    // 测试任务分析
    func testTaskAnalysis() async throws {
        // 测试空输入
        do {
            _ = try await service.analyzeTask("")
            XCTFail("Should throw error for empty input")
        } catch AIServiceError.emptyInput {
            // Expected error
        }
        
        // 测试实际任务分析
        let input = "完成iOS项目的用户界面设计，截止日期是明天下午3点"
        do {
            let response = try await service.analyzeTask(input)
            
            // 验证返回格式
            XCTAssertFalse(response.title.isEmpty)
            XCTAssertFalse(response.taskDescription.isEmpty)
            XCTAssertFalse(response.suggestions.isEmpty)
            
            // 创建完整的JSON格式输出
            let jsonOutput: [String: Any] = [
                "title": response.title,
                "description": response.taskDescription,
                "importance": response.importance,
                "urgency": response.urgency,
                "hidden_value": response.hiddenValue,
                "suggestions": response.suggestions,
                "explanation": response.explanation
            ]
            
            // 转换为格式化的JSON字符串
            let jsonData = try JSONSerialization.data(withJSONObject: jsonOutput, options: [.prettyPrinted])
            if let jsonString = String(data: jsonData, encoding: .utf8) {
                print("\n=== Analysis Result (JSON Format) ===")
                print(jsonString)
                print("=== End of Analysis Result ===\n")
            }
            
        } catch AIServiceError.missingAPIKey {
            print("⚠️ Skipping API test - missing API key")
        } catch {
            print("❌ Error:", error)
        }
    }
} 