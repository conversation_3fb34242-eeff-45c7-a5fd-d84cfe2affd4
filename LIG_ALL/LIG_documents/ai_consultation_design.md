# AI问诊系统详细设计

## 一、AI对话问答设计

### Opening
```
AI："你好，我是你的过敏健康助手。为了更好地了解你的情况，我需要和你聊聊。你可以随时告诉我需要暂停或返回上一步。准备好了吗？"
```

### 会话流程
1. 基础信息收集
   - 年龄范围
   - 过敏史时长
   - 是否确诊过敏性鼻炎
   - 是否有其他过敏史

2. 症状评估
   - 主要症状描述
   - 症状类型细分
   - 症状频率
   - 症状严重程度
   - 生活影响程度

3. 触发因素分析
   - 季节变化
   - 特定环境
   - 时间规律
   - 其他诱因

4. 用药历史
   - 现有治疗方式
   - 效果反馈
   - 副作用情况

## 二、症状评估标准

### 严重程度评分（0-3分制）
1. 鼻塞
   - 0分：无症状
   - 1分：轻微，不影响日常
   - 2分：明显，影响日常
   - 3分：严重，严重影响生活

2. 流涕
   - [类似评分标准]

3. 打喷嚏
   - [类似评分标准]

### 影响评估
- 睡眠质量影响
- 日间工作影响
- 户外活动影响
- 情绪影响

### 频率评估
- 持续性：每周4天以上
- 间歇性：每周4天以下
- 季节性：特定季节发作

## 三、建议生成逻辑

### 基础建议模块
```
IF 症状评分 > 2 AND 未确诊:
   建议就医确诊
   提供线下医院推荐

IF 症状为间歇性 AND 有明确触发因素:
   生成预防建议
   提供环境改善方案

IF 症状为持续性:
   建议进行过敏原检测
   提供日常防护建议
```

### 生活建议模块
1. 环境改善建议
   - 房间通风方案
   - 床品清洁建议
   - 空气净化建议

2. 日常防护建议
   - 外出防护措施
   - 室内防护措施
   - 生活习惯调整

### 随访规划
```
IF 症状严重程度 >= 2:
   每周随访
ELSE IF 症状严重程度 = 1:
   每两周随访
ELSE:
   每月随访
```