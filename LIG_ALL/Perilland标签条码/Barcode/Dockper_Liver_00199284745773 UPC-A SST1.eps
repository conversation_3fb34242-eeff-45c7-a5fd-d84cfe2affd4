%!PS-Adobe-3.0 EPSF-3.0
%%BoundingBox: 0 0 110 74
%%Title: 00199284745773 UPCA SST1
%%Creator: GS1US.BarcodeGenerator/1.1.61+fcdcbb9d05
%%CreationDate: 2025-03-07T22:19:28.4516586Z
%%Pages: 0
%%EndComments
0 74 translate
0.06 -0.06 scale
newpath
698 1152 moveto
688 1148 680 1141 677 1132 curveto
673 1119 678 1104 691 1095 curveto
695 1091 lineto
690 1087 lineto
682 1079 678 1069 681 1060 curveto
683 1053 687 1048 695 1044 curveto
701 1041 703 1041 711 1041 curveto
719 1041 721 1041 726 1044 curveto
737 1049 742 1056 741 1068 curveto
741 1076 738 1081 731 1087 curveto
726 1091 lineto
731 1094 lineto
752 1111 752 1138 729 1150 curveto
721 1154 707 1155 698 1152 curveto
closepath
723 1137 moveto
728 1134 732 1130 732 1125 curveto
733 1117 726 1108 715 1101 curveto
711 1098 lineto
706 1101 lineto
693 1109 687 1120 691 1128 curveto
696 1138 711 1142 723 1137 curveto
closepath
718 1079 moveto
726 1074 730 1066 725 1061 curveto
722 1057 718 1055 711 1056 curveto
701 1056 695 1060 695 1066 curveto
694 1071 698 1076 704 1079 curveto
710 1083 711 1083 718 1079 curveto
closepath
fill
newpath
1002 1152 moveto
997 1150 996 1145 998 1129 curveto
1000 1110 1005 1097 1018 1081 curveto
1023 1075 1029 1067 1031 1064 curveto
1035 1058 lineto
1009 1058 lineto
983 1057 lineto
981 1054 lineto
978 1051 978 1048 981 1045 curveto
983 1043 lineto
1015 1043 lineto
1046 1043 lineto
1048 1045 lineto
1051 1047 1051 1048 1050 1053 curveto
1049 1061 1044 1071 1033 1085 curveto
1018 1105 1013 1115 1012 1137 curveto
1011 1146 1010 1149 1009 1150 curveto
1007 1153 1005 1153 1002 1152 curveto
closepath
fill
newpath
1200 1152 moveto
1198 1150 1198 1148 1198 1146 curveto
1198 1141 1200 1140 1212 1139 curveto
1234 1137 1245 1128 1245 1113 curveto
1245 1104 1242 1099 1235 1096 curveto
1230 1093 1228 1093 1217 1093 curveto
1201 1092 1200 1092 1201 1081 curveto
1201 1076 1201 1066 1201 1060 curveto
1203 1042 1201 1043 1231 1043 curveto
1254 1043 lineto
1256 1045 lineto
1259 1048 1259 1051 1257 1055 curveto
1255 1057 1254 1057 1236 1057 curveto
1214 1058 1216 1057 1216 1070 curveto
1216 1078 lineto
1224 1079 lineto
1245 1080 1258 1092 1259 1111 curveto
1261 1134 1242 1151 1212 1153 curveto
1204 1154 1203 1154 1200 1152 curveto
closepath
fill
newpath
1324 1152 moveto
1319 1150 1318 1145 1320 1129 curveto
1322 1110 1327 1097 1340 1081 curveto
1345 1075 1351 1067 1353 1064 curveto
1357 1058 lineto
1331 1058 lineto
1305 1057 lineto
1303 1054 lineto
1300 1051 1300 1048 1303 1045 curveto
1305 1043 lineto
1337 1043 lineto
1368 1043 lineto
1370 1045 lineto
1373 1047 1373 1048 1372 1053 curveto
1371 1061 1366 1071 1355 1085 curveto
1340 1105 1335 1115 1334 1137 curveto
1333 1146 1332 1149 1331 1150 curveto
1329 1153 1327 1153 1324 1152 curveto
closepath
fill
newpath
1431 1152 moveto
1426 1150 1425 1145 1427 1129 curveto
1429 1110 1434 1097 1447 1081 curveto
1452 1075 1458 1067 1460 1064 curveto
1464 1058 lineto
1438 1058 lineto
1412 1057 lineto
1410 1054 lineto
1407 1051 1407 1048 1410 1045 curveto
1412 1043 lineto
1444 1043 lineto
1475 1043 lineto
1477 1045 lineto
1480 1047 1480 1048 1479 1053 curveto
1478 1061 1473 1071 1462 1085 curveto
1447 1105 1442 1115 1441 1137 curveto
1440 1146 1439 1149 1438 1150 curveto
1436 1153 1434 1153 1431 1152 curveto
closepath
fill
newpath
1729 1152 moveto
1716 1149 1711 1145 1713 1139 curveto
1715 1135 1718 1134 1727 1136 curveto
1742 1141 1756 1139 1763 1131 curveto
1768 1126 1769 1122 1768 1114 curveto
1767 1105 1760 1099 1745 1097 curveto
1738 1096 1736 1095 1734 1093 curveto
1731 1089 1732 1087 1746 1072 curveto
1759 1058 lineto
1738 1058 lineto
1720 1057 1717 1057 1716 1055 curveto
1713 1052 1713 1047 1716 1045 curveto
1719 1043 1720 1043 1747 1043 curveto
1775 1043 lineto
1777 1045 lineto
1779 1047 1780 1048 1780 1052 curveto
1780 1057 lineto
1768 1070 lineto
1755 1083 1754 1084 1757 1085 curveto
1762 1085 1776 1095 1779 1101 curveto
1781 1105 1783 1113 1783 1118 curveto
1784 1123 1780 1133 1776 1138 curveto
1768 1150 1748 1156 1729 1152 curveto
closepath
fill
newpath
76 1151 moveto
74 1150 74 1148 74 1105 curveto
73 1060 lineto
64 1068 lineto
58 1073 53 1077 52 1077 curveto
46 1079 42 1073 43 1068 curveto
44 1067 51 1060 58 1054 curveto
73 1042 lineto
78 1042 lineto
82 1042 84 1042 86 1044 curveto
87 1045 88 1047 88 1097 curveto
88 1147 87 1149 86 1151 curveto
83 1153 79 1153 76 1151 curveto
closepath
fill
newpath
374 1151 moveto
371 1147 372 1144 380 1135 curveto
386 1128 399 1113 398 1112 curveto
398 1112 394 1112 389 1112 curveto
381 1112 379 1111 374 1109 curveto
363 1104 357 1095 355 1083 curveto
352 1067 361 1051 376 1044 curveto
381 1041 383 1041 391 1041 curveto
398 1041 400 1042 405 1044 curveto
428 1055 433 1082 415 1113 curveto
408 1125 400 1135 391 1144 curveto
384 1152 383 1153 379 1153 curveto
377 1153 375 1152 374 1151 curveto
closepath
402 1096 moveto
408 1094 410 1089 411 1081 curveto
412 1065 405 1056 390 1056 curveto
378 1055 370 1064 369 1076 curveto
368 1084 372 1092 378 1095 curveto
383 1098 396 1098 402 1096 curveto
closepath
fill
newpath
481 1151 moveto
478 1147 479 1144 487 1135 curveto
493 1128 506 1113 505 1112 curveto
505 1112 501 1112 496 1112 curveto
488 1112 486 1111 481 1109 curveto
470 1104 464 1095 462 1083 curveto
459 1067 468 1051 483 1044 curveto
488 1041 490 1041 498 1041 curveto
505 1041 507 1042 512 1044 curveto
535 1055 540 1082 522 1113 curveto
515 1125 507 1135 498 1144 curveto
491 1152 490 1153 486 1153 curveto
484 1153 482 1152 481 1151 curveto
closepath
509 1096 moveto
515 1094 517 1089 518 1081 curveto
519 1065 512 1056 497 1056 curveto
485 1055 477 1064 476 1076 curveto
475 1084 479 1092 485 1095 curveto
490 1098 503 1098 509 1096 curveto
closepath
fill
newpath
827 1151 moveto
826 1149 825 1146 825 1139 curveto
824 1129 lineto
807 1129 lineto
788 1129 784 1128 783 1123 curveto
781 1117 783 1111 799 1078 curveto
807 1060 815 1044 816 1043 curveto
820 1039 828 1042 828 1048 curveto
828 1049 821 1064 813 1081 curveto
805 1098 799 1112 799 1113 curveto
798 1113 804 1114 811 1114 curveto
824 1114 lineto
825 1104 lineto
826 1093 827 1091 832 1091 curveto
838 1091 840 1093 840 1104 curveto
840 1114 lineto
844 1114 lineto
850 1114 852 1115 854 1118 curveto
856 1124 851 1129 843 1129 curveto
840 1129 lineto
840 1139 lineto
840 1147 839 1149 838 1151 curveto
835 1153 829 1153 827 1151 curveto
closepath
fill
newpath
1130 1151 moveto
1129 1149 1128 1146 1128 1139 curveto
1127 1129 lineto
1110 1129 lineto
1091 1129 1087 1128 1086 1123 curveto
1084 1117 1086 1111 1102 1078 curveto
1110 1060 1118 1044 1119 1043 curveto
1123 1039 1131 1042 1131 1048 curveto
1131 1049 1124 1064 1116 1081 curveto
1108 1098 1102 1112 1102 1113 curveto
1101 1113 1107 1114 1114 1114 curveto
1127 1114 lineto
1128 1104 lineto
1129 1093 1130 1091 1135 1091 curveto
1141 1091 1143 1093 1143 1104 curveto
1143 1114 lineto
1147 1114 lineto
1153 1114 1155 1115 1157 1118 curveto
1159 1124 1154 1129 1146 1129 curveto
1143 1129 lineto
1143 1139 lineto
1143 1147 1142 1149 1141 1151 curveto
1138 1153 1132 1153 1130 1151 curveto
closepath
fill
newpath
576 1150 moveto
572 1149 571 1143 572 1133 curveto
574 1117 580 1109 599 1097 curveto
605 1093 612 1088 614 1086 curveto
624 1076 624 1065 614 1058 curveto
612 1056 610 1056 602 1056 curveto
592 1056 591 1056 585 1059 curveto
578 1063 577 1063 575 1061 curveto
571 1060 570 1055 571 1052 curveto
573 1049 585 1043 594 1041 curveto
614 1038 631 1047 635 1064 curveto
639 1080 631 1093 608 1108 curveto
593 1117 587 1126 587 1135 curveto
587 1138 lineto
610 1138 lineto
631 1138 633 1138 635 1139 curveto
637 1142 637 1147 634 1149 curveto
631 1151 630 1151 605 1151 curveto
588 1151 577 1151 576 1150 curveto
closepath
fill
newpath
153 540 moveto
153 0 lineto
161 0 lineto
169 0 lineto
169 540 lineto
169 1080 lineto
161 1080 lineto
153 1080 lineto
153 540 lineto
closepath
fill
newpath
185 540 moveto
185 0 lineto
193 0 lineto
201 0 lineto
201 540 lineto
201 1080 lineto
193 1080 lineto
185 1080 lineto
185 540 lineto
closepath
fill
newpath
233 540 moveto
233 0 lineto
249 0 lineto
265 0 lineto
265 540 lineto
265 1080 lineto
249 1080 lineto
233 1080 lineto
233 540 lineto
closepath
fill
newpath
297 540 moveto
297 0 lineto
305 0 lineto
313 0 lineto
313 540 lineto
313 1080 lineto
305 1080 lineto
297 1080 lineto
297 540 lineto
closepath
fill
newpath
889 540 moveto
889 0 lineto
897 0 lineto
905 0 lineto
905 540 lineto
905 1080 lineto
897 1080 lineto
889 1080 lineto
889 540 lineto
closepath
fill
newpath
921 540 moveto
921 0 lineto
929 0 lineto
937 0 lineto
937 540 lineto
937 1080 lineto
929 1080 lineto
921 1080 lineto
921 540 lineto
closepath
fill
newpath
1513 540 moveto
1513 0 lineto
1521 0 lineto
1529 0 lineto
1529 540 lineto
1529 1080 lineto
1521 1080 lineto
1513 1080 lineto
1513 540 lineto
closepath
fill
newpath
1593 540 moveto
1593 0 lineto
1601 0 lineto
1609 0 lineto
1609 540 lineto
1609 1080 lineto
1601 1080 lineto
1593 1080 lineto
1593 540 lineto
closepath
fill
newpath
1625 540 moveto
1625 0 lineto
1633 0 lineto
1641 0 lineto
1641 540 lineto
1641 1080 lineto
1633 1080 lineto
1625 1080 lineto
1625 540 lineto
closepath
fill
newpath
1657 540 moveto
1657 0 lineto
1665 0 lineto
1673 0 lineto
1673 540 lineto
1673 1080 lineto
1665 1080 lineto
1657 1080 lineto
1657 540 lineto
closepath
fill
newpath
361 509 moveto
361 0 lineto
369 0 lineto
377 0 lineto
377 509 lineto
377 1018 lineto
369 1018 lineto
361 1018 lineto
361 509 lineto
closepath
fill
newpath
393 509 moveto
393 0 lineto
409 0 lineto
425 0 lineto
425 509 lineto
425 1018 lineto
409 1018 lineto
393 1018 lineto
393 509 lineto
closepath
fill
newpath
473 509 moveto
473 0 lineto
481 0 lineto
489 0 lineto
489 509 lineto
489 1018 lineto
481 1018 lineto
473 1018 lineto
473 509 lineto
closepath
fill
newpath
505 509 moveto
505 0 lineto
521 0 lineto
537 0 lineto
537 509 lineto
537 1018 lineto
521 1018 lineto
505 1018 lineto
505 509 lineto
closepath
fill
newpath
569 509 moveto
569 0 lineto
577 0 lineto
585 0 lineto
585 509 lineto
585 1018 lineto
577 1018 lineto
569 1018 lineto
569 509 lineto
closepath
fill
newpath
617 509 moveto
617 0 lineto
633 0 lineto
649 0 lineto
649 509 lineto
649 1018 lineto
633 1018 lineto
617 1018 lineto
617 509 lineto
closepath
fill
newpath
665 509 moveto
665 0 lineto
681 0 lineto
697 0 lineto
697 509 lineto
697 1018 lineto
681 1018 lineto
665 1018 lineto
665 509 lineto
closepath
fill
newpath
713 509 moveto
713 0 lineto
737 0 lineto
761 0 lineto
761 509 lineto
761 1018 lineto
737 1018 lineto
713 1018 lineto
713 509 lineto
closepath
fill
newpath
777 509 moveto
777 0 lineto
785 0 lineto
793 0 lineto
793 509 lineto
793 1018 lineto
785 1018 lineto
777 1018 lineto
777 509 lineto
closepath
fill
newpath
841 509 moveto
841 0 lineto
857 0 lineto
873 0 lineto
873 509 lineto
873 1018 lineto
857 1018 lineto
841 1018 lineto
841 509 lineto
closepath
fill
newpath
953 509 moveto
953 0 lineto
961 0 lineto
969 0 lineto
969 509 lineto
969 1018 lineto
961 1018 lineto
953 1018 lineto
953 509 lineto
closepath
fill
newpath
1017 509 moveto
1017 0 lineto
1025 0 lineto
1033 0 lineto
1033 509 lineto
1033 1018 lineto
1025 1018 lineto
1017 1018 lineto
1017 509 lineto
closepath
fill
newpath
1065 509 moveto
1065 0 lineto
1073 0 lineto
1081 0 lineto
1081 509 lineto
1081 1018 lineto
1073 1018 lineto
1065 1018 lineto
1065 509 lineto
closepath
fill
newpath
1097 509 moveto
1097 0 lineto
1121 0 lineto
1145 0 lineto
1145 509 lineto
1145 1018 lineto
1121 1018 lineto
1097 1018 lineto
1097 509 lineto
closepath
fill
newpath
1177 509 moveto
1177 0 lineto
1185 0 lineto
1193 0 lineto
1193 509 lineto
1193 1018 lineto
1185 1018 lineto
1177 1018 lineto
1177 509 lineto
closepath
fill
newpath
1225 509 moveto
1225 0 lineto
1249 0 lineto
1273 0 lineto
1273 509 lineto
1273 1018 lineto
1249 1018 lineto
1225 1018 lineto
1225 509 lineto
closepath
fill
newpath
1289 509 moveto
1289 0 lineto
1297 0 lineto
1305 0 lineto
1305 509 lineto
1305 1018 lineto
1297 1018 lineto
1289 1018 lineto
1289 509 lineto
closepath
fill
newpath
1353 509 moveto
1353 0 lineto
1361 0 lineto
1369 0 lineto
1369 509 lineto
1369 1018 lineto
1361 1018 lineto
1353 1018 lineto
1353 509 lineto
closepath
fill
newpath
1401 509 moveto
1401 0 lineto
1409 0 lineto
1417 0 lineto
1417 509 lineto
1417 1018 lineto
1409 1018 lineto
1401 1018 lineto
1401 509 lineto
closepath
fill
newpath
1465 509 moveto
1465 0 lineto
1473 0 lineto
1481 0 lineto
1481 509 lineto
1481 1018 lineto
1473 1018 lineto
1465 1018 lineto
1465 509 lineto
closepath
fill
showpage
%%EOF
