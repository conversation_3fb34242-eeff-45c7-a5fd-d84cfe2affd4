'use server';

import { ClaudeService } from '@/services/claude';

export async function processMessage(message: string): Promise<{ success: boolean; response?: string; error?: string }> {
  try {
    const claude = new ClaudeService();
    const response = await claude.sendMessage([
      {
        text: message,
        isUser: true,
        pending: false
      }
    ]);
    
    return {
      success: true,
      response
    };
  } catch (error) {
    console.error('Error processing message:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'An unknown error occurred'
    };
  }
}