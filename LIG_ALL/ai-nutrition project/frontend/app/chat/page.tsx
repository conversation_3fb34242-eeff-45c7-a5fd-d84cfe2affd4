import { Suspense } from 'react';
import { Navbar } from '@/components/navbar';
import { ChatContainer } from '@/components/chat/chat-container';
import { Skeleton } from '@/components/ui/skeleton';

function ChatSkeleton() {
  return (
    <div className="flex flex-col h-[calc(100vh-8rem)] overflow-hidden">
      <Skeleton className="flex-1" />
    </div>
  );
}

export default function ChatPage() {
  return (
    <div className="min-h-screen bg-background">
      <Navbar />
      <main className="pt-16">
        <Suspense fallback={<ChatSkeleton />}>
          <ChatContainer />
        </Suspense>
      </main>
    </div>
  );
}