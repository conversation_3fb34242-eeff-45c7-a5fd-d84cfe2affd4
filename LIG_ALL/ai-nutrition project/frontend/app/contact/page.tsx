import { Navbar } from '@/components/navbar';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Mail, MessageSquare, Phone } from 'lucide-react';

export default function Contact() {
  return (
    <div className="min-h-screen bg-background">
      <Navbar />
      <main className="py-24">
        <div className="mx-auto max-w-7xl px-6 lg:px-8">
          <div className="mx-auto max-w-2xl lg:text-center">
            <h2 className="text-base font-semibold leading-7 text-primary">Contact Us</h2>
            <p className="mt-2 text-3xl font-bold tracking-tight sm:text-4xl">
              Get in Touch
            </p>
            <p className="mt-6 text-lg leading-8 text-muted-foreground">
              Have questions? We're here to help. Reach out to us through any of the channels below.
            </p>
          </div>

          <div className="mx-auto mt-16 max-w-2xl">
            <Card>
              <CardHeader>
                <CardTitle>Send us a message</CardTitle>
              </CardHeader>
              <CardContent>
                <form className="space-y-6">
                  <div className="space-y-2">
                    <Input placeholder="Your Name" />
                  </div>
                  <div className="space-y-2">
                    <Input type="email" placeholder="Your Email" />
                  </div>
                  <div className="space-y-2">
                    <Textarea placeholder="Your Message" className="min-h-[150px]" />
                  </div>
                  <Button className="w-full">Send Message</Button>
                </form>
              </CardContent>
            </Card>

            <div className="mt-12 grid grid-cols-1 gap-6 sm:grid-cols-3">
              <Card>
                <CardContent className="pt-6">
                  <div className="flex flex-col items-center text-center">
                    <Mail className="h-8 w-8 text-primary mb-4" />
                    <h3 className="font-medium">Email</h3>
                    <p className="text-sm text-muted-foreground mt-2">
                      <EMAIL>
                    </p>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="pt-6">
                  <div className="flex flex-col items-center text-center">
                    <Phone className="h-8 w-8 text-primary mb-4" />
                    <h3 className="font-medium">Phone</h3>
                    <p className="text-sm text-muted-foreground mt-2">
                      +****************
                    </p>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="pt-6">
                  <div className="flex flex-col items-center text-center">
                    <MessageSquare className="h-8 w-8 text-primary mb-4" />
                    <h3 className="font-medium">Live Chat</h3>
                    <p className="text-sm text-muted-foreground mt-2">
                      Available 24/7
                    </p>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}