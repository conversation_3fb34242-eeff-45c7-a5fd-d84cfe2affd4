'use client';

import { useSearchParams, useRouter } from 'next/navigation';
import { useState, useEffect } from 'react';
import { Navbar } from '@/components/navbar';
import { Progress } from '@/components/ui/progress';
import { QuestionCard } from '@/components/quiz/question-card';
import { Recommendations } from '@/components/quiz/recommendations';
import { healthConcernFlows } from '@/lib/constants/quiz-flow';
import { AnimatePresence } from 'framer-motion';
import { type HealthConcern } from '@/lib/types/quiz';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';

export default function QuizContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const concernParam = searchParams.get('concern') as HealthConcern | null;
  
  const [currentQuestionId, setCurrentQuestionId] = useState<string>('');
  const [answers, setAnswers] = useState<Record<string, string | string[]>>({});
  const [questionHistory, setQuestionHistory] = useState<string[]>([]);
  const [isComplete, setIsComplete] = useState(false);

  // Initialize the quiz based on the health concern
  useEffect(() => {
    if (concernParam && healthConcernFlows[concernParam]) {
      const firstQuestionId = Object.keys(healthConcernFlows[concernParam])[0];
      setCurrentQuestionId(firstQuestionId);
      setQuestionHistory([firstQuestionId]);
      setAnswers({ healthConcern: concernParam });
    }
  }, [concernParam]);

  const currentQuestion = concernParam && healthConcernFlows[concernParam]?.[currentQuestionId];
  const totalQuestions = concernParam ? Object.keys(healthConcernFlows[concernParam]).length : 0;
  const currentQuestionIndex = questionHistory.indexOf(currentQuestionId) + 1;
  const progress = (currentQuestionIndex / totalQuestions) * 100;

  const handleAnswer = (value: string | string[]) => {
    setAnswers((prev) => ({ ...prev, [currentQuestionId]: value }));
  };

  const handleNext = () => {
    if (!concernParam) return;
    
    const nextQuestions = Object.keys(healthConcernFlows[concernParam]);
    const currentIndex = nextQuestions.indexOf(currentQuestionId);
    
    if (currentIndex < nextQuestions.length - 1) {
      const nextQuestionId = nextQuestions[currentIndex + 1];
      setQuestionHistory((prev) => [...prev, nextQuestionId]);
      setCurrentQuestionId(nextQuestionId);
    } else if (currentIndex === nextQuestions.length - 1) {
      handleComplete();
    }
  };

  const handlePrevious = () => {
    if (questionHistory.length > 1) {
      const newHistory = [...questionHistory];
      newHistory.pop();
      setQuestionHistory(newHistory);
      setCurrentQuestionId(newHistory[newHistory.length - 1]);
    }
  };

  const handleComplete = () => {
    setIsComplete(true);
    router.push(`/quiz/${concernParam}/products`);
  };

  const handleRestart = () => {
    if (concernParam && healthConcernFlows[concernParam]) {
      const firstQuestionId = Object.keys(healthConcernFlows[concernParam])[0];
      setCurrentQuestionId(firstQuestionId);
      setQuestionHistory([firstQuestionId]);
      setAnswers({ healthConcern: concernParam });
      setIsComplete(false);
    }
  };

  if (!concernParam || !healthConcernFlows[concernParam]) {
    return (
      <div className="min-h-screen bg-background">
        <Navbar />
        <main className="py-24">
          <div className="container">
            <div className="max-w-2xl mx-auto text-center">
              <h2 className="text-2xl font-bold mb-4">Invalid Health Concern</h2>
              <p className="text-muted-foreground mb-8">
                Please select a valid health concern from the home page.
              </p>
            </div>
          </div>
        </main>
      </div>
    );
  }

  const isLastQuestion = currentQuestionIndex === totalQuestions;
  const hasAnswer = answers[currentQuestionId];
  const showNextButton = currentQuestion?.type === 'checkbox' && hasAnswer;

  return (
    <div className="min-h-screen bg-background">
      <Navbar />
      <main className="container max-w-3xl py-6">
        <div className="space-y-8">
          {!isComplete ? (
            <>
              <div className="mb-8">
                <h2 className="text-2xl font-bold mb-4">Personalized Health Assessment</h2>
                <Progress value={progress} className="h-2" />
                <p className="text-sm text-muted-foreground mt-2">
                  Question {currentQuestionIndex} of {totalQuestions}
                </p>
              </div>

              <AnimatePresence mode="wait">
                {currentQuestion && (
                  <QuestionCard
                    key={currentQuestionId}
                    question={currentQuestion}
                    value={answers[currentQuestionId] || (currentQuestion.type === 'checkbox' ? [] : '')}
                    onChange={handleAnswer}
                    onNext={handleNext}
                  />
                )}
              </AnimatePresence>

              <div className="flex justify-between mt-8">
                <Button
                  variant="outline"
                  onClick={handlePrevious}
                  disabled={questionHistory.length <= 1}
                >
                  Previous
                </Button>
                {showNextButton && (
                  isLastQuestion ? (
                    <Button onClick={handleComplete}>
                      View Recommendations
                    </Button>
                  ) : (
                    <Button onClick={handleNext}>
                      Next
                    </Button>
                  )
                )}
              </div>
            </>
          ) : (
            <div className="flex items-center justify-center min-h-[400px]">
              <div className="relative inline-flex">
                <div className="w-24 h-24 border-4 border-dashed rounded-full animate-spin border-primary" style={{ animationDuration: '3s' }}></div>
                <span className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 font-semibold text-primary text-xl">
                  LIG
                </span>
              </div>
            </div>
          )}
        </div>
      </main>
    </div>
  );
} 