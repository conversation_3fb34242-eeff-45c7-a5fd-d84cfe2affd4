import { Navbar } from "@/components/navbar";
import { healthConcernFlows } from '@/lib/constants/quiz-flow';
import { type HealthConcern } from '@/lib/types/quiz';
import ProductList from './product-list';

// 为每个健康问题定义相关产品
const healthConcernProducts: Record<HealthConcern, Array<{
  id: number;
  name: string;
  description: string;
  price: number;
  tags: string[];
  image: string;
}>> = {
  'respiratory_health': [
    {
      id: 1,
      name: "Respiratory Support Complex",
      description: "Natural supplement for respiratory health support",
      price: 39.99,
      tags: ["Respiratory Health", "Immune Support", "Natural"],
      image: "https://images.unsplash.com/photo-1584017911941-608706237d5c?q=80&w=2787&auto=format&fit=crop"
    }
  ],
  'heart_health': [
    {
      id: 2,
      name: "Heart Health Formula",
      description: "Comprehensive support for cardiovascular health",
      price: 44.99,
      tags: ["Heart Health", "Cardiovascular", "Wellness"],
      image: "https://images.unsplash.com/photo-1577174881658-0f30ed549adc?q=80&w=2787&auto=format&fit=crop"
    }
  ],
  'hair_renew': [
    {
      id: 3,
      name: "Hair Vitality Complex",
      description: "Advanced formula for healthy hair growth and strength",
      price: 34.99,
      tags: ["Hair Health", "Biotin", "Vitamins"],
      image: "https://images.unsplash.com/photo-1584017911941-608706237d5c?q=80&w=2787&auto=format&fit=crop"
    }
  ],
  'mental_health': [
    {
      id: 4,
      name: "Mental Clarity Support",
      description: "Natural support for mental well-being and focus",
      price: 49.99,
      tags: ["Mental Health", "Focus", "Calm"],
      image: "https://images.unsplash.com/photo-1577174881658-0f30ed549adc?q=80&w=2787&auto=format&fit=crop"
    }
  ],
  'eye_care': [
    {
      id: 5,
      name: "Vision Support Formula",
      description: "Comprehensive eye health supplement",
      price: 39.99,
      tags: ["Eye Health", "Vision", "Antioxidants"],
      image: "https://images.unsplash.com/photo-1584017911941-608706237d5c?q=80&w=2787&auto=format&fit=crop"
    }
  ],
  'immune_support': [
    {
      id: 6,
      name: "Immune Defense Plus",
      description: "Advanced immune system support formula",
      price: 45.99,
      tags: ["Immune Health", "Wellness", "Vitamins"],
      image: "https://images.unsplash.com/photo-1577174881658-0f30ed549adc?q=80&w=2787&auto=format&fit=crop"
    }
  ]
};

export function generateStaticParams() {
  return Object.keys(healthConcernProducts).map((concern) => ({
    questionId: concern,
  }));
}

interface PageProps {
  params: {
    questionId: string;
  };
}

export default function QuestionProducts({ params }: PageProps) {
  const healthConcern = params.questionId as HealthConcern;
  const products = healthConcernProducts[healthConcern] || [];
  const concernFlow = healthConcernFlows[healthConcern];
  const concernName = concernFlow ? Object.values(concernFlow)[0]?.text : 'your health needs';

  return (
    <div className="min-h-screen bg-background">
      <Navbar />
      <main className="py-24">
        <div className="container mx-auto px-6">
          <h1 className="text-3xl font-bold mb-2">Recommended Products</h1>
          <p className="text-muted-foreground mb-8">
            Based on your concerns about {concernName}
          </p>
          <ProductList products={products} />
        </div>
      </main>
    </div>
  );
} 