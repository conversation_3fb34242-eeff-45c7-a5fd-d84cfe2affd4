"use client";

import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Heart, ShoppingCart } from "lucide-react";
import { Navbar } from "@/components/navbar";

const products = [
  {
    id: 1,
    name: "Vitamin D3 Supplement",
    description: "High-quality vitamin D3 for immune support and bone health",
    price: 24.99,
    tags: ["Immunity", "Bone Health"],
    image: "https://images.unsplash.com/photo-1584308666744-24d5c474f2ae?q=80&w=2830&auto=format&fit=crop"
  },
  {
    id: 2,
    name: "Omega-3 Fish Oil",
    description: "Pure fish oil rich in EPA & DHA for heart and brain health",
    price: 29.99,
    tags: ["Heart Health", "Brain Health"],
    image: "https://images.unsplash.com/photo-1577174881658-0f30ed549adc?q=80&w=2787&auto=format&fit=crop"
  },
  {
    id: 3,
    name: "Magnesium Complex",
    description: "Advanced magnesium formula for muscle and nerve function",
    price: 19.99,
    tags: ["Muscle Health", "Sleep"],
    image: "https://images.unsplash.com/photo-1471864190281-a93a3070b6de?q=80&w=2787&auto=format&fit=crop"
  }
];

export default function ProductsPage() {
  return (
    <div className="min-h-screen bg-background">
      <Navbar />
      <main className="py-24">
        <div className="container mx-auto px-6">
          <h1 className="text-3xl font-bold mb-8">Recommended Products</h1>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {products.map((product) => (
              <Card key={product.id} className="overflow-hidden">
                <div className="relative h-48">
                  <img
                    src={product.image}
                    alt={product.name}
                    className="w-full h-full object-cover"
                  />
                  <button className="absolute top-2 right-2 p-2 rounded-full bg-white/80 hover:bg-white transition-colors">
                    <Heart className="w-5 h-5 text-gray-600" />
                  </button>
                </div>
                <div className="p-4">
                  <div className="flex gap-2 mb-2">
                    {product.tags.map((tag) => (
                      <Badge key={tag} variant="secondary">
                        {tag}
                      </Badge>
                    ))}
                  </div>
                  <h2 className="text-xl font-semibold mb-2">{product.name}</h2>
                  <p className="text-gray-600 mb-4">{product.description}</p>
                  <div className="flex items-center justify-between">
                    <span className="text-2xl font-bold">${product.price}</span>
                    <Button className="flex items-center gap-2">
                      <ShoppingCart className="w-4 h-4" />
                      Add to Cart
                    </Button>
                  </div>
                </div>
              </Card>
            ))}
          </div>
        </div>
      </main>
    </div>
  );
}