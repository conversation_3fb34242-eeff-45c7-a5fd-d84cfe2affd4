@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Primary Green */
    --primary: 142 30% 65%;
    --primary-foreground: 0 0% 100%;
    
    /* Secondary Beige */
    --background: 60 30% 96%;
    --foreground: 0 0% 29%;
    
    /* Accent Orange */
    --accent: 35 100% 64%;
    --accent-foreground: 0 0% 100%;
    
    /* Neutral Gray */
    --muted: 0 0% 96%;
    --muted-foreground: 0 0% 29%;
    
    /* Soft White */
    --card: 0 0% 100%;
    --card-foreground: 0 0% 29%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 29%;
    
    /* Supporting Colors */
    --secondary: 60 30% 96%;
    --secondary-foreground: 0 0% 29%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 60 30% 90%;
    --input: 60 30% 90%;
    --ring: 142 30% 65%;
    
    /* Chart Colors */
    --chart-1: 142 30% 65%;
    --chart-2: 60 30% 96%;
    --chart-3: 35 100% 64%;
    --chart-4: 0 0% 29%;
    --chart-5: 0 0% 96%;
    
    --radius: 0.5rem;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  
  html {
    height: 100%;
  }

  body {
    @apply bg-background text-foreground;
    height: 100%;
  }

  /* 隐藏滚动条的 class */
  .scrollbar-hide {
    -ms-overflow-style: none; /* IE 和 Edge */
    scrollbar-width: none; /* Firefox */
  }

  .scrollbar-hide::-webkit-scrollbar {
    display: none; /* Chrome, Safari */
  }

  .container {
    @apply mx-auto px-4 sm:px-6 lg:px-8 max-w-7xl;
  }
}