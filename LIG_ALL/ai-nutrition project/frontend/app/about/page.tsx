import { Navbar } from '@/components/navbar';
import { Card, CardContent } from '@/components/ui/card';
import { Brain, Users, Award, Clock } from 'lucide-react';

const stats = [
  { name: 'Active Users', value: '10,000+', icon: Users },
  { name: 'Expert Recommendations', value: '99.9%', icon: Brain },
  { name: 'Success Rate', value: '95%', icon: Award },
  { name: 'Response Time', value: '< 2min', icon: Clock },
];

export default function About() {
  return (
    <div className="min-h-screen bg-background">
      <Navbar />
      <main className="py-24">
        <div className="mx-auto max-w-7xl px-6 lg:px-8">
          <div className="mx-auto max-w-2xl lg:text-center">
            <h2 className="text-base font-semibold leading-7 text-primary">About Us</h2>
            <p className="mt-2 text-3xl font-bold tracking-tight sm:text-4xl">
              Revolutionizing Nutrition with AI
            </p>
            <p className="mt-6 text-lg leading-8 text-muted-foreground">
              We combine cutting-edge artificial intelligence with extensive nutritional science
              to provide personalized health recommendations that work for you.
            </p>
          </div>

          <div className="mx-auto mt-16 max-w-2xl sm:mt-20 lg:mt-24 lg:max-w-none">
            <dl className="grid max-w-xl grid-cols-1 gap-x-8 gap-y-10 lg:max-w-none lg:grid-cols-4">
              {stats.map((stat) => (
                <Card key={stat.name}>
                  <CardContent className="pt-6">
                    <div className="flex flex-col items-center">
                      <stat.icon className="h-8 w-8 text-primary mb-4" />
                      <dt className="text-sm font-medium text-muted-foreground">
                        {stat.name}
                      </dt>
                      <dd className="mt-2 text-3xl font-bold tracking-tight">
                        {stat.value}
                      </dd>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </dl>
          </div>

          <div className="mx-auto mt-16 max-w-2xl lg:text-center">
            <h3 className="text-2xl font-bold tracking-tight">Our Mission</h3>
            <p className="mt-6 text-lg leading-8 text-muted-foreground">
              Our mission is to make personalized nutrition accessible to everyone. We believe that
              optimal health starts with understanding your body's unique needs and providing
              tailored solutions that fit your lifestyle.
            </p>
          </div>
        </div>
      </main>
    </div>
  );
}