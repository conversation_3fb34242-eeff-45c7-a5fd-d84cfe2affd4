# AI Nutrition 颜色方案文档

这个文档定义了 AI Nutrition 项目使用的颜色方案，包括背景色、品牌色、功效色和文本色。

## 颜色定义

### 基础色
- **背景色**
  - 浅米灰色: `#E5DED3` <span style="display:inline-block; width:20px; height:20px; background-color:#E5DED3; border:1px solid #ccc;"></span>
- **品牌色**
  - 品牌紫色: `#9A73BC` <span style="display:inline-block; width:20px; height:20px; background-color:#9A73BC; border:1px solid #ccc;"></span>
- **文本色**
  - 深灰色: `#333333` <span style="display:inline-block; width:20px; height:20px; background-color:#333333; border:1px solid #ccc;"></span> (用于说明文字和产品描述)
  - 白色: `#FFFFFF` <span style="display:inline-block; width:20px; height:20px; background-color:#FFFFFF; border:1px solid #ccc;"></span> (用于深色背景上的文字)

### 功效颜色

| 功效 | 颜色名称 | 十六进制码 | 预览 |
|------|----------|------------|-------|
| 免疫支持 | 砖红色 | `#A15959` | <span style="display:inline-block; width:20px; height:20px; background-color:#A15959; border:1px solid #ccc;"></span> |
| 睡眠支持 | 蓝色 | `#5979A1` | <span style="display:inline-block; width:20px; height:20px; background-color:#5979A1; border:1px solid #ccc;"></span> |
| 肝脏支持 | 绿色 | `#5E8C6B` | <span style="display:inline-block; width:20px; height:20px; background-color:#5E8C6B; border:1px solid #ccc;"></span> |
| 过敏缓解 | 深赭红色 | `#A16359` | <span style="display:inline-block; width:20px; height:20px; background-color:#A16359; border:1px solid #ccc;"></span> |
| 抗压力 | 黛紫色 | `#79599A` | <span style="display:inline-block; width:20px; height:20px; background-color:#79599A; border:1px solid #ccc;"></span> |
| 关节支持 | 暗砖红色 | `#8C5E5E` | <span style="display:inline-block; width:20px; height:20px; background-color:#8C5E5E; border:1px solid #ccc;"></span> |
| 消化健康 | 青铜绿 | `#597969` | <span style="display:inline-block; width:20px; height:20px; background-color:#597969; border:1px solid #ccc;"></span> |
| 脑功能 | 绛紫色 | `#8C5E7A` | <span style="display:inline-block; width:20px; height:20px; background-color:#8C5E7A; border:1px solid #ccc;"></span> |

## 柔和中性色彩方案

这套颜色方案包含柔和、中深色调、略带灰度特质的颜色，它们能很好地相互搭配使用，形成协调的色彩方案。

### 背景色
- 浅米灰色: `#E5DED3` <span style="display:inline-block; width:20px; height:20px; background-color:#E5DED3; border:1px solid #ccc;"></span>

### 蓝色系
- 蓝色: `#5979A1` <span style="display:inline-block; width:20px; height:20px; background-color:#5979A1; border:1px solid #ccc;"></span>
- 墨蓝色: `#3D5979` <span style="display:inline-block; width:20px; height:20px; background-color:#3D5979; border:1px solid #ccc;"></span>

### 绿色系
- 绿色: `#5E8C6B` <span style="display:inline-block; width:20px; height:20px; background-color:#5E8C6B; border:1px solid #ccc;"></span>
- 青铜绿: `#597969` <span style="display:inline-block; width:20px; height:20px; background-color:#597969; border:1px solid #ccc;"></span>

### 红色系
- 砖红色: `#A15959` <span style="display:inline-block; width:20px; height:20px; background-color:#A15959; border:1px solid #ccc;"></span>
- 深赭红色: `#A16359` <span style="display:inline-block; width:20px; height:20px; background-color:#A16359; border:1px solid #ccc;"></span>
- 暗砖红色: `#8C5E5E` <span style="display:inline-block; width:20px; height:20px; background-color:#8C5E5E; border:1px solid #ccc;"></span>

### 紫色系
- 黛紫色: `#79599A` <span style="display:inline-block; width:20px; height:20px; background-color:#79599A; border:1px solid #ccc;"></span>
- 暗酱紫色: `#6A5979` <span style="display:inline-block; width:20px; height:20px; background-color:#6A5979; border:1px solid #ccc;"></span>
- 绛紫色: `#8C5E7A` <span style="display:inline-block; width:20px; height:20px; background-color:#8C5E7A; border:1px solid #ccc;"></span>

### 棕色系
- 褐铜色: `#796459` <span style="display:inline-block; width:20px; height:20px; background-color:#796459; border:1px solid #ccc;"></span>
- 灰棕色: `#7D7159` <span style="display:inline-block; width:20px; height:20px; background-color:#7D7159; border:1px solid #ccc;"></span>
- 暗驼色: `#8C7B5E` <span style="display:inline-block; width:20px; height:20px; background-color:#8C7B5E; border:1px solid #ccc;"></span>

## 颜色预览

### 基础色
<div style="display:flex; gap:10px; margin-bottom:20px;">
  <div style="text-align:center;">
    <div style="width:100px; height:50px; background-color:#E5DED3; border:1px solid #ccc;"></div>
    <p>浅米灰色<br>#E5DED3</p>
  </div>
  <div style="text-align:center;">
    <div style="width:100px; height:50px; background-color:#9A73BC; border:1px solid #ccc;"></div>
    <p>品牌紫色<br>#9A73BC</p>
  </div>
  <div style="text-align:center;">
    <div style="width:100px; height:50px; background-color:#333333; border:1px solid #ccc;"></div>
    <p>深灰色<br>#333333</p>
  </div>
  <div style="text-align:center;">
    <div style="width:100px; height:50px; background-color:#FFFFFF; border:1px solid #ccc;"></div>
    <p>白色<br>#FFFFFF</p>
  </div>
</div>

### 功效颜色
<div style="display:flex; flex-wrap:wrap; gap:10px; margin-bottom:20px;">
  <div style="text-align:center;">
    <div style="width:100px; height:50px; background-color:#A15959; border:1px solid #ccc;"></div>
    <p>免疫支持<br>#A15959</p>
  </div>
  <div style="text-align:center;">
    <div style="width:100px; height:50px; background-color:#5979A1; border:1px solid #ccc;"></div>
    <p>睡眠支持<br>#5979A1</p>
  </div>
  <div style="text-align:center;">
    <div style="width:100px; height:50px; background-color:#5E8C6B; border:1px solid #ccc;"></div>
    <p>肝脏支持<br>#5E8C6B</p>
  </div>
  <div style="text-align:center;">
    <div style="width:100px; height:50px; background-color:#A16359; border:1px solid #ccc;"></div>
    <p>过敏缓解<br>#A16359</p>
  </div>
  <div style="text-align:center;">
    <div style="width:100px; height:50px; background-color:#79599A; border:1px solid #ccc;"></div>
    <p>抗压力<br>#79599A</p>
  </div>
  <div style="text-align:center;">
    <div style="width:100px; height:50px; background-color:#8C5E5E; border:1px solid #ccc;"></div>
    <p>关节支持<br>#8C5E5E</p>
  </div>
  <div style="text-align:center;">
    <div style="width:100px; height:50px; background-color:#597969; border:1px solid #ccc;"></div>
    <p>消化健康<br>#597969</p>
  </div>
  <div style="text-align:center;">
    <div style="width:100px; height:50px; background-color:#8C5E7A; border:1px solid #ccc;"></div>
    <p>脑功能<br>#8C5E7A</p>
  </div>
</div>

### 柔和中性色彩方案
<div style="background-color: #E5DED3; padding: 15px; border-radius: 8px; margin-bottom: 20px;">
  <div style="display:flex; flex-wrap:wrap; gap:10px; margin-bottom:10px;">
    <div style="text-align:center;">
      <div style="width:100px; height:50px; background-color:#5979A1; border:1px solid #ccc;"></div>
      <p>蓝色<br>#5979A1</p>
    </div>
    <div style="text-align:center;">
      <div style="width:100px; height:50px; background-color:#3D5979; border:1px solid #ccc;"></div>
      <p>墨蓝色<br>#3D5979</p>
    </div>
    <div style="text-align:center;">
      <div style="width:100px; height:50px; background-color:#5E8C6B; border:1px solid #ccc;"></div>
      <p>绿色<br>#5E8C6B</p>
    </div>
    <div style="text-align:center;">
      <div style="width:100px; height:50px; background-color:#597969; border:1px solid #ccc;"></div>
      <p>青铜绿<br>#597969</p>
    </div>
  </div>
  <div style="display:flex; flex-wrap:wrap; gap:10px; margin-bottom:10px;">
    <div style="text-align:center;">
      <div style="width:100px; height:50px; background-color:#A15959; border:1px solid #ccc;"></div>
      <p>砖红色<br>#A15959</p>
    </div>
    <div style="text-align:center;">
      <div style="width:100px; height:50px; background-color:#A16359; border:1px solid #ccc;"></div>
      <p>深赭红色<br>#A16359</p>
    </div>
    <div style="text-align:center;">
      <div style="width:100px; height:50px; background-color:#8C5E5E; border:1px solid #ccc;"></div>
      <p>暗砖红色<br>#8C5E5E</p>
    </div>
  </div>
  <div style="display:flex; flex-wrap:wrap; gap:10px; margin-bottom:10px;">
    <div style="text-align:center;">
      <div style="width:100px; height:50px; background-color:#79599A; border:1px solid #ccc;"></div>
      <p>黛紫色<br>#79599A</p>
    </div>
    <div style="text-align:center;">
      <div style="width:100px; height:50px; background-color:#6A5979; border:1px solid #ccc;"></div>
      <p>暗酱紫色<br>#6A5979</p>
    </div>
    <div style="text-align:center;">
      <div style="width:100px; height:50px; background-color:#8C5E7A; border:1px solid #ccc;"></div>
      <p>绛紫色<br>#8C5E7A</p>
    </div>
  </div>
  <div style="display:flex; flex-wrap:wrap; gap:10px;">
    <div style="text-align:center;">
      <div style="width:100px; height:50px; background-color:#796459; border:1px solid #ccc;"></div>
      <p>褐铜色<br>#796459</p>
    </div>
    <div style="text-align:center;">
      <div style="width:100px; height:50px; background-color:#7D7159; border:1px solid #ccc;"></div>
      <p>灰棕色<br>#7D7159</p>
    </div>
    <div style="text-align:center;">
      <div style="width:100px; height:50px; background-color:#8C7B5E; border:1px solid #ccc;"></div>
      <p>暗驼色<br>#8C7B5E</p>
    </div>
  </div>
</div>

## 使用指南

### 在 TypeScript/JavaScript 中使用

```typescript
// 在 lib/constants/colors.ts 中定义
export const COLORS = {
  background: {
    lightBeige: '#E5DED3',
  },
  brand: {
    purple: '#9A73BC',
  },
  text: {
    dark: '#333333',
    white: '#FFFFFF',
  },
  effects: {
    immunity: '#A15959',
    sleep: '#5979A1',
    liver: '#5E8C6B',
    allergy: '#A16359',
    stress: '#79599A',
    joint: '#8C5E5E',
    digestion: '#597969',
    brain: '#8C5E7A',
  },
  softNeutral: {
    blue: '#5979A1',
    darkBlue: '#3D5979',
    green: '#5E8C6B',
    bronzeGreen: '#597969',
    brickRed: '#A15959',
    deepOchreRed: '#A16359',
    darkBrickRed: '#8C5E5E',
    indigoPurple: '#79599A',
    darkSaucePurple: '#6A5979',
    crimsonPurple: '#8C5E7A',
    brownBronze: '#796459',
    grayBrown: '#7D7159',
    darkCamel: '#8C7B5E',
  },
} as const;
```

### 在 Tailwind 中使用

在 `tailwind.config.ts` 中添加这些颜色：

```typescript
export default {
  theme: {
    extend: {
      colors: {
        'light-beige': '#E5DED3',
        'brand-purple': '#9A73BC',
        'text-dark': '#333333',
        'text-white': '#FFFFFF',
        effects: {
          immunity: '#A15959',
          sleep: '#5979A1',
          liver: '#5E8C6B',
          allergy: '#A16359',
          stress: '#79599A',
          joint: '#8C5E5E',
          digestion: '#597969',
          brain: '#8C5E7A',
        },
        soft: {
          blue: '#5979A1',
          'dark-blue': '#3D5979',
          green: '#5E8C6B',
          'bronze-green': '#597969',
          'brick-red': '#A15959',
          'deep-ochre': '#A16359',
          'dark-brick': '#8C5E5E',
          'indigo-purple': '#79599A',
          'dark-sauce': '#6A5979',
          'crimson-purple': '#8C5E7A',
          'brown-bronze': '#796459',
          'gray-brown': '#7D7159',
          'dark-camel': '#8C7B5E',
        },
      },
    },
  },
};
```

### 使用示例

```tsx
// 在组件中使用
<div className="bg-light-beige text-text-dark">
  <span className="text-effects-immunity">免疫支持</span>
  <span className="text-effects-sleep">睡眠支持</span>
</div>

// 使用柔和中性色彩方案
<div className="bg-light-beige p-4">
  <div className="bg-soft-blue text-white p-2 rounded">蓝色按钮</div>
  <div className="bg-soft-brick-red text-white p-2 rounded mt-2">红色提示</div>
  <div className="bg-soft-green text-white p-2 rounded mt-2">绿色卡片</div>
</div>

// 使用功效颜色
<div className="grid grid-cols-2 gap-4">
  <div className="bg-effects-immunity text-white p-3 rounded">免疫支持</div>
  <div className="bg-effects-sleep text-white p-3 rounded">睡眠支持</div>
  <div className="bg-effects-liver text-white p-3 rounded">肝脏支持</div>
  <div className="bg-effects-brain text-white p-3 rounded">脑功能</div>
</div>
```

## 颜色选择说明

- **浅米灰色 (#E5DED3)**: 提供温暖、自然的背景色，适合展示柔和中性色彩
- **品牌紫色 (#9A73BC)**: 传达专业、可信赖的品牌形象
- **功效颜色**: 每种功效都有其独特的颜色，帮助用户直观识别不同功能
- **文本深灰色 (#333333)**: 确保文字清晰可读，同时避免纯黑色带来的视觉疲劳
- **柔和中性色彩**: 这些颜色具有柔和、中深色调、略带灰度特质，能很好地相互搭配使用

## 无障碍设计考虑

所有颜色都经过对比度检查，确保：
- 文本颜色与背景色的对比度符合 WCAG 2.1 标准
- 功效颜色可以清晰区分，便于色盲用户识别 