# 颜色展示

背景色 (浅米灰色): #E5DED3

## 颜色列表

<div style="background-color: #E5DED3; padding: 20px; border-radius: 8px;">

### 蓝色系
<svg width="200" height="80" style="margin: 10px;">
  <rect width="200" height="80" fill="#5979A1" rx="8" ry="8"/>
  <text x="100" y="45" fill="white" font-family="Arial" font-size="16" text-anchor="middle">蓝色: #5979A1</text>
</svg>

<svg width="200" height="80" style="margin: 10px;">
  <rect width="200" height="80" fill="#3D5979" rx="8" ry="8"/>
  <text x="100" y="45" fill="white" font-family="Arial" font-size="16" text-anchor="middle">墨蓝色: #3D5979</text>
</svg>

### 绿色系
<svg width="200" height="80" style="margin: 10px;">
  <rect width="200" height="80" fill="#5E8C6B" rx="8" ry="8"/>
  <text x="100" y="45" fill="white" font-family="Arial" font-size="16" text-anchor="middle">绿色: #5E8C6B</text>
</svg>

<svg width="200" height="80" style="margin: 10px;">
  <rect width="200" height="80" fill="#597969" rx="8" ry="8"/>
  <text x="100" y="45" fill="white" font-family="Arial" font-size="16" text-anchor="middle">青铜绿: #597969</text>
</svg>

### 红色系
<svg width="200" height="80" style="margin: 10px;">
  <rect width="200" height="80" fill="#A15959" rx="8" ry="8"/>
  <text x="100" y="45" fill="white" font-family="Arial" font-size="16" text-anchor="middle">砖红色: #A15959</text>
</svg>

<svg width="200" height="80" style="margin: 10px;">
  <rect width="200" height="80" fill="#A16359" rx="8" ry="8"/>
  <text x="100" y="45" fill="white" font-family="Arial" font-size="16" text-anchor="middle">深赭红色: #A16359</text>
</svg>

<svg width="200" height="80" style="margin: 10px;">
  <rect width="200" height="80" fill="#8C5E5E" rx="8" ry="8"/>
  <text x="100" y="45" fill="white" font-family="Arial" font-size="16" text-anchor="middle">暗砖红色: #8C5E5E</text>
</svg>

### 紫色系
<svg width="200" height="80" style="margin: 10px;">
  <rect width="200" height="80" fill="#79599A" rx="8" ry="8"/>
  <text x="100" y="45" fill="white" font-family="Arial" font-size="16" text-anchor="middle">黛紫色: #79599A</text>
</svg>

<svg width="200" height="80" style="margin: 10px;">
  <rect width="200" height="80" fill="#6A5979" rx="8" ry="8"/>
  <text x="100" y="45" fill="white" font-family="Arial" font-size="16" text-anchor="middle">暗酱紫色: #6A5979</text>
</svg>

<svg width="200" height="80" style="margin: 10px;">
  <rect width="200" height="80" fill="#8C5E7A" rx="8" ry="8"/>
  <text x="100" y="45" fill="white" font-family="Arial" font-size="16" text-anchor="middle">绛紫色: #8C5E7A</text>
</svg>

### 棕色系
<svg width="200" height="80" style="margin: 10px;">
  <rect width="200" height="80" fill="#796459" rx="8" ry="8"/>
  <text x="100" y="45" fill="white" font-family="Arial" font-size="16" text-anchor="middle">褐铜色: #796459</text>
</svg>

<svg width="200" height="80" style="margin: 10px;">
  <rect width="200" height="80" fill="#7D7159" rx="8" ry="8"/>
  <text x="100" y="45" fill="white" font-family="Arial" font-size="16" text-anchor="middle">灰棕色: #7D7159</text>
</svg>

<svg width="200" height="80" style="margin: 10px;">
  <rect width="200" height="80" fill="#8C7B5E" rx="8" ry="8"/>
  <text x="100" y="45" fill="white" font-family="Arial" font-size="16" text-anchor="middle">暗驼色: #8C7B5E</text>
</svg>

</div>

## 颜色组合示例

<div style="background-color: #E5DED3; padding: 20px; border-radius: 8px; display: flex; flex-wrap: wrap; justify-content: center;">

<svg width="150" height="150" style="margin: 10px;">
  <rect width="150" height="150" fill="#5979A1" rx="8" ry="8"/>
  <rect width="75" height="75" x="37.5" y="37.5" fill="#5E8C6B" rx="4" ry="4"/>
</svg>

<svg width="150" height="150" style="margin: 10px;">
  <rect width="150" height="150" fill="#A15959" rx="8" ry="8"/>
  <rect width="75" height="75" x="37.5" y="37.5" fill="#79599A" rx="4" ry="4"/>
</svg>

<svg width="150" height="150" style="margin: 10px;">
  <rect width="150" height="150" fill="#3D5979" rx="8" ry="8"/>
  <rect width="75" height="75" x="37.5" y="37.5" fill="#8C7B5E" rx="4" ry="4"/>
</svg>

<svg width="150" height="150" style="margin: 10px;">
  <rect width="150" height="150" fill="#8C5E7A" rx="8" ry="8"/>
  <rect width="75" height="75" x="37.5" y="37.5" fill="#597969" rx="4" ry="4"/>
</svg>

</div>

## 色彩渐变示例

<div style="background-color: #E5DED3; padding: 20px; border-radius: 8px;">

<svg width="400" height="80" style="margin: 10px;">
  <defs>
    <linearGradient id="gradient1" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#5979A1;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#5E8C6B;stop-opacity:1" />
    </linearGradient>
  </defs>
  <rect width="400" height="80" fill="url(#gradient1)" rx="8" ry="8"/>
  <text x="200" y="45" fill="white" font-family="Arial" font-size="16" text-anchor="middle">蓝色 → 绿色</text>
</svg>

<svg width="400" height="80" style="margin: 10px;">
  <defs>
    <linearGradient id="gradient2" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#A15959;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#79599A;stop-opacity:1" />
    </linearGradient>
  </defs>
  <rect width="400" height="80" fill="url(#gradient2)" rx="8" ry="8"/>
  <text x="200" y="45" fill="white" font-family="Arial" font-size="16" text-anchor="middle">砖红色 → 黛紫色</text>
</svg>

<svg width="400" height="80" style="margin: 10px;">
  <defs>
    <linearGradient id="gradient3" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#8C7B5E;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#3D5979;stop-opacity:1" />
    </linearGradient>
  </defs>
  <rect width="400" height="80" fill="url(#gradient3)" rx="8" ry="8"/>
  <text x="200" y="45" fill="white" font-family="Arial" font-size="16" text-anchor="middle">暗驼色 → 墨蓝色</text>
</svg>

</div> 