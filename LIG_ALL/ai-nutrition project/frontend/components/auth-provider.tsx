'use client';

import { Clerk<PERSON><PERSON><PERSON> } from '@clerk/clerk-react';

const publishableKey = process.env.NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY as string;

if (!publishableKey) {
  throw new Error('Missing Clerk Publishable Key');
}

export function AuthProvider({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <ClerkProvider
      publishableKey={publishableKey}
      appearance={{
        elements: {
          rootBox: "w-full",
          card: "rounded-xl shadow-lg",
          formButtonPrimary: "bg-primary hover:bg-primary/90 text-primary-foreground transition-colors"
        }
      }}
    >
      {children}
    </ClerkProvider>
  );
} 