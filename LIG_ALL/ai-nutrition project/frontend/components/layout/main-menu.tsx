"use client";

import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { cn } from '@/lib/utils';

const navigation = [
  { name: 'About', href: '/about' },
  { name: 'Products', href: '/products' },
  { name: 'Contact', href: '/contact' },
];

export function MainMenu() {
  const pathname = usePathname();

  return (
    <div className="h-full flex flex-col pt-6">
      <nav className="flex flex-col space-y-4">
        {navigation.map((item) => (
          <Link
            key={item.name}
            href={item.href}
            className={cn(
              'flex items-center py-2 text-lg transition-colors hover:text-primary',
              pathname === item.href ? 'text-primary font-medium' : 'text-muted-foreground'
            )}
          >
            {item.name}
          </Link>
        ))}
      </nav>
    </div>
  );
}