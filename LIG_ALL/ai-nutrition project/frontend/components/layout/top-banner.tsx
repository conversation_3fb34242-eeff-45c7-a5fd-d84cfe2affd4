"use client";

import { useState, useEffect } from 'react';
import { cn } from '@/lib/utils';

export function TopBanner() {
  const [isSticky, setIsSticky] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      setIsSticky(window.scrollY > 0);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  return (
    <div
      className={cn(
        "w-full bg-background transition-all duration-300 z-50",
        isSticky ? "fixed top-0 border-b shadow-sm" : "relative"
      )}
    >
      <div className="container flex h-16 items-center justify-between">
        <div className="flex items-center space-x-4">
          <span className="text-sm text-muted-foreground">
            Free shipping on all orders
          </span>
        </div>
        <div className="flex items-center space-x-4">
          <span className="text-sm text-muted-foreground">
            100% online process
          </span>
        </div>
      </div>
    </div>
  );
}