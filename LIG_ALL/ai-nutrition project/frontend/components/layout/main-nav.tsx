"use client";

import Link from 'next/link';
import { Heart, Menu } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Sheet, Sheet<PERSON>ontent, Sheet<PERSON>eader, SheetTitle, SheetTrigger } from '@/components/ui/sheet';
import { cn } from '@/lib/utils';
import { MainMenu } from './main-menu';
import { UserButton } from '../user-button';

export function MainNav() {
  return (
    <nav className="fixed top-0 left-0 right-0 w-full bg-background/80 backdrop-blur-sm z-50 border-b">
      <div className="px-6 lg:px-8 flex h-16 items-center justify-between">
        <Link href="/" className="flex items-center hover:opacity-80 transition-opacity">
          <Heart className="h-8 w-8 text-primary" />
          <span className="ml-2 text-xl font-bold">LIG AI Nutritionist</span>
        </Link>

        <div className="flex items-center gap-4">
          <UserButton />
          <Sheet>
            <SheetTrigger asChild>
              <Button variant="ghost" size="icon">
                <Menu className="h-6 w-6" />
                <span className="sr-only">Toggle menu</span>
              </Button>
            </SheetTrigger>
            <SheetContent side="right">
              <SheetHeader>
                <SheetTitle>Menu</SheetTitle>
              </SheetHeader>
              <MainMenu />
            </SheetContent>
          </Sheet>
        </div>
      </div>
    </nav>
  );
}