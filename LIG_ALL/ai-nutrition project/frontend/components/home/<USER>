"use client";

import Link from 'next/link';
import { ArrowRight } from 'lucide-react';
import { cn } from '@/lib/utils';

interface HealthConcernCardProps {
  title: string;
  highlight: string;
  image: string;
  href: string;
  color: string;
}

export function HealthConcernCard({ 
  title, 
  highlight, 
  image, 
  href,
  color,
}: HealthConcernCardProps) {
  return (
    <Link 
      href={href}
      className="group relative overflow-hidden rounded-3xl transition-all duration-300"
    >
      <div 
        className={cn(
          "relative h-[200px] flex items-center justify-between transition-all duration-300 overflow-hidden",
          "group-hover:scale-105"
        )}
        style={{
          backgroundImage: `url(${image})`,
          backgroundSize: 'cover',
          backgroundPosition: 'center',
        }}
      >
        <div className="absolute inset-0 bg-gradient-to-r from-black/70 to-black/30" />
        
        <div className="relative z-10 w-full p-6 flex items-center justify-between">
          <div className="flex-1">
            <h3 className="text-4xl font-bold text-white tracking-tight">
              {title}{' '}
              <span className="font-extrabold" style={{ color }}>{highlight}</span>
            </h3>
          </div>
          <ArrowRight 
            className={cn(
              "h-8 w-8 transition-all duration-300",
              "text-white/70 group-hover:text-white",
              "group-hover:translate-x-1"
            )} 
          />
        </div>
      </div>
    </Link>
  );
}