"use client";

import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { ArrowRight } from 'lucide-react';
import { RotatingText } from '@/components/ui/rotating-text';
import { healthWords } from '@/lib/constants/rotating-words';

export function HeroSection() {
  return (
    <section className="pt-32 pb-8">
      <div className="container">
        <div className="max-w-7xl">
          <h1 className="text-6xl font-bold tracking-tight leading-none">
            <span className="block mb-2">
              <RotatingText words={healthWords as any} />
            </span>
            <div className="flex items-center justify-between">
              <span>solutions just for you</span>
              <div className="relative group">
                <Link href="/chat">
                  <Button 
                    size="lg" 
                    className="h-14 px-8 text-lg rounded-full bg-[#9DC4B0] hover:bg-[#8AB39D] text-white border-none shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-0.5"
                  >
                    Build my solution
                    <ArrowRight className="ml-2 h-5 w-5" />
                  </Button>
                </Link>
                <span className="absolute -bottom-6 right-2 text-sm text-[#666666] font-medium tracking-wide opacity-80">
                  AI powered
                </span>
              </div>
            </div>
          </h1>
          <p className="mt-6 text-xl text-muted-foreground max-w-2xl">
            Begin your journey to better health
          </p>
        </div>
      </div>
    </section>
  );
}