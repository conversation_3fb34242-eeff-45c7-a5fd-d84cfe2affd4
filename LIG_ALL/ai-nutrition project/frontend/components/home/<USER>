import { HealthConcernCard } from './health-concern-card';
import { healthConcerns } from '@/lib/constants/health-concerns';

export function HealthConcernsGrid() {
  return (
    <section className="py-8">
      <div className="container">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 max-w-7xl mx-auto">
          {healthConcerns.map((concern, index) => (
            <HealthConcernCard key={index} {...concern} />
          ))}
        </div>
      </div>
    </section>
  );
}