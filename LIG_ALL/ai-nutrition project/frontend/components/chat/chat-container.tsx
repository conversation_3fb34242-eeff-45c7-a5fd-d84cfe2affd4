'use client';

import { useState, useTransition, useEffect } from 'react';
import { Message } from '@/types/chat';
import { MessageList } from './message-list';
import { ChatInput } from './chat-input';
import { Card } from '../ui/card';
import { processMessage } from '@/app/chat/actions';
import { useToast } from '@/components/ui/use-toast';
import { PlusCircle, MessageCircle, Trash2, ChevronLeft, ChevronRight } from 'lucide-react';
import { cn } from '@/lib/utils';

const INITIAL_MESSAGE: Message = {
  text: "Hello! I'm your personal health and nutrition assistant. How can I help you today?",
  isUser: false,
  pending: false,
};

interface ChatSession {
  id: string;
  messages: Message[];
  lastUpdated: string;
}

const STORAGE_KEY = 'chat-sessions';
const MAX_SESSIONS = 3;

export function ChatContainer() {
  const [sessions, setSessions] = useState<ChatSession[]>([]);
  const [currentSessionId, setCurrentSessionId] = useState<string>('');
  const [messages, setMessages] = useState<Message[]>([]);
  const [isPending, startTransition] = useTransition();
  const { toast } = useToast();
  const [isCollapsed, setIsCollapsed] = useState(false);

  // 加载保存的会话记录
  useEffect(() => {
    const savedSessions = localStorage.getItem(STORAGE_KEY);
    if (savedSessions) {
      const loadedSessions = JSON.parse(savedSessions);
      setSessions(loadedSessions);
      if (loadedSessions.length > 0) {
        setCurrentSessionId(loadedSessions[0].id);
        setMessages(loadedSessions[0].messages);
      } else {
        createNewSession();
      }
    } else {
      createNewSession();
    }
  }, []);

  // 创建新会话
  const createNewSession = () => {
    const newSession: ChatSession = {
      id: Date.now().toString(),
      messages: [INITIAL_MESSAGE],
      lastUpdated: new Date().toISOString()
    };
    
    setSessions(prev => {
      const updatedSessions = [newSession, ...prev].slice(0, MAX_SESSIONS);
      localStorage.setItem(STORAGE_KEY, JSON.stringify(updatedSessions));
      return updatedSessions;
    });
    setCurrentSessionId(newSession.id);
    setMessages([INITIAL_MESSAGE]);
  };

  // 切换会话
  const switchSession = (sessionId: string) => {
    const session = sessions.find(s => s.id === sessionId);
    if (session) {
      setCurrentSessionId(sessionId);
      setMessages(session.messages);
    }
  };

  // 删除会话
  const deleteSession = (sessionId: string) => {
    setSessions(prev => {
      const updatedSessions = prev.filter(s => s.id !== sessionId);
      localStorage.setItem(STORAGE_KEY, JSON.stringify(updatedSessions));
      
      if (sessionId === currentSessionId) {
        if (updatedSessions.length > 0) {
          setCurrentSessionId(updatedSessions[0].id);
          setMessages(updatedSessions[0].messages);
        } else {
          createNewSession();
        }
      }
      return updatedSessions;
    });
  };

  // 更新会话
  const updateCurrentSession = (newMessages: Message[]) => {
    setSessions(prev => {
      const updatedSessions = prev.map(session => 
        session.id === currentSessionId
          ? { ...session, messages: newMessages, lastUpdated: new Date().toISOString() }
          : session
      );
      localStorage.setItem(STORAGE_KEY, JSON.stringify(updatedSessions));
      return updatedSessions;
    });
  };

  const handleSubmit = async (message: string) => {
    const userMessage: Message = {
      text: message,
      isUser: true,
      pending: false
    };
    const updatedMessages = [...messages, userMessage];
    setMessages(updatedMessages);
    updateCurrentSession(updatedMessages);

    const pendingMessage: Message = {
      text: '...',
      isUser: false,
      pending: true,
    };
    setMessages(prev => [...prev, pendingMessage]);

    startTransition(async () => {
      try {
        const result = await processMessage(message);
        
        if (!result.success) {
          throw new Error(result.error);
        }
        const finalMessages = [
          ...updatedMessages,
          {
            text: result.response || 'No response received',
            isUser: false,
            pending: false,
          },
        ];
        setMessages(finalMessages);
        updateCurrentSession(finalMessages);
      } catch (error) {
        const errorMessages = [
          ...updatedMessages,
          {
            text: 'Sorry, I encountered an error. Please try again.',
            isUser: false,
            pending: false,
          },
        ];
        setMessages(errorMessages);
        updateCurrentSession(errorMessages);
        toast({
          variant: "destructive",
          title: "Error",
          description: "Failed to get response. Please try again.",
        });
      }
    });
  };

  return (
    <div className="h-[calc(100vh-4rem)] flex justify-center">
      <div className="w-[95%] flex">
        {/* 可收缩的侧边栏 */}
        <div 
          className={cn(
            "flex flex-col border-r border-border transition-all duration-300 ease-in-out relative bg-background/95",
            isCollapsed ? "w-0" : "w-64"
          )}
        >
          {/* 收缩按钮 */}
          <button
            onClick={() => setIsCollapsed(!isCollapsed)}
            className="absolute -right-4 top-1/2 transform -translate-y-1/2 z-10
              w-8 h-8 rounded-full bg-background border border-border flex items-center justify-center
              hover:bg-muted transition-colors"
          >
            {isCollapsed ? (
              <ChevronRight className="h-4 w-4" />
            ) : (
              <ChevronLeft className="h-4 w-4" />
            )}
          </button>

          {/* 侧边栏内容 */}
          <div className={cn(
            "flex flex-col gap-2 p-2 overflow-hidden",
            isCollapsed ? "opacity-0" : "opacity-100"
          )}>
            <button
              onClick={createNewSession}
              className="flex items-center gap-2 p-2 rounded-lg bg-primary text-primary-foreground hover:bg-primary/90 transition-colors"
            >
              <PlusCircle className="h-4 w-4" />
              <span>New Chat</span>
            </button>
            <div className="flex flex-col gap-2 overflow-y-auto">
              {sessions.map(session => (
                <div
                  key={session.id}
                  className={cn(
                    "group flex items-center gap-2 p-2 rounded-lg hover:bg-muted/50 transition-colors cursor-pointer",
                    session.id === currentSessionId ? "bg-muted" : ""
                  )}
                >
                  <button
                    className="flex-1 flex items-center gap-2 min-w-0"
                    onClick={() => switchSession(session.id)}
                  >
                    <MessageCircle className="h-4 w-4 flex-shrink-0" />
                    <span className="truncate text-sm">
                      {session.messages[1]?.text.slice(0, 20) || 'New Chat'}...
                    </span>
                  </button>
                  {sessions.length > 1 && (
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        deleteSession(session.id);
                      }}
                      className="opacity-0 group-hover:opacity-100 p-1 hover:bg-muted rounded"
                    >
                      <Trash2 className="h-4 w-4 text-muted-foreground" />
                    </button>
                  )}
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* 主聊天区域 */}
        <div className="flex-1">
          <Card className="h-full flex flex-col rounded-none border-0">
            <div className="flex-1 overflow-y-auto">
              <MessageList messages={messages} />
            </div>
            <div className="border-t p-4 bg-background">
              <ChatInput onSubmit={handleSubmit} isLoading={isPending} />
            </div>
          </Card>
        </div>
      </div>
    </div>
  );
}