'use client';

import { useEffect, useRef } from 'react';
import { Message } from '@/types/chat';
import { cn } from '@/lib/utils';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import rehypeRaw from 'rehype-raw';
import rehypeSanitize from 'rehype-sanitize';

interface MessageListProps {
  messages: Message[];
}

export function MessageList({ messages }: MessageListProps) {
  const messagesEndRef = useRef<HTMLDivElement>(null);

  return (
    <div 
      className="flex-1 overflow-y-auto h-full bg-white dark:bg-gray-900"
    >
      <div className="space-y-4 p-3">
        {messages.length === 0 ? (
          <div className="flex items-center justify-center h-full min-h-[100px]">
            <p className="text-muted-foreground text-center">
              Start a conversation by asking a nutrition-related question.
            </p>
          </div>
        ) : (
          messages.map((message, index) => (
            <div
              key={index}
              className={cn(
                "flex",
                message.isUser ? "justify-end" : "justify-start"
              )}
            >
              {!message.isUser && (
                <div className="flex-shrink-0 w-6 h-6 rounded-full bg-primary flex items-center justify-center text-white mr-2">
                  AI
                </div>
              )}
              <div
                className={cn(
                  "max-w-[80%] rounded-lg px-3 py-2",
                  message.isUser
                    ? "bg-primary text-primary-foreground"
                    : "bg-muted",
                  message.pending && "animate-pulse"
                )}
              >
                {message.isUser ? (
                  <p className={cn(message.pending && "text-muted-foreground")}>
                    {message.text}
                  </p>
                ) : (
                  <div className="markdown-content prose dark:prose-invert prose-sm max-w-none">
                    <ReactMarkdown
                      remarkPlugins={[remarkGfm]}
                      rehypePlugins={[rehypeRaw, rehypeSanitize]}
                      components={{
                        h1: ({node, ...props}) => <h1 className="text-lg font-bold mb-2 mt-1" {...props}/>,
                        h2: ({node, ...props}) => <h2 className="text-base font-semibold mb-2 mt-1" {...props}/>,
                        h3: ({node, ...props}) => <h3 className="text-sm font-medium mb-1 mt-1" {...props}/>,
                        p: ({node, ...props}) => <p className="mb-2" {...props}/>,
                        ul: ({node, ...props}) => <ul className="list-disc list-inside mb-2" {...props}/>,
                        ol: ({node, ...props}) => <ol className="list-decimal list-inside mb-2" {...props}/>,
                        li: ({node, ...props}) => <li className="mb-1" {...props}/>,
                        code: ({node, inline, className, ...props}: {
                          node?: any;
                          inline?: boolean;
                          className?: string;
                        } & React.HTMLAttributes<HTMLElement>) => 
                          inline ? (
                            <code className="bg-muted px-1 py-0.5 rounded" {...props}/>
                          ) : (
                            <code className="block bg-muted p-2 rounded-md my-2 whitespace-pre-wrap" {...props}/>
                          ),
                        blockquote: ({node, ...props}) => 
                          <blockquote className="border-l-4 border-primary pl-4 italic my-2" {...props}/>,
                        hr: ({node, ...props}) => <hr className="my-4 border-muted" {...props}/>,
                        table: ({node, ...props}) => 
                          <div className="overflow-x-auto">
                            <table className="min-w-full divide-y divide-muted my-2" {...props}/>
                          </div>,
                        th: ({node, ...props}) => 
                          <th className="px-2 py-1 bg-muted font-medium" {...props}/>,
                        td: ({node, ...props}) => 
                          <td className="px-2 py-1 border-t border-muted" {...props}/>,
                      }}
                    >
                      {message.text}
                    </ReactMarkdown>
                  </div>
                )}
              </div>
            </div>
          ))
        )}
        <div ref={messagesEndRef} />
      </div>
    </div>
  );
}