import { UserButton as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, SignInButton, useUser } from "@clerk/clerk-react";

export function UserButton() {
  const { isSignedIn, isLoaded } = useUser();

  if (!isLoaded) {
    return null;
  }

  if (!isSignedIn) {
    return (
      <SignInButton mode="modal">
        <button className="rounded-md bg-[#F5F5F0] px-3 py-2 text-sm font-medium text-gray-700 hover:bg-[#EAEAE5] transition-colors">
          Sign in
        </button>
      </SignInButton>
    );
  }

  return <ClerkUserButton afterSignOutUrl="/" />;
} 