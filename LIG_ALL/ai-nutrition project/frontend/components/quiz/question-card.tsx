"use client";

import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { QuestionNode } from "@/lib/quiz-flow";
import { motion } from "framer-motion";
import { cn } from "@/lib/utils";

interface QuestionCardProps {
  question: QuestionNode;
  value: string | string[];
  onChange: (value: string | string[]) => void;
  onNext?: () => void;
}

export function QuestionCard({ question, value, onChange, onNext }: QuestionCardProps) {
  const handleOptionClick = (optionValue: string) => {
    if (question.type === 'radio') {
      onChange(optionValue);
      // Auto advance for single choice questions
      if (onNext) {
        setTimeout(onNext, 300); // Small delay for visual feedback
      }
    } else {
      const currentValues = Array.isArray(value) ? value : [];
      const newValues = currentValues.includes(optionValue)
        ? currentValues.filter((v) => v !== optionValue)
        : [...currentValues, optionValue];
      onChange(newValues);
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      transition={{ duration: 0.3 }}
    >
      <Card>
        <CardContent className="pt-6">
          <h3 className="text-xl font-semibold mb-6">{question.text}</h3>
          <div className="space-y-3">
            {question.options.map((option) => {
              const isSelected = question.type === 'radio'
                ? value === option.value
                : (value as string[])?.includes(option.value);

              return (
                <Button
                  key={option.value}
                  variant="outline"
                  className={cn(
                    "w-full justify-start text-left h-auto p-4 font-normal hover:bg-accent",
                    isSelected && "bg-accent"
                  )}
                  onClick={() => handleOptionClick(option.value)}
                >
                  <span className="flex-1">{option.label}</span>
                </Button>
              );
            })}
          </div>
          {question.type === 'checkbox' && Array.isArray(value) && value.length > 0 && (
            <div className="mt-4 text-sm text-muted-foreground text-center">
              Click Next to continue after selecting your options
            </div>
          )}
        </CardContent>
      </Card>
    </motion.div>
  );
}