"use client";

import { motion } from "framer-motion";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { products } from "@/lib/products";
import { QuizState } from "@/lib/types";

interface RecommendationsProps {
  answers: QuizState;
  onRestart: () => void;
}

export function Recommendations({ answers, onRestart }: RecommendationsProps) {
  const recommendedProduct = products.find(
    (product) => product.category === answers.healthConcern
  );

  if (!recommendedProduct) {
    return (
      <div className="text-center">
        <h2 className="text-2xl font-bold mb-4">No Recommendations Available</h2>
        <p className="text-muted-foreground mb-8">
          We couldn't find a product matching your specific needs. Please try the quiz again.
        </p>
        <Button variant="outline" onClick={onRestart}>
          Take Quiz Again
        </Button>
      </div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      transition={{ duration: 0.3 }}
      className="space-y-8"
    >
      <div className="text-center">
        <h2 className="text-2xl font-bold mb-4">Your Personalized Recommendation</h2>
        <p className="text-muted-foreground mb-8">
          Based on your responses, we've selected this product specifically for you.
        </p>
      </div>

      <div className="max-w-xl mx-auto">
        <Card className="flex flex-col">
          <CardHeader className="p-0">
            <img
              src={recommendedProduct.imageUrl}
              alt={recommendedProduct.name}
              className="h-64 w-full object-cover rounded-t-lg"
            />
          </CardHeader>
          <CardContent className="flex-1 p-6">
            <CardTitle className="text-2xl mb-4">{recommendedProduct.name}</CardTitle>
            <p className="text-lg text-muted-foreground mb-6">
              {recommendedProduct.description}
            </p>
            <div className="space-y-6">
              <div>
                <h3 className="font-semibold mb-2">Key Benefits:</h3>
                <div className="flex flex-wrap gap-2">
                  {recommendedProduct.benefits.map((benefit) => (
                    <Badge key={benefit} variant="secondary" className="text-sm">
                      {benefit}
                    </Badge>
                  ))}
                </div>
              </div>
              <div className="flex items-center justify-between">
                <p className="text-2xl font-bold">${recommendedProduct.price}</p>
                <Button size="lg" asChild>
                  <a href={recommendedProduct.purchaseLink} target="_blank" rel="noopener noreferrer">
                    Learn More
                  </a>
                </Button>
              </div>
            </div>
          </CardContent>
          <CardFooter className="p-6 pt-0 flex flex-col gap-4">
            <p className="text-sm text-muted-foreground">
              This recommendation is tailored to your health concern: {" "}
              <span className="font-medium">
                {answers.healthConcern?.replace(/_/g, " ").replace(/\b\w/g, c => c.toUpperCase())}
              </span>
            </p>
            <Button variant="outline" onClick={onRestart} className="w-full">
              Take Quiz Again
            </Button>
          </CardFooter>
        </Card>
      </div>
    </motion.div>
  );
}