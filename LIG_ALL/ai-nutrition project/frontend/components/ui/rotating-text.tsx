"use client";

import { useEffect, useState } from 'react';
import { AnimatePresence, motion } from 'framer-motion';

interface RotatingWord {
  text: string;
  color: string;
}

interface RotatingTextProps {
  words: RotatingWord[];
  interval?: number;
}

export function RotatingText({ words, interval = 2000 }: RotatingTextProps) {
  const [index, setIndex] = useState(0);
  const [isVisible, setIsVisible] = useState(true);

  useEffect(() => {
    const timer = setInterval(() => {
      setIsVisible(false);
      setTimeout(() => {
        setIndex((prev) => (prev + 1) % words.length);
        setIsVisible(true);
      }, 200);
    }, interval);

    return () => clearInterval(timer);
  }, [interval, words.length]);

  return (
    <div className="h-[1.2em] relative overflow-hidden">
      <AnimatePresence mode="wait">
        {isVisible && (
          <motion.span
            key={words[index].text}
            initial={{ y: 20, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            exit={{ y: -20, opacity: 0 }}
            transition={{ duration: 0.2 }}
            style={{ color: words[index].color }}
            className="absolute"
          >
            {words[index].text}
          </motion.span>
        )}
      </AnimatePresence>
    </div>
  );
}