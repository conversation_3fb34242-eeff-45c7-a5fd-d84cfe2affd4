import { type QuestionNode } from '../types/questions';

export const heartQuestions: Record<string, QuestionNode> = {
  exercise_habits: {
    id: 'exercise_habits',
    text: 'What type of exercise do you regularly perform?',
    type: 'checkbox',
    options: [
      { value: 'cardio', label: 'Cardio (running, cycling)' },
      { value: 'strength', label: 'Strength training' },
      { value: 'walking', label: 'Walking' },
      { value: 'none', label: 'No regular exercise' }
    ]
  },
  diet_assessment: {
    id: 'diet_assessment',
    text: 'How would you describe your diet?',
    type: 'radio',
    options: [
      { value: 'healthy', label: 'Healthy and balanced' },
      { value: 'moderate', label: 'Moderately healthy' },
      { value: 'poor', label: 'Could use improvement' },
      { value: 'unknown', label: 'Not sure' }
    ]
  },
  heart_monitoring: {
    id: 'heart_monitoring',
    text: 'Do you monitor your heart rate or blood pressure?',
    type: 'radio',
    options: [
      { value: 'regularly', label: 'Yes, regularly' },
      { value: 'sometimes', label: 'Yes, occasionally' },
      { value: 'rarely', label: 'Rarely' },
      { value: 'never', label: 'Never' }
    ]
  },
  family_history: {
    id: 'family_history',
    text: 'Is there a history of heart disease in your family?',
    type: 'radio',
    options: [
      { value: 'yes', label: 'Yes' },
      { value: 'no', label: 'No' },
      { value: 'unknown', label: 'Not sure' }
    ]
  }
};