import { type QuestionNode } from '../types/questions';

export const immuneQuestions: Record<string, QuestionNode> = {
  illness_frequency: {
    id: 'illness_frequency',
    text: 'How often do you get sick (cold, flu, etc.)?',
    type: 'radio',
    options: [
      { value: 'rarely', label: 'Rarely (0-1 times/year)' },
      { value: 'occasionally', label: 'Occasionally (2-3 times/year)' },
      { value: 'frequently', label: 'Frequently (4+ times/year)' },
      { value: 'chronic', label: 'Chronic health issues' }
    ]
  },
  recovery_time: {
    id: 'recovery_time',
    text: 'How long does it typically take you to recover from illness?',
    type: 'radio',
    options: [
      { value: 'quick', label: 'Quick (1-3 days)' },
      { value: 'normal', label: 'Normal (4-7 days)' },
      { value: 'extended', label: '1-2 weeks' },
      { value: 'prolonged', label: 'More than 2 weeks' }
    ]
  },
  lifestyle_factors: {
    id: 'lifestyle_factors',
    text: 'Which factors might affect your immune system?',
    type: 'checkbox',
    options: [
      { value: 'stress', label: 'High stress levels' },
      { value: 'sleep', label: 'Poor sleep' },
      { value: 'diet', label: 'Unbalanced diet' },
      { value: 'exercise', label: 'Lack of exercise' }
    ]
  }
};