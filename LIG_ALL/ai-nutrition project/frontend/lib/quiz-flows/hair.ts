import { type QuestionNode } from '../types/questions';

export const hairQuestions: Record<string, QuestionNode> = {
  hair_loss_pattern: {
    id: 'hair_loss_pattern',
    text: 'What pattern of hair loss are you experiencing?',
    type: 'radio',
    options: [
      { value: 'crown', label: 'Thinning at crown' },
      { value: 'hairline', label: 'Receding hairline' },
      { value: 'overall', label: 'Overall thinning' },
      { value: 'patches', label: 'Patchy hair loss' }
    ]
  },
  hair_loss_duration: {
    id: 'hair_loss_duration',
    text: 'How long have you been experiencing hair loss?',
    type: 'radio',
    options: [
      { value: 'recent', label: 'Less than 6 months' },
      { value: 'moderate', label: '6 months to 1 year' },
      { value: 'long', label: '1-2 years' },
      { value: 'extended', label: 'More than 2 years' }
    ]
  },
  hair_care_routine: {
    id: 'hair_care_routine',
    text: 'How would you describe your hair care routine?',
    type: 'checkbox',
    options: [
      { value: 'minimal', label: 'Minimal maintenance' },
      { value: 'regular', label: 'Regular washing and styling' },
      { value: 'extensive', label: 'Extensive styling and treatment' },
      { value: 'professional', label: 'Regular professional treatments' }
    ]
  },
  scalp_condition: {
    id: 'scalp_condition',
    text: 'How would you describe your scalp condition?',
    type: 'checkbox',
    options: [
      { value: 'normal', label: 'Normal' },
      { value: 'dry', label: 'Dry and flaky' },
      { value: 'oily', label: 'Oily' },
      { value: 'itchy', label: 'Itchy or irritated' }
    ]
  }
};