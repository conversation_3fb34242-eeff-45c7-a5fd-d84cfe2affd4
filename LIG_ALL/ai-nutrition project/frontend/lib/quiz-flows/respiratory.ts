import { type QuestionNode } from '../types/questions';

export const respiratoryQuestions: Record<string, QuestionNode> = {
  nasal_symptoms: {
    id: 'nasal_symptoms',
    text: 'Which nasal symptoms do you experience most frequently?',
    type: 'checkbox',
    options: [
      { value: 'congestion', label: 'Nasal congestion' },
      { value: 'runny_nose', label: 'Runny nose' },
      { value: 'sneezing', label: 'Frequent sneezing' },
      { value: 'sinus_pressure', label: 'Sinus pressure' }
    ]
  },
  symptom_triggers: {
    id: 'symptom_triggers',
    text: 'What triggers your symptoms?',
    type: 'checkbox',
    options: [
      { value: 'allergies', label: 'Seasonal allergies' },
      { value: 'dust', label: 'Dust and indoor allergens' },
      { value: 'weather', label: 'Weather changes' },
      { value: 'unknown', label: 'Not sure' }
    ]
  },
  smell_assessment: {
    id: 'smell_assessment',
    text: 'How would you rate your sense of smell?',
    type: 'radio',
    options: [
      { value: 'normal', label: 'Normal' },
      { value: 'reduced', label: 'Slightly reduced' },
      { value: 'very_reduced', label: 'Significantly reduced' },
      { value: 'none', label: 'Cannot smell' }
    ]
  },
  weather_sensitivity: {
    id: 'weather_sensitivity',
    text: 'How do weather changes affect your symptoms?',
    type: 'radio',
    options: [
      { value: 'no_effect', label: 'No effect' },
      { value: 'mild', label: 'Mild worsening' },
      { value: 'moderate', label: 'Moderate worsening' },
      { value: 'severe', label: 'Severe worsening' }
    ]
  }
};