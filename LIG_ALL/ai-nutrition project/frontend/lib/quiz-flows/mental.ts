import { type QuestionNode } from '../types/questions';

export const mentalQuestions: Record<string, QuestionNode> = {
  sleep_quality: {
    id: 'sleep_quality',
    text: 'How would you rate your sleep quality?',
    type: 'radio',
    options: [
      { value: 'excellent', label: 'Excellent', nextQuestionId: 'stress_sources' },
      { value: 'good', label: 'Good', nextQuestionId: 'stress_sources' },
      { value: 'fair', label: 'Fair', nextQuestionId: 'stress_sources' },
      { value: 'poor', label: 'Poor', nextQuestionId: 'stress_sources' }
    ]
  },
  stress_sources: {
    id: 'stress_sources',
    text: 'What are your main sources of stress?',
    type: 'checkbox',
    options: [
      { value: 'work', label: 'Work/Career' },
      { value: 'relationships', label: 'Relationships' },
      { value: 'health', label: 'Health concerns' },
      { value: 'financial', label: 'Financial matters' }
    ]
  },
  mood_patterns: {
    id: 'mood_patterns',
    text: 'How would you describe your mood patterns?',
    type: 'radio',
    options: [
      { value: 'stable', label: 'Generally stable' },
      { value: 'variable', label: 'Somewhat variable' },
      { value: 'fluctuating', label: 'Frequently changing' },
      { value: 'unpredictable', label: 'Highly unpredictable' }
    ]
  }
};