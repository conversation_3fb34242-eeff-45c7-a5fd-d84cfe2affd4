import { type QuestionNode } from '../types/questions';

export const eyeQuestions: Record<string, QuestionNode> = {
  vision_status: {
    id: 'vision_status',
    text: 'Do you currently use vision correction?',
    type: 'radio',
    options: [
      { value: 'none', label: 'No correction needed' },
      { value: 'glasses', label: 'Glasses' },
      { value: 'contacts', label: 'Contact lenses' },
      { value: 'both', label: 'Both glasses and contacts' }
    ]
  },
  screen_time: {
    id: 'screen_time',
    text: 'How many hours do you spend looking at screens daily?',
    type: 'radio',
    options: [
      { value: 'low', label: 'Less than 4 hours' },
      { value: 'moderate', label: '4-8 hours' },
      { value: 'high', label: '8-12 hours' },
      { value: 'very_high', label: 'More than 12 hours' }
    ]
  },
  eye_symptoms: {
    id: 'eye_symptoms',
    text: 'Which eye symptoms do you experience?',
    type: 'checkbox',
    options: [
      { value: 'dryness', label: 'Eye dryness' },
      { value: 'strain', label: 'Eye strain' },
      { value: 'fatigue', label: 'Eye fatigue' },
      { value: 'blurriness', label: 'Blurred vision' }
    ]
  }
};