import { 
  HealthConcern, 
  HealthGoal, 
  ActivityLevel, 
  DietaryRestriction,
  SleepDuration,
  StressLevel,
  AgeGroup,
  Gender
} from './types';

export const genders: { value: Gender; label: string }[] = [
  { value: 'male', label: 'Male' },
  { value: 'female', label: 'Female' },
  { value: 'non_binary', label: 'Non-binary' },
  { value: 'prefer_not_to_say', label: 'Prefer not to say' },
];

export const ageGroups: { value: AgeGroup; label: string }[] = [
  { value: 'under_18', label: 'Under 18' },
  { value: '18_to_29', label: '18–29' },
  { value: '30_to_49', label: '30–49' },
  { value: '50_to_65', label: '50–65' },
  { value: 'over_65', label: '65+' },
];

export const healthConcerns: { value: HealthConcern; label: string }[] = [
  { value: 'eye_care', label: 'Eye Care' },
  { value: 'hair_renew', label: 'Hair Renew' },
  { value: 'bone_joint_health', label: 'Bone/Joint Health' },
  { value: 'immune_support', label: 'Immune Support' },
  { value: 'energy_boost', label: 'Energy Boost' },
  { value: 'stress_management', label: 'Stress Management' },
  { value: 'digestion_gut_health', label: 'Digestion/Gut Health' },
  { value: 'heart_health', label: 'Heart Health' },
  { value: 'weight_management', label: 'Weight Management' },
  { value: 'skin_health', label: 'Skin Health' },
  { value: 'sleep_support', label: 'Sleep Support' },
];

export const healthGoals: { value: HealthGoal; label: string }[] = [
  { value: 'increase_energy', label: 'Increase Energy' },
  { value: 'improve_mental_focus', label: 'Improve Mental Focus' },
  { value: 'enhance_physical_performance', label: 'Enhance Physical Performance' },
  { value: 'manage_weight', label: 'Manage Weight' },
  { value: 'promote_better_sleep', label: 'Promote Better Sleep' },
  { value: 'boost_immunity', label: 'Boost Immunity' },
  { value: 'improve_digestion', label: 'Improve Digestion' },
];

export const activityLevels: { value: ActivityLevel; label: string }[] = [
  { value: 'sedentary', label: 'Sedentary' },
  { value: 'light_activity', label: 'Light Activity' },
  { value: 'moderate_activity', label: 'Moderate Activity' },
  { value: 'high_activity', label: 'High Activity' },
];

export const dietaryRestrictions: { value: DietaryRestriction; label: string }[] = [
  { value: 'gluten_free', label: 'Gluten-free' },
  { value: 'lactose_free', label: 'Lactose-free' },
  { value: 'nut_free', label: 'Nut-free' },
  { value: 'soy_free', label: 'Soy-free' },
  { value: 'none', label: 'None' },
];

export const sleepDurations: { value: SleepDuration; label: string }[] = [
  { value: 'less_than_5', label: 'Less than 5 hours' },
  { value: '5_to_6', label: '5–6 hours' },
  { value: '6_to_8', label: '6–8 hours' },
  { value: 'more_than_8', label: 'More than 8 hours' },
];

export const stressLevels: { value: StressLevel; label: string }[] = [
  { value: 'low', label: 'Low' },
  { value: 'moderate', label: 'Moderate' },
  { value: 'high', label: 'High' },
  { value: 'extreme', label: 'Extreme' },
];