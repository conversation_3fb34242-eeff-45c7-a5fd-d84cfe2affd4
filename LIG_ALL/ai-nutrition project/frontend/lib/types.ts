export type HealthConcern = 
  | 'eye_care' 
  | 'hair_renew' 
  | 'bone_joint_health' 
  | 'immune_support' 
  | 'energy_boost'
  | 'stress_management'
  | 'digestion_gut_health'
  | 'heart_health'
  | 'weight_management'
  | 'skin_health'
  | 'sleep_support';

export type HealthGoal =
  | 'increase_energy'
  | 'improve_mental_focus'
  | 'enhance_physical_performance'
  | 'manage_weight'
  | 'promote_better_sleep'
  | 'boost_immunity'
  | 'improve_digestion';

export type ActivityLevel =
  | 'sedentary'
  | 'light_activity'
  | 'moderate_activity'
  | 'high_activity';

export type DietaryRestriction =
  | 'gluten_free'
  | 'lactose_free'
  | 'nut_free'
  | 'soy_free'
  | 'none';

export type SleepDuration =
  | 'less_than_5'
  | '5_to_6'
  | '6_to_8'
  | 'more_than_8';

export type StressLevel =
  | 'low'
  | 'moderate'
  | 'high'
  | 'extreme';

export type AgeGroup =
  | 'under_18'
  | '18_to_29'
  | '30_to_49'
  | '50_to_65'
  | 'over_65';

export type Gender =
  | 'male'
  | 'female'
  | 'non_binary'
  | 'prefer_not_to_say';

export interface QuizState {
  gender: Gender | null;
  ageGroup: AgeGroup | null;
  healthConcern: HealthConcern | null;
  healthGoal: HealthGoal | null;
  activityLevel: ActivityLevel | null;
  dietaryRestrictions: DietaryRestriction | null;
  sleepDuration: SleepDuration | null;
  stressLevel: StressLevel | null;
  step: number;
}

export interface Product {
  id: string;
  name: string;
  description: string;
  imageUrl: string;
  price: number;
  benefits: string[];
  category: string;
  purchaseLink: string;
}