export type HealthConcern = 
  | 'respiratory_health'
  | 'heart_health'
  | 'hair_renew'
  | 'mental_health'
  | 'eye_care'
  | 'immune_support';

export type Gender = 'male' | 'female' | 'non_binary' | 'prefer_not_to_say';
export type AgeGroup = 'under_18' | '18_to_29' | '30_to_49' | '50_to_65' | 'over_65';

// Common types shared across different health concerns
export type Frequency = 'rarely' | 'sometimes' | 'often' | 'always';
export type Severity = 'mild' | 'moderate' | 'severe' | 'extreme';
export type Duration = 'recent' | 'months' | 'years' | 'lifelong';
export type FamilyHistory = 'yes' | 'no' | 'unknown';
export type DietaryHabit = 'poor' | 'fair' | 'good' | 'excellent';
export type Lifestyle = 'sedentary' | 'moderate' | 'active' | 'very_active';

export interface BaseQuizState {
  gender: Gender | null;
  ageGroup: AgeGroup | null;
  healthConcern: HealthConcern | null;
}