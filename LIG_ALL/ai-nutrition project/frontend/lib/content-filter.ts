export type ContentValidationResult = {
  isValid: boolean;
  reason?: string;
};

export function validateHealthContent(text: string): ContentValidationResult {
  // 定义健康和营养相关的关键词
  const healthKeywords = [
    'nutrition', 'health', 'diet', 'food', 'meal', 'vitamin', 'mineral',
    'protein', 'carbohydrate', 'fat', 'calorie', 'weight', 'exercise',
    'nutrient', 'eating', 'healthy', 'wellness', 'metabolism', 'supplement',
    'vegetable', 'fruit', 'meat', 'fish', 'dairy', 'grain', 'fiber',
    'hydration', 'water', 'macro', 'micro', 'antioxidant', '营养', '健康',
    '饮食', '食物', '维生素', '蛋白质', '碳水', '脂肪', '卡路里', '体重',
    '运动', '营养素', '饮食', '健康', '代谢', '补充剂', '蔬菜', '水果',
    '肉类', '鱼类', '乳制品', '谷物', '纤维', '水分', '抗氧化'
  ];

  // 定义不相关话题的关键词
  const irrelevantKeywords = [
    'stock market', 'investment', 'politics', 'movie', 'entertainment',
    'gaming', 'sports car', 'fashion', 'celebrity', 'gossip', 'stock price',
    'real estate', '股市', '投资', '政治', '电影', '娱乐', '游戏', '跑车',
    '时尚', '明星', '八卦', '股票', '房地产'
  ];

  // 转换为小写进行比较
  const lowercaseText = text.toLowerCase();

  // 检查是否包含健康相关关键词
  const hasHealthContent = healthKeywords.some(keyword => 
    lowercaseText.includes(keyword.toLowerCase())
  );

  // 检查是否包含不相关话题的关键词
  const hasIrrelevantContent = irrelevantKeywords.some(keyword => 
    lowercaseText.includes(keyword.toLowerCase())
  );

  // 验证内容长度
  if (text.length < 5) {
    return {
      isValid: false,
      reason: 'Content is too short to be meaningful'
    };
  }

  // 内容审核逻辑
  if (!hasHealthContent) {
    return {
      isValid: false,
      reason: 'Content is not related to health or nutrition'
    };
  }

  if (hasIrrelevantContent) {
    return {
      isValid: false,
      reason: 'Content contains irrelevant topics'
    };
  }

  return {
    isValid: true
  };
}

// 用于修改不合规的内容
export function modifyInvalidContent(text: string): string {
  return `I apologize, but I can only provide information about nutrition and health-related topics. Please feel free to ask any questions about diet, nutrition, wellness, or healthy living.`;
}