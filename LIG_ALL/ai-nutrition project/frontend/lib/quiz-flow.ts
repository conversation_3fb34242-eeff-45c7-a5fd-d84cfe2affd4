import { HealthConcern, HealthGoal } from './types';

export type QuestionNode = {
  id: string;
  text: string;
  options: { value: string; label: string; nextQuestionId?: string }[];
  type: 'radio' | 'checkbox';
};

export const questionFlow: Record<string, QuestionNode> = {
  gender: {
    id: 'gender',
    text: 'What is your gender?',
    type: 'radio',
    options: [
      { value: 'male', label: 'Male', nextQuestionId: 'age_group' },
      { value: 'female', label: 'Female', nextQuestionId: 'age_group' },
      { value: 'non_binary', label: 'Non-binary', nextQuestionId: 'age_group' },
      { value: 'prefer_not_to_say', label: 'Prefer not to say', nextQuestionId: 'age_group' }
    ]
  },
  age_group: {
    id: 'age_group',
    text: 'What is your age group?',
    type: 'radio',
    options: [
      { value: 'under_18', label: 'Under 18', nextQuestionId: 'health_concern' },
      { value: '18_to_29', label: '18–29', nextQuestionId: 'health_concern' },
      { value: '30_to_49', label: '30–49', nextQuestionId: 'health_concern' },
      { value: '50_to_65', label: '50–65', nextQuestionId: 'health_concern' },
      { value: 'over_65', label: '65+', nextQuestionId: 'health_concern' }
    ]
  },
  health_concern: {
    id: 'health_concern',
    text: 'What is your recent health concern?',
    type: 'radio',
    options: [
      { 
        value: 'sleep_support',
        label: 'Sleep Support',
        nextQuestionId: 'sleep_duration'
      },
      {
        value: 'stress_management',
        label: 'Stress Management',
        nextQuestionId: 'stress_level'
      },
      {
        value: 'immune_support',
        label: 'Immune Support',
        nextQuestionId: 'activity_level'
      },
      {
        value: 'digestion_gut_health',
        label: 'Digestion/Gut Health',
        nextQuestionId: 'dietary_restrictions'
      },
      {
        value: 'energy_boost',
        label: 'Energy Boost',
        nextQuestionId: 'sleep_duration'
      }
    ]
  },
  sleep_duration: {
    id: 'sleep_duration',
    text: 'How much sleep do you typically get per night?',
    type: 'radio',
    options: [
      { value: 'less_than_5', label: 'Less than 5 hours', nextQuestionId: 'stress_level' },
      { value: '5_to_6', label: '5-6 hours', nextQuestionId: 'stress_level' },
      { value: '6_to_8', label: '6-8 hours', nextQuestionId: 'stress_level' },
      { value: 'more_than_8', label: 'More than 8 hours', nextQuestionId: 'stress_level' }
    ]
  },
  stress_level: {
    id: 'stress_level',
    text: 'How would you describe your stress levels?',
    type: 'radio',
    options: [
      { value: 'low', label: 'Low', nextQuestionId: 'activity_level' },
      { value: 'moderate', label: 'Moderate', nextQuestionId: 'activity_level' },
      { value: 'high', label: 'High', nextQuestionId: 'activity_level' },
      { value: 'extreme', label: 'Extreme', nextQuestionId: 'activity_level' }
    ]
  },
  activity_level: {
    id: 'activity_level',
    text: 'What is your daily activity level?',
    type: 'radio',
    options: [
      { value: 'sedentary', label: 'Sedentary', nextQuestionId: 'dietary_restrictions' },
      { value: 'light_activity', label: 'Light Activity', nextQuestionId: 'dietary_restrictions' },
      { value: 'moderate_activity', label: 'Moderate Activity', nextQuestionId: 'dietary_restrictions' },
      { value: 'high_activity', label: 'High Activity', nextQuestionId: 'dietary_restrictions' }
    ]
  },
  dietary_restrictions: {
    id: 'dietary_restrictions',
    text: 'Do you have any dietary restrictions?',
    type: 'checkbox',
    options: [
      { value: 'gluten_free', label: 'Gluten-free' },
      { value: 'lactose_free', label: 'Lactose-free' },
      { value: 'nut_free', label: 'Nut-free' },
      { value: 'soy_free', label: 'Soy-free' },
      { value: 'none', label: 'None' }
    ]
  }
};