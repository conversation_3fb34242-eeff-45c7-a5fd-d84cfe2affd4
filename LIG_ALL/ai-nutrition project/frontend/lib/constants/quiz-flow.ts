import { type QuestionNode } from '@/lib/types/questions';
import { respiratoryQuestions } from '../quiz-flows/respiratory';
import { heartQuestions } from '../quiz-flows/heart';
import { hairQuestions } from '../quiz-flows/hair';
import { mentalQuestions } from '../quiz-flows/mental';
import { eyeQuestions } from '../quiz-flows/eye';
import { immuneQuestions } from '../quiz-flows/immune';

// Common initial questions for all flows
const commonQuestions: Record<string, QuestionNode> = {
  gender: {
    id: 'gender',
    text: 'What is your gender?',
    type: 'radio',
    options: [
      { value: 'male', label: 'Male', nextQuestionId: 'age_group' },
      { value: 'female', label: 'Female', nextQuestionId: 'age_group' },
      { value: 'non_binary', label: 'Non-binary', nextQuestionId: 'age_group' },
      { value: 'prefer_not_to_say', label: 'Prefer not to say', nextQuestionId: 'age_group' }
    ]
  },
  age_group: {
    id: 'age_group',
    text: 'What is your age group?',
    type: 'radio',
    options: [
      { value: 'under_18', label: 'Under 18', nextQuestionId: 'concern_specific' },
      { value: '18_to_29', label: '18–29', nextQuestionId: 'concern_specific' },
      { value: '30_to_49', label: '30–49', nextQuestionId: 'concern_specific' },
      { value: '50_to_65', label: '50–65', nextQuestionId: 'concern_specific' },
      { value: 'over_65', label: '65+', nextQuestionId: 'concern_specific' }
    ]
  }
};

// Map health concerns to their specific question flows
export const healthConcernFlows = {
  respiratory_health: respiratoryQuestions,
  heart_health: heartQuestions,
  hair_renew: hairQuestions,
  mental_health: mentalQuestions,
  eye_care: eyeQuestions,
  immune_support: immuneQuestions
};

export const questionFlow = {
  ...commonQuestions,
  // Each health concern's questions will be dynamically added based on the user's selection
};