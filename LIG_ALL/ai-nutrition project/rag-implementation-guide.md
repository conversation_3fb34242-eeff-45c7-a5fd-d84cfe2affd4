# RAG Implementation Guide for Next.js + TypeScript Project

## Project Context

This is a Next.js 14 project with TypeScript, using:
- Claude 3.5 Sonnet for LLM
- Clerk for authentication
- Shadcn/ui for components
- Tailwind for styling

The goal is to implement a RAG system for pregnancy-related Q&A, with test data provided in pregnancy-qa.json.

## Implementation Requirements

1. **Vector Store Implementation**
- Create a vector store using ChromaDB's JavaScript client
- Implement document embedding and storage
- Enable efficient similarity search
- Support metadata filtering

2. **RAG Integration**
- Implement context retrieval
- Design prompt templates
- Handle Claude 3.5 Sonnet integration
- Manage conversation context

3. **API Design**
- Create RESTful endpoints
- Implement error handling
- Add rate limiting
- Enable response streaming

## Directory Structure

```plaintext
/app
  /api
    /chat
      route.ts  // Chat endpoint
    /vectors
      route.ts  // Vector operations
/lib
  /rag
    types.ts    // Type definitions
    store.ts    // Vector store implementation
    prompts.ts  // Prompt templates
    claude.ts   // Claude client wrapper
/components
  /chat
    ChatInput.tsx
    ChatMessages.tsx
    MessageItem.tsx
```

## Implementation Steps

1. **Set up Vector Store**

```typescript
// lib/rag/store.ts
import { ChromaClient, OpenAIEmbeddingFunction } from 'chromadb'

interface Document {
  id: string
  content: string
  metadata: {
    category: string
    stage: string
    importance: string
  }
}

export class VectorStore {
  private client: ChromaClient
  private collection: any

  constructor() {
    this.client = new ChromaClient()
    this.collection = this.client.getOrCreateCollection({
      name: "pregnancy_qa",
      metadata: { "description": "Pregnancy Q&A knowledge base" }
    })
  }

  async addDocuments(documents: Document[]) {
    // Implementation
  }

  async searchSimilar(query: string, topK: number = 3) {
    // Implementation
  }
}
```

2. **Create Chat API Route**

```typescript
// app/api/chat/route.ts
import { Anthropic } from '@anthropic-ai/sdk'
import { VectorStore } from '@/lib/rag/store'
import { StreamingTextResponse } from 'ai'

const anthropic = new Anthropic({
  apiKey: process.env.ANTHROPIC_API_KEY!
})

export async function POST(req: Request) {
  const { messages } = await req.json()
  const latestMessage = messages[messages.length - 1]

  // Get relevant documents
  const vectorStore = new VectorStore()
  const docs = await vectorStore.searchSimilar(latestMessage.content)

  // Create prompt with context
  const prompt = `
    You are a knowledgeable pregnancy advisor. 
    Use this reference information to answer the question:
    ${docs.map(d => d.content).join('\n\n')}

    Question: ${latestMessage.content}

    Provide a clear, accurate and supportive answer. If the information isn't 
    in the references, say so and suggest consulting a healthcare provider.
  `

  // Generate streaming response
  const response = await anthropic.messages.create({
    model: 'claude-3-sonnet-20240229',
    max_tokens: 1024,
    stream: true,
    messages: [{ role: 'user', content: prompt }]
  })

  return new StreamingTextResponse(response.toReadableStream())
}
```

3. **Implement Chat Components**

```typescript
// components/chat/ChatInput.tsx
'use client'

import { useState } from 'react'
import { Button } from '../ui/button'
import { Textarea } from '../ui/textarea'

export function ChatInput({ onSubmit }: { onSubmit: (message: string) => void }) {
  const [input, setInput] = useState('')

  return (
    <div className="flex gap-2 items-end p-4">
      <Textarea 
        value={input}
        onChange={(e) => setInput(e.target.value)}
        placeholder="Ask a question..."
        className="min-h-[80px]"
      />
      <Button 
        onClick={() => {
          onSubmit(input)
          setInput('')
        }}
      >
        Send
      </Button>
    </div>
  )
}
```

## Error Handling

1. **Vector Store Errors**
```typescript
export class VectorStoreError extends Error {
  constructor(message: string, public cause?: Error) {
    super(message)
    this.name = 'VectorStoreError'
  }
}

// Usage
try {
  await vectorStore.searchSimilar(query)
} catch (error) {
  throw new VectorStoreError('Failed to search vectors', error as Error)
}
```

2. **API Error Responses**
```typescript
export async function POST(req: Request) {
  try {
    // ... implementation
  } catch (error) {
    console.error('Chat error:', error)
    return Response.json(
      { error: 'Failed to generate response' },
      { status: 500 }
    )
  }
}
```

## Security Considerations

1. **Input Validation**
```typescript
import { z } from 'zod'

const ChatRequestSchema = z.object({
  messages: z.array(z.object({
    role: z.enum(['user', 'assistant']),
    content: z.string().min(1).max(1000)
  }))
})
```

2. **Rate Limiting**
```typescript
import { rateLimit } from '@/lib/rate-limit'

export async function POST(req: Request) {
  const ip = req.headers.get('x-forwarded-for')
  const limit = await rateLimit(ip)
  
  if (!limit.success) {
    return new Response('Too Many Requests', { status: 429 })
  }
  // ... rest of implementation
}
```

## Performance Optimization

1. **Caching Strategy**
```typescript
import { redis } from '@/lib/redis'

async function getCachedResponse(query: string) {
  const cached = await redis.get(`chat:${query}`)
  if (cached) return JSON.parse(cached)
  return null
}
```

2. **Batch Processing**
```typescript
export class VectorStore {
  async addDocumentsInBatches(documents: Document[], batchSize = 100) {
    for (let i = 0; i < documents.length; i += batchSize) {
      const batch = documents.slice(i, i + batchSize)
      await this.addDocuments(batch)
    }
  }
}
```

## Testing Strategy

1. **Unit Tests**
```typescript
// __tests__/lib/rag/store.test.ts
import { VectorStore } from '@/lib/rag/store'

describe('VectorStore', () => {
  it('should retrieve relevant documents', async () => {
    const store = new VectorStore()
    const results = await store.searchSimilar('孕早期营养')
    expect(results[0].metadata.category).toBe('营养指导')
  })
})
```

2. **Integration Tests**
```typescript
// __tests__/api/chat.test.ts
import { POST } from '@/app/api/chat/route'

describe('Chat API', () => {
  it('should generate relevant responses', async () => {
    const req = new Request('http://localhost:3000/api/chat', {
      method: 'POST',
      body: JSON.stringify({
        messages: [{
          role: 'user',
          content: '孕早期需要补充什么营养？'
        }]
      })
    })
    
    const res = await POST(req)
    expect(res.status).toBe(200)
  })
})
```

## Deployment Considerations

1. **Environment Variables**
```env
ANTHROPIC_API_KEY=
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=
CLERK_SECRET_KEY=
CHROMADB_HOST=
REDIS_URL=
```

2. **Resource Scaling**
- Configure appropriate memory limits
- Set up monitoring and alerts
- Implement proper error tracking

## Next Steps

1. Implement the basic vector store and test with sample data
2. Create the chat API endpoint with proper error handling
3. Build the chat interface components
4. Add authentication and rate limiting
5. Implement caching and performance optimizations
6. Write tests and set up CI/CD
7. Deploy and monitor

Would you like me to focus on implementing any specific part first?