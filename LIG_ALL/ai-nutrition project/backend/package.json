{"name": "backend", "version": "1.0.0", "main": "dist/app.js", "scripts": {"start": "node dist/app.js", "dev": "ts-node src/app.ts", "build": "tsc", "watch": "tsc -w"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@anthropic-ai/sdk": "^0.33.1", "@types/cors": "^2.8.17", "@types/express": "^5.0.0", "@types/node": "^22.10.2", "cors": "^2.8.5", "dotenv": "^16.4.7", "express": "^4.21.2", "ts-node": "^10.9.2", "typescript": "^5.7.2"}}