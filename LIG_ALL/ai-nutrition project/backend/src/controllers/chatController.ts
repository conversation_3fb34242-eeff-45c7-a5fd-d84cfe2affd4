import { Request, Response } from 'express';
import { ClaudeService } from '../services/claudeService';

const claudeService = new ClaudeService();

export const chatController = {
  async sendMessage(req: Request, res: Response) {
    try {
      const { messages } = req.body;
      
      if (!Array.isArray(messages)) {
        return res.status(400).json({ error: 'Messages must be an array' });
      }

      const response = await claudeService.sendMessage(messages);
      res.json({ response });
    } catch (error) {
      console.error('Chat controller error:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }
}; 