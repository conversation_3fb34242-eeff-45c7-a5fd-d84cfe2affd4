# AI Nutrition Project - Build Instructions

## Project Overview
This is a modern AI-powered nutrition guidance platform built with Next.js 14, featuring real-time chat capabilities, nutrition quizzes, and product recommendations. The application follows a modern tech stack and component-based architecture.

## Core Technology Stack

### Frontend Framework
- Next.js 14.1.0 with App Router
- React 18.2.0
- TypeScript 5.3+
- Tailwind CSS 3.4.1

### UI Components & Styling
- Radix UI for accessible components
- shadcn/ui component system
- Tailwind CSS with custom configuration
- Framer Motion for animations
- Lucide React for icons

### Authentication & AI
- Clerk for authentication
- Anthropic AI SDK for chat functionality

## Project Structure Guide

```
├── app/                    
│   ├── chat/              # AI chat interface
│   ├── quiz/              # Nutrition quiz system
│   ├── products/          # Product recommendations
│   ├── about/             # About page
│   ├── contact/           # Contact information
│   ├── layout.tsx         # Root layout
│   └── page.tsx           # Landing page
├── components/            
│   ├── chat/             # Chat components
│   ├── layout/           # Layout elements
│   ├── ui/               # Reusable UI components
│   └── home/             # Landing page components
├── lib/                   # Utility functions
├── types/                # TypeScript definitions
├── services/            # External integrations
└── hooks/               # Custom React hooks
```

## Implementation Guidelines

### 1. Authentication Setup
- Implement Clerk authentication
- Set up protected routes
- Create middleware for auth checks
- Handle user sessions properly

### 2. AI Chat Implementation
- Integrate Anthropic AI SDK
- Build chat interface with real-time updates
- Implement conversation history
- Handle streaming responses
- Add proper error handling

### 3. UI/UX Requirements
- Implement responsive design
- Support dark/light mode
- Use consistent spacing and typography
- Follow accessibility guidelines
- Add loading states and transitions

### 4. Data Management
- Use TypeScript for type safety
- Implement proper form validation with Zod
- Handle API responses safely
- Manage state with React hooks

## Critical Considerations

### Do's
✅ Use TypeScript strictly
✅ Implement proper error handling
✅ Follow component-based architecture
✅ Add loading states
✅ Use environment variables
✅ Implement proper security measures

### Don'ts
❌ Mix client/server components incorrectly
❌ Expose sensitive data
❌ Skip error boundaries
❌ Ignore TypeScript types
❌ Use deprecated features
❌ Implement unsafe auth practices

## Configuration Requirements

### Required Files
1. `next.config.js`
```js
/** @type {import('next').NextConfig} */
const nextConfig = {
  // Add necessary configurations
}
```

2. `tailwind.config.ts`
- Include custom theme
- Configure plugins
- Set up animations

3. `.env.local` Template
```
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=
CLERK_SECRET_KEY=
ANTHROPIC_API_KEY=
```

## Development Process
1. Set up development environment
2. Install dependencies
3. Configure environment variables
4. Implement core features:
   - Authentication
   - Chat functionality
   - Quiz system
   - Product recommendations
5. Add UI components
6. Test thoroughly
7. Optimize performance

## Security Checklist
- [ ] Secure API routes
- [ ] Implement rate limiting
- [ ] Handle user data safely
- [ ] Set up proper CORS
- [ ] Validate all inputs
- [ ] Secure environment variables

## Performance Requirements
- Optimize image loading
- Implement code splitting
- Use proper caching
- Monitor bundle size
- Implement lazy loading

Remember to maintain clean code practices and follow the established project structure. This will ensure consistency and maintainability across the application.