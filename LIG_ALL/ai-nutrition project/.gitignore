# Dependencies
node_modules/
.pnp
.pnp.js
package-lock.json

# Testing
/coverage

# Next.js
.next/
out/

# Production
build/
dist/

# Misc
.DS_Store
*.pem
.env.local
.env.development.local
.env.test.local
.env.production.local

# Debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# IDE
.idea/
.vscode/
*.swp
*.swo

# Vercel
.vercel

# TypeScript
*.tsbuildinfo
next-env.d.ts

# ESLint
.eslintcache
*.eslintrc.json

# Next.js build output
/.next/
/frontend/.next/
