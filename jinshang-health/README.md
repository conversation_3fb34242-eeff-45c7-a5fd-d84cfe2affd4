# 晋商健康多端应用

## 项目简介
晋商健康多端应用是一个基于微信小程序和移动应用的综合性健康服务平台，致力于为晋商企业家群体提供全方位的健康管理与企业发展服务。项目旨在实现"两个健康"战略：促进非公有制经济健康发展和非公有制经济人士健康成长。

### 主要特点
- 多端统一：支持小程序端和APP端，提供一致的用户体验
- 国际化支持：内置i18n多语言配置
- 类型安全：采用TypeScript确保代码质量
- 模块化结构：清晰的项目分层，便于开发和维护

### 核心功能
1. **商业资源对接**：提供商机信息共享和晋商地域网络搜索功能，帮助晋商企业家快速找到特定地区的同行业资源
2. **企业健康发展**：提供企业财务健康评估、组织结构健康度、政策解读与指导等功能
3. **企业家健康成长**：提供身体健康监测、心理健康评估、高端医疗资源对接等功能
4. **个人中心**：提供用户信息管理、收藏管理、浏览历史等功能

## 技术框架

### 开发环境
- Node.js 16.x 或更高版本
- 微信开发者工具最新版
- yarn 或 npm 包管理器

### 项目结构
```
项目根目录
├── miniprogram/           # 小程序端
│   ├── pages/            # 页面文件
│   │   ├── index/        # 首页
│   │   ├── resource/     # 商业资源对接
│   │   ├── health/       # 两个健康
│   │   └── profile/      # 个人中心
│   ├── components/       # 组件文件
│   │   ├── common/       # 通用组件
│   │   └── business/     # 业务组件
│   ├── utils/            # 工具函数
│   ├── services/         # 服务层
│   ├── models/           # 数据模型
│   ├── constants/        # 常量定义
│   ├── assets/           # 静态资源
│   └── app.*             # 小程序入口文件
├── miniapp/              # APP端（规划中）
├── i18n/                 # 多语言配置
├── typings/              # 类型定义
├── project_docs/         # 项目文档
└── 配置文件
    ├── project.config.json       # 项目配置
    ├── project.miniapp.json      # 多端应用配置
    ├── project.private.config.json# 私有配置
    └── tsconfig.json            # TypeScript配置
```

## 项目架构图

```mermaid
graph LR
    A[晋商健康多端应用] --> B[基础配置]
    A --> C[多端程序]
    A --> D[项目资源]
    
    subgraph 基础配置
        direction TB
        B --> B1[project.config.json]
        B --> B2[project.miniapp.json]
        B --> B3[tsconfig.json]
        B --> B4[project.private.config.json]
    end
    
    subgraph 多端程序
        direction TB
        C --> M1[miniprogram - 小程序]
        C --> M2[miniapp - APP]
        
        M1 --> P1[pages - 页面]
        M1 --> P2[utils - 工具]
        M1 --> P3[components - 组件]
        M1 --> P4[services - 服务]
        M1 --> P5[app配置文件]
    end
    
    subgraph 项目资源
        direction TB
        D --> D1[i18n - 多语言配置]
        D --> D2[typings - 类型定义]
        D --> D3[project_docs - 项目文档]
    end

    classDef default fill:#2d3436,stroke:#fff,stroke-width:1px,color:#fff
    classDef config fill:#2980b9,stroke:#fff,stroke-width:1px,color:#fff
    classDef program fill:#8e44ad,stroke:#fff,stroke-width:1px,color:#fff
    classDef resource fill:#16a085,stroke:#fff,stroke-width:1px,color:#fff
    classDef platform fill:#c0392b,stroke:#fff,stroke-width:1px,color:#fff
    
    class A default
    class B,B1,B2,B3,B4 config
    class C,P1,P2,P3,P4,P5 program
    class D,D1,D2,D3 resource
    class M1,M2 platform
```

## 开发指南

### 环境准备
1. 克隆项目
```bash
git clone [项目地址]
```

2. 安装依赖
```bash
yarn install
```

3. 启动开发
```bash
# 小程序端
yarn dev:weapp

# APP端（规划中）
yarn dev:app
```

### 项目配置
- `project.config.json`: 小程序项目配置
- `project.miniapp.json`: 多端应用配置
- `tsconfig.json`: TypeScript编译配置
- `project.private.config.json`: 本地开发配置

### 开发规范
1. **代码规范**
   - 使用TypeScript进行开发
   - 遵循项目目录结构
   - 保持代码风格一致

2. **资源管理**
   - 组件按通用/业务分类
   - 工具函数放置于utils目录
   - 服务层处理业务逻辑

3. **多语言开发**
   - 文案统一在i18n目录管理
   - 支持中英文切换
   - 按端分别配置

## 部署发布

### 小程序端
1. 构建项目
```bash
yarn build:weapp
```
2. 使用微信开发者工具上传
3. 提交审核发布

### APP端（规划中）
- 待补充

## 项目进度

当前项目整体完成度约40%，各模块完成情况：

| 模块 | 完成度 | 备注 |
|------|--------|------|
| 项目基础架构 | 95% | 基础框架已完成，文件结构清晰 |
| 首页 | 70% | UI基本实现，部分交互逻辑未完成 |
| 商业资源 | 40% | 基础UI实现，核心功能待开发 |
| 两个健康 | 20% | 入口页面完成，子模块基本未开发 |
| 个人中心 | 30% | 页面UI完成，功能逻辑未实现 |

详细的项目路线图和任务规划请参阅 [项目路线图](./project_docs/PROJECT_ROADMAP.md)。

## 贡献指南
1. Fork 本仓库
2. 创建特性分支
3. 提交代码
4. 发起Pull Request

## 版本规划
- v1.0.0: 小程序端基础框架搭建
- v1.1.0: 商业资源对接和两个健康基础功能
- v1.2.0: 核心功能完善
- v2.0.0: APP端开发（规划中）

## 联系方式
- 项目负责人：[姓名]
- 邮箱：[邮箱地址]

## 版权信息
Copyright © 2024 晋商健康团队 (JinShang Health Team)

## 项目文档

详细的项目文档请参阅 [项目文档索引](./project_docs/INDEX.md)，包含以下内容：

- [项目路线图](./project_docs/PROJECT_ROADMAP.md) - 项目愿景、路线图和任务规划
- [开发指南](./project_docs/DEVELOPMENT_GUIDELINES.md) - 环境配置、代码规范和开发流程
- [UI设计指南](./project_docs/UI_DESIGN.md) - 设计原则、核心功能UI设计和组件规范
- [错误记录簿](./project_docs/ERROR_BOOK.md) - 常见问题和解决方案
