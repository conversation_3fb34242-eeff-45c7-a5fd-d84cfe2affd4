{"description": "项目配置文件", "miniprogramRoot": "miniprogram/", "compileType": "miniprogram", "setting": {"useCompilerPlugins": ["typescript"], "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}, "coverView": true, "postcss": true, "minified": true, "enhance": true, "showShadowRootInWxmlPanel": true, "packNpmRelationList": [], "ignoreUploadUnusedFiles": true, "compileHotReLoad": true, "skylineRenderEnable": true, "useMultiFrameRuntime": true, "useApiHook": true, "useApiHostProcess": true, "condition": true}, "simulatorType": "wechat", "simulatorPluginLibVersion": {"wxext14566970e7e9f62": "3.6.5-23"}, "condition": {}, "srcMiniprogramRoot": "miniprogram/", "editorSetting": {"tabIndent": "insertSpaces", "tabSize": 2}, "libVersion": "3.7.8", "packOptions": {"ignore": [], "include": []}, "appid": "wxe49a919e0eccb173", "projectname": "jinshang-health", "projectArchitecture": "multiPlatform"}