/**
 * 网络请求工具函数
 */

// 基础URL，实际开发中替换为真实API地址
const BASE_URL = 'https://api.example.com';

// 请求选项接口
interface RequestOptions {
  url: string;
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE';
  data?: any;
  header?: Record<string, string>;
}

// 响应接口
interface ResponseData<T = any> {
  code: number;
  message: string;
  data: T;
}

/**
 * 发送网络请求
 * @param options 请求选项
 */
export function request<T>(options: RequestOptions): Promise<T> {
  return new Promise((resolve, reject) => {
    wx.request({
      url: options.url.startsWith('http') ? options.url : `${BASE_URL}${options.url}`,
      method: options.method || 'GET',
      data: options.data,
      header: {
        'content-type': 'application/json',
        ...options.header
      },
      success: (res: any) => {
        const { statusCode, data } = res;
        
        if (statusCode >= 200 && statusCode < 300) {
          // 假设API返回格式为 { code, message, data }
          const responseData = data as ResponseData<T>;
          
          if (responseData.code === 0) {
            resolve(responseData.data);
          } else {
            reject(new Error(responseData.message || '请求失败'));
            wx.showToast({
              title: responseData.message || '请求失败',
              icon: 'none'
            });
          }
        } else {
          reject(new Error(`网络请求失败，状态码：${statusCode}`));
          wx.showToast({
            title: `网络请求失败，状态码：${statusCode}`,
            icon: 'none'
          });
        }
      },
      fail: (err) => {
        reject(err);
        wx.showToast({
          title: '网络连接失败',
          icon: 'none'
        });
      }
    });
  });
}

/**
 * GET请求
 * @param url 请求地址
 * @param data 请求参数
 * @param header 请求头
 */
export function get<T>(url: string, data?: any, header?: Record<string, string>): Promise<T> {
  return request<T>({
    url,
    method: 'GET',
    data,
    header
  });
}

/**
 * POST请求
 * @param url 请求地址
 * @param data 请求参数
 * @param header 请求头
 */
export function post<T>(url: string, data?: any, header?: Record<string, string>): Promise<T> {
  return request<T>({
    url,
    method: 'POST',
    data,
    header
  });
}

/**
 * PUT请求
 * @param url 请求地址
 * @param data 请求参数
 * @param header 请求头
 */
export function put<T>(url: string, data?: any, header?: Record<string, string>): Promise<T> {
  return request<T>({
    url,
    method: 'PUT',
    data,
    header
  });
}

/**
 * DELETE请求
 * @param url 请求地址
 * @param data 请求参数
 * @param header 请求头
 */
export function del<T>(url: string, data?: any, header?: Record<string, string>): Promise<T> {
  return request<T>({
    url,
    method: 'DELETE',
    data,
    header
  });
} 