/**index.wxss**/
page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: rgba(250,250,250,1);
}
.scrollarea {
  flex: 1;
  width: 100%;
  height: 100%;
  overflow-y: auto;
  background-color: #f5f7fa;
}

.container {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding-bottom: 40rpx;
}

.header-bg {
  position: absolute;
  top: 0;
  width: 100%;
  height: 400rpx;
  z-index: 0;
}

.header-image {
  width: 100%;
  height: 300rpx;
  margin-top: 50rpx;
  z-index: 1;
}

.header-text {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx 30rpx;
  box-sizing: border-box;
  z-index: 1;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
  text-align: center;
}

.subtitle {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 10rpx;
  text-align: center;
}

.button-container {
  margin-top: 30rpx;
  width: 100%;
  display: flex;
  justify-content: center;
}

.action-button {
  background-color: #1976d2;
  color: white;
  padding: 15rpx 50rpx;
  border-radius: 40rpx;
  font-size: 28rpx;
  font-weight: bold;
  text-align: center;
}

.swiper-container {
  width: 94%;
  height: 300rpx;
  margin: 20rpx auto;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.1);
}

.slide-image {
  width: 100%;
  height: 100%;
}

.notice-bar {
  width: 94%;
  display: flex;
  align-items: center;
  background-color: #fff;
  padding: 20rpx 30rpx;
  margin: 20rpx auto;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);
}

.notice-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 20rpx;
}

.notice-text {
  font-size: 26rpx;
  color: #666;
  flex: 1;
}

.two-health-container {
  width: 94%;
  display: flex;
  justify-content: space-between;
  margin: 20rpx auto;
}

.health-card {
  width: 48%;
  background-color: #fff;
  padding: 30rpx;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  align-items: center;
}

.card-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  text-align: center;
}

.card-icon {
  width: 100rpx;
  height: 100rpx;
  margin-bottom: 20rpx;
}

.card-desc {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 20rpx;
  text-align: center;
}

.more-button {
  font-size: 24rpx;
  color: #1976d2;
  padding: 10rpx 20rpx;
  border: 1rpx solid #1976d2;
  border-radius: 30rpx;
  text-align: center;
}

.section {
  width: 94%;
  background-color: #fff;
  padding: 30rpx;
  margin: 20rpx auto;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.1);
  box-sizing: border-box;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  display: block;
  text-align: center;
}

.section-subtitle {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 20rpx;
  display: block;
  text-align: center;
}

.tab-container {
  width: 100%;
}

.tab-header {
  display: flex;
  border-bottom: 1rpx solid #eee;
  margin-bottom: 20rpx;
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 15rpx 0;
  font-size: 28rpx;
  color: #666;
}

.tab-active {
  color: #1976d2;
  font-weight: bold;
  border-bottom: 4rpx solid #1976d2;
}

.service-list {
  padding: 10rpx 0;
}

.service-list-item {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.list-dot {
  width: 16rpx;
  height: 16rpx;
  background-color: #1976d2;
  border-radius: 50%;
  margin-right: 20rpx;
}

.service-text {
  font-size: 26rpx;
  color: #666;
}

.cases-container {
  width: 100%;
  white-space: nowrap;
  margin-top: 20rpx;
}

.case-item {
  display: inline-block;
  width: 500rpx;
  background-color: #f9f9f9;
  padding: 20rpx;
  margin-right: 20rpx;
  border-radius: 12rpx;
  border-left: 6rpx solid #1976d2;
  box-sizing: border-box;
}

.case-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
  display: block;
}

.case-desc {
  font-size: 24rpx;
  color: #666;
  display: block;
  white-space: normal;
}

.service-item {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.service-icon {
  width: 60rpx;
  height: 60rpx;
  margin-right: 20rpx;
}

.expert-container {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  margin-top: 20rpx;
}

.expert-item {
  width: 48%;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 30rpx;
}

.expert-avatar {
  width: 150rpx;
  height: 150rpx;
  border-radius: 50%;
  margin-bottom: 15rpx;
}

.expert-name {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 5rpx;
  text-align: center;
}

.expert-title {
  font-size: 24rpx;
  color: #666;
  text-align: center;
}