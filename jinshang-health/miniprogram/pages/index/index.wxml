<!--index.wxml-->
<scroll-view class="scrollarea" scroll-y type="list">
  <view class="container">
    <!-- 顶部区域 -->
    <image class="header-bg" src="/images/bg.png" mode="aspectFill"/>
    <image class="header-image" src="/images/head.png" mode="aspectFit"/>

    <view class="header-text">
      <text class="title">晋商健康服务平台</text>
      <text class="subtitle">促进非公有制经济健康发展</text>
      <text class="subtitle">助力非公有制经济人士健康成长</text>
    </view>
    
    <view class="button-container">
      <view class="action-button" catch:tap="toHealth">
        两个健康
      </view>
    </view>

    <!-- 轮播图 -->
    <swiper class="swiper-container" indicator-dots="true" autoplay="true" interval="3000" duration="500" circular="true">
      <swiper-item wx:for="{{banners}}" wx:key="id">
        <image class="slide-image" src="{{item.image}}" mode="aspectFill"/>
      </swiper-item>
    </swiper>

    <!-- 通知栏 -->
    <view class="notice-bar">
      <image class="notice-icon" src="/images/company_health.png" mode="aspectFit"/>
      <text class="notice-text">{{notice}}</text>
    </view>

    <!-- 两个健康核心概念区 -->
    <view class="two-health-container">
      <!-- 企业健康卡片 -->
      <view class="health-card" bindtap="toEnterpriseHealth">
        <text class="card-title">企业健康</text>
        <image class="card-icon" src="/images/company_health.png" mode="aspectFit"/>
        <text class="card-desc">促进非公有制经济健康发展</text>
        <view class="more-button">了解更多 ></view>
      </view>
      
      <!-- 企业家健康卡片 -->
      <view class="health-card" bindtap="toEntrepreneurHealth">
        <text class="card-title">企业家健康</text>
        <image class="card-icon" src="/images/person_health.png" mode="aspectFit"/>
        <text class="card-desc">促进非公有制经济人士健康成长</text>
        <view class="more-button">了解更多 ></view>
      </view>
    </view>

    <!-- 服务模块 -->
    <view class="section">
      <text class="section-title">服务模块</text>
      <view class="tab-container">
        <view class="tab-header">
          <view class="tab-item {{activeTab === 'enterprise' ? 'tab-active' : ''}}" bindtap="switchTab" data-tab="enterprise">企业健康</view>
          <view class="tab-item {{activeTab === 'entrepreneur' ? 'tab-active' : ''}}" bindtap="switchTab" data-tab="entrepreneur">企业家健康</view>
        </view>
        
        <!-- 企业健康服务列表 -->
        <view class="service-list" hidden="{{activeTab !== 'enterprise'}}">
          <view class="service-list-item" wx:for="{{enterpriseServices}}" wx:key="id">
            <view class="list-dot"></view>
            <text class="service-text">{{item.name}}</text>
          </view>
        </view>
        
        <!-- 企业家健康服务列表 -->
        <view class="service-list" hidden="{{activeTab !== 'entrepreneur'}}">
          <view class="service-list-item" wx:for="{{entrepreneurServices}}" wx:key="id">
            <view class="list-dot"></view>
            <text class="service-text">{{item.name}}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 成功案例 -->
    <view class="section">
      <text class="section-title">成功案例</text>
      <scroll-view class="cases-container" scroll-x="true">
        <view class="case-item" wx:for="{{cases}}" wx:key="id" bindtap="viewCase" data-id="{{item.id}}">
          <text class="case-title">{{item.title}}</text>
          <text class="case-desc">{{item.desc}}</text>
        </view>
      </scroll-view>
    </view>

    <!-- 商业资源对接 -->
    <view class="section">
      <text class="section-title">商业资源对接</text>
      <view class="service-item">
        <image class="service-icon" src="/images/Icon3.png" mode="aspectFit"/>
        <text class="service-text">连接优质商业资源，助力企业发展</text>
      </view>
      <view class="service-item">
        <image class="service-icon" src="/images/Icon4.png" mode="aspectFit"/>
        <text class="service-text">提供全方位商业支持和服务</text>
      </view>
      <view style="display: flex; justify-content: center; margin-top: 20rpx;">
        <view class="more-button" bindtap="toResource">进入资源中心 ></view>
      </view>
    </view>

    <!-- 专家团队 -->
    <view class="section">
      <text class="section-title">专家团队</text>
      <text class="section-subtitle">专业的健康顾问团队为您服务</text>
      <view class="expert-container">
        <view class="expert-item" wx:for="{{experts}}" wx:key="id" bindtap="viewExpert" data-id="{{item.id}}">
          <image class="expert-avatar" src="{{item.avatar}}"/>
          <text class="expert-name">{{item.name}}</text>
          <text class="expert-title">{{item.title}}</text>
        </view>
      </view>
    </view>
  </view>
</scroll-view>