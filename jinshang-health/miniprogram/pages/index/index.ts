// index.ts
// 获取应用实例
const app = getApp<IAppOption>()
const defaultAvatarUrl = 'https://mmbiz.qpic.cn/mmbiz/icTdbqWNOwNRna42FI242Lcia07jQodd2FJGIYQfG0LAJGFxM4FbnQP6yfMxBgJ0F3YRqJCJ1aPAK2dQagdusBZg/0'

Component({
  data: {
    banners: [
      {
        id: 1,
        image: '/images/bg.png',
        title: '促进非公有制经济健康发展'
      },
      {
        id: 2,
        image: '/images/bg.png',
        title: '助力非公有制经济人士健康成长'
      },
      {
        id: 3,
        image: '/images/bg.png',
        title: '晋商健康，两个健康服务平台'
      }
    ],
    notice: '晋商健康服务平台正式上线，提供企业健康和企业家健康全方位服务',
    activeTab: 'enterprise',
    enterpriseServices: [
      { id: 1, name: '企业健康评估' },
      { id: 2, name: '企业诊断与咨询' },
      { id: 3, name: '企业经营分析' },
      { id: 4, name: '发展战略规划' }
    ],
    entrepreneurServices: [
      { id: 1, name: '健康体检服务' },
      { id: 2, name: '压力管理' },
      { id: 3, name: '心理咨询' },
      { id: 4, name: '领导力提升' }
    ],
    cases: [
      { id: 1, title: '某制造企业降本增效案例', desc: '通过健康评估和管理，提升企业运营效率15%' },
      { id: 2, title: '某科技公司人才留存案例', desc: '通过企业家健康服务，降低管理层流失率40%' },
      { id: 3, title: '某贸易企业战略转型案例', desc: '基于健康评估，实现业务转型，利润提升35%' }
    ],
    experts: [
      { id: 1, avatar: '/images/heart.png', name: '张教授', title: '企业战略' },
      { id: 2, avatar: '/images/heart1.png', name: '李博士', title: '健康管理' },
      { id: 3, avatar: '/images/heart.png', name: '王顾问', title: '企业诊断' },
      { id: 4, avatar: '/images/heart1.png', name: '赵专家', title: '心理健康' }
    ],
    userInfo: {
      avatarUrl: defaultAvatarUrl,
      nickName: '',
    },
    hasUserInfo: false,
    canIUseGetUserProfile: wx.canIUse('getUserProfile'),
    canIUseNicknameComp: wx.canIUse('input.type.nickname'),
  },
  methods: {
    // 事件处理函数
    toHealth() {
      console.log('跳转到健康页面')
      wx.switchTab({
        url: '../health/health',
      })
    },
    
    // 切换服务标签页
    switchTab(e: any) {
      const tab = e.currentTarget.dataset.tab;
      this.setData({
        activeTab: tab
      });
    },
    
    // 查看企业健康详情
    toEnterpriseHealth() {
      wx.navigateTo({
        url: '../health/enterprise/enterprise'
      });
    },
    
    // 查看企业家健康详情
    toEntrepreneurHealth() {
      wx.navigateTo({
        url: '../health/entrepreneur/entrepreneur'
      });
    },
    
    // 查看案例详情
    viewCase(e: any) {
      const caseId = e.currentTarget.dataset.id;
      wx.navigateTo({
        url: `../resource/case_detail/case_detail?id=${caseId}`
      });
    },
    
    // 查看专家详情
    viewExpert(e: any) {
      const expertId = e.currentTarget.dataset.id;
      wx.navigateTo({
        url: `../resource/expert_detail/expert_detail?id=${expertId}`
      });
    },
    
    // 前往资源中心
    toResource() {
      wx.switchTab({
        url: '../resource/resource'
      });
    }
  },
})
