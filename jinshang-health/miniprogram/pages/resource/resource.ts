// 商业资源对接页面
Page({
  data: {
    // 热门商机数据
    opportunities: [
      {
        id: '1',
        title: '山西特产食品加工合作',
        description: '寻找山西特色食品原料供应商，建立长期合作关系',
        tags: ['食品加工', '黑龙江省哈尔滨市']
      },
      {
        id: '2',
        title: '新能源技术项目投资',
        description: '寻找对新能源领域有兴趣的投资伙伴，共同开发市场',
        tags: ['新能源', '山西省太原市']
      }
    ]
  },
  
  onLoad() {
    console.log('资源页面加载');
    // 初始化数据
    this.initData();
  },
  
  onShow() {
    // 页面显示时的逻辑
  },

  // 初始化数据
  initData() {
    // 这里可以添加从服务器获取数据的逻辑
    console.log('初始化资源页面数据');
  },

  // 搜索事件处理
  onSearch(e: any) {
    const searchValue = e.detail.value;
    console.log('搜索内容:', searchValue);
    // 这里可以添加搜索逻辑
    wx.showToast({
      title: '搜索功能开发中',
      icon: 'none'
    });
  },

  // 功能卡片点击事件
  onCardTap(e: any) {
    const type = e.currentTarget.dataset.type;
    console.log('点击了功能卡片:', type);
    
    switch(type) {
      case 'business':
        // 商机浏览
        wx.showToast({
          title: '商机浏览功能开发中',
          icon: 'none'
        });
        break;
      case 'network':
        // 跳转到晋商网络页面
        wx.showToast({
          title: '晋商网络功能开发中',
          icon: 'none'
        });
        break;
      case 'news':
        // 行业资讯
        wx.showToast({
          title: '行业资讯功能开发中',
          icon: 'none'
        });
        break;
      case 'policy':
        // 政策解读
        wx.showToast({
          title: '政策解读功能开发中',
          icon: 'none'
        });
        break;
    }
  },

  // 查看更多商机
  viewMoreOpportunities() {
    console.log('查看更多商机');
    wx.showToast({
      title: '更多商机功能开发中',
      icon: 'none'
    });
  },

  // 查看商机详情
  viewOpportunityDetail(e: any) {
    const id = e.currentTarget.dataset.id;
    console.log('查看商机详情:', id);
    
    wx.showToast({
      title: '商机详情功能开发中',
      icon: 'none'
    });
  }
}); 