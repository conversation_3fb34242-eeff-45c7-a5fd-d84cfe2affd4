.container {
  display: flex;
  flex-direction: column;
  padding: 0;
  background-color: #f5f7fa;
  min-height: 100vh;
  width: 100%;
  box-sizing: border-box;
}

/* 顶部搜索区域 */
.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 20rpx;
  background-color: #fff;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  width: 100%;
  box-sizing: border-box;
}

.search-box {
  display: flex;
  align-items: center;
  background-color: #f5f5f5;
  border-radius: 30rpx;
  padding: 10rpx 20rpx;
  flex: 1;
  margin-right: 20rpx;
}

.search-input {
  flex: 1;
  height: 60rpx;
  font-size: 28rpx;
  margin-left: 10rpx;
  color: #333;
}

.avatar {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  overflow: hidden;
}

.avatar-img {
  width: 100%;
  height: 100%;
}

/* 功能入口卡片区域 */
.function-cards {
  padding: 20rpx 15rpx;
  background-color: #fff;
  margin-top: 10rpx;
  width: 100%;
  box-sizing: border-box;
}

.card-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 15rpx;
}

.card-row:last-child {
  margin-bottom: 0;
}

.function-card {
  width: 49%;
  height: 160rpx;
  background-color: #f8f8f8;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.function-card.highlight {
  background-color: #e6f7ff;
  border: 1px solid #91d5ff;
}

.card-content {
  text-align: center;
}

.card-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
}

/* 热门商机区域 */
.hot-opportunities {
  margin-top: 10rpx;
  background-color: #fff;
  padding: 20rpx 15rpx;
  width: 100%;
  box-sizing: border-box;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15rpx;
}

.section-title {
  font-size: 34rpx;
  font-weight: bold;
  color: #333;
}

.view-more {
  display: flex;
  align-items: center;
}

.more-text {
  font-size: 28rpx;
  color: #999;
}

.more-icon {
  font-size: 28rpx;
  color: #999;
  margin-left: 6rpx;
}

.opportunity-list {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.opportunity-card {
  padding: 15rpx 0;
  border-bottom: 1rpx solid #eee;
  width: 100%;
}

.opportunity-card:last-child {
  border-bottom: none;
}

.opportunity-content {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.opportunity-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 10rpx;
}

.opportunity-desc {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 16rpx;
}

.opportunity-info {
  display: flex;
  flex-wrap: wrap;
  width: 100%;
}

.info-item {
  font-size: 24rpx;
  color: #999;
  background-color: #f5f5f5;
  padding: 4rpx 12rpx;
  border-radius: 4rpx;
  margin-right: 12rpx;
  margin-bottom: 8rpx;
} 