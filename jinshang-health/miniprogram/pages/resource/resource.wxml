<view class="container">
  <!-- 顶部搜索区域 -->
  <view class="header">
    <view class="search-box">
      <icon type="search" size="14" color="#999"></icon>
      <input class="search-input" placeholder="搜索商机/企业家" confirm-type="search" bindconfirm="onSearch" />
    </view>
    <view class="avatar">
      <image class="avatar-img" src="/images/20250227_210742_A_simple_minimalist_default_user.png"></image>
    </view>
  </view>
  
  <!-- 功能入口卡片区域 -->
  <view class="function-cards">
    <view class="card-row">
      <view class="function-card" bindtap="onCardTap" data-type="business">
        <view class="card-content">
          <text class="card-title">商机浏览</text>
        </view>
      </view>
      <view class="function-card highlight" bindtap="onCardTap" data-type="network">
        <view class="card-content">
          <text class="card-title">晋商网络</text>
        </view>
      </view>
    </view>
    
    <view class="card-row">
      <view class="function-card" bindtap="onCardTap" data-type="news">
        <view class="card-content">
          <text class="card-title">行业资讯</text>
        </view>
      </view>
      <view class="function-card" bindtap="onCardTap" data-type="policy">
        <view class="card-content">
          <text class="card-title">政策解读</text>
        </view>
      </view>
    </view>
  </view>
  
  <!-- 热门商机区域 -->
  <view class="hot-opportunities">
    <view class="section-header">
      <text class="section-title">热门商机</text>
      <view class="view-more" bindtap="viewMoreOpportunities">
        <text class="more-text">查看更多</text>
        <text class="more-icon">></text>
      </view>
    </view>
    
    <view class="opportunity-list">
      <view class="opportunity-card" wx:for="{{opportunities}}" wx:key="id" bindtap="viewOpportunityDetail" data-id="{{item.id}}">
        <view class="opportunity-content">
          <text class="opportunity-title">{{item.title}}</text>
          <text class="opportunity-desc">{{item.description}}</text>
          <view class="opportunity-info">
            <text class="info-item" wx:for="{{item.tags}}" wx:key="*this" wx:for-item="tag">{{tag}}</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</view> 