.container {
  padding-bottom: 40rpx;
}

.header {
  position: relative;
  height: 320rpx;
  width: 100%;
  overflow: hidden;
}

.header-bg {
  width: 100%;
  height: 100%;
}

.header-content {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: #ffffff;
  background-color: rgba(0, 0, 0, 0.3);
}

.title {
  font-size: 48rpx;
  font-weight: bold;
  margin-bottom: 16rpx;
}

.subtitle {
  font-size: 28rpx;
}

.section-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333333;
  padding: 30rpx 30rpx 20rpx;
  position: relative;
}

.section-title::after {
  content: '';
  position: absolute;
  left: 30rpx;
  bottom: 10rpx;
  width: 60rpx;
  height: 6rpx;
  background-color: #1AAD19;
  border-radius: 3rpx;
}

.assessment-cards {
  padding: 0 30rpx;
}

.assessment-card {
  display: flex;
  align-items: center;
  background-color: #fff;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.card-icon {
  width: 80rpx;
  height: 80rpx;
  margin-right: 20rpx;
}

.card-content {
  flex: 1;
}

.card-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 8rpx;
}

.card-desc {
  font-size: 24rpx;
  color: #666;
  display: block;
}

.arrow-icon {
  width: 32rpx;
  height: 32rpx;
}

.service-grid {
  display: flex;
  flex-wrap: wrap;
  padding: 0 15rpx;
}

.service-item {
  width: 25%;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx 0;
}

.service-icon {
  width: 80rpx;
  height: 80rpx;
  margin-bottom: 10rpx;
}

.service-name {
  font-size: 24rpx;
  color: #333;
  text-align: center;
}

.news-list {
  padding: 0 30rpx;
}

.news-item {
  display: flex;
  background-color: #fff;
  border-radius: 16rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.news-image {
  width: 180rpx;
  height: 120rpx;
  border-radius: 8rpx;
  margin-right: 20rpx;
}

.news-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.news-title {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.news-date {
  font-size: 24rpx;
  color: #999;
} 