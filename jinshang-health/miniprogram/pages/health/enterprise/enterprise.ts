// 企业健康页面
Page({
  data: {
    // 企业健康服务列表
    services: [
      {
        id: 1,
        name: '政策解读',
        icon: '/images/policy_icon.png'
      },
      {
        id: 2,
        name: '融资服务',
        icon: '/images/finance_service_icon.png'
      },
      {
        id: 3,
        name: '法律咨询',
        icon: '/images/legal_icon.png'
      },
      {
        id: 4,
        name: '人才招聘',
        icon: '/images/recruitment_icon.png'
      },
      {
        id: 5,
        name: '市场拓展',
        icon: '/images/market_icon.png'
      },
      {
        id: 6,
        name: '技术服务',
        icon: '/images/tech_icon.png'
      },
      {
        id: 7,
        name: '管理咨询',
        icon: '/images/management_icon.png'
      },
      {
        id: 8,
        name: '更多服务',
        icon: '/images/more_icon.png'
      }
    ],
    
    // 企业健康资讯列表
    news: [
      {
        id: 1,
        title: '如何提升企业数字化转型能力？专家为你解析',
        image: '/images/news1.png',
        date: '2023-10-15'
      },
      {
        id: 2,
        title: '山西省出台新政策：支持中小企业创新发展',
        image: '/images/news2.png',
        date: '2023-10-12'
      },
      {
        id: 3,
        title: '企业财务健康管理的五个关键指标分析',
        image: '/images/news3.png',
        date: '2023-10-08'
      }
    ]
  },
  
  onLoad() {
    console.log('企业健康页面加载');
    this._fetchEnterpriseHealthData();
  },
  
  onShow() {
    // 页面显示时的逻辑
  },
  
  // 导航到评估详情页
  navigateToAssessment(e: any) {
    const { type } = e.currentTarget.dataset;
    wx.navigateTo({
      url: `/pages/health/enterprise/assessment/assessment?type=${type}`
    });
  },
  
  // 导航到服务详情页
  navigateToService(e: any) {
    const { id } = e.currentTarget.dataset;
    wx.navigateTo({
      url: `/pages/health/enterprise/service/service_detail?id=${id}`
    });
  },
  
  // 导航到资讯详情页
  navigateToNewsDetail(e: any) {
    const { id } = e.currentTarget.dataset;
    wx.navigateTo({
      url: `/pages/health/enterprise/news/news_detail?id=${id}`
    });
  },
  
  // 获取企业健康数据
  _fetchEnterpriseHealthData() {
    // 这里可以添加API请求获取实际数据
    // wx.request({
    //   url: 'https://api.example.com/enterprise-health',
    //   method: 'GET',
    //   success: (res) => {
    //     this.setData({
    //       services: res.data.services,
    //       news: res.data.news
    //     });
    //   },
    //   fail: (err) => {
    //     console.error('获取企业健康数据失败', err);
    //   }
    // });
  }
}); 