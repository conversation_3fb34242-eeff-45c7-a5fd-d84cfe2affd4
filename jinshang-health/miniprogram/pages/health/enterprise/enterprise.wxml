<view class="container">
  <view class="header">
    <image class="header-bg" src="/images/enterprise_header_bg.png" mode="aspectFill"></image>
    <view class="header-content">
      <text class="title">企业健康</text>
      <text class="subtitle">为企业提供全方位的健康管理服务</text>
    </view>
  </view>

  <view class="section-title">企业健康评估</view>
  
  <view class="assessment-cards">
    <view class="assessment-card" bindtap="navigateToAssessment" data-type="finance">
      <image class="card-icon" src="/images/finance_icon.png" mode="aspectFit"></image>
      <view class="card-content">
        <text class="card-title">财务健康</text>
        <text class="card-desc">评估企业财务状况，提供优化建议</text>
      </view>
      <image class="arrow-icon" src="/images/arrow_right.png" mode="aspectFit"></image>
    </view>
    
    <view class="assessment-card" bindtap="navigateToAssessment" data-type="operation">
      <image class="card-icon" src="/images/operation_icon.png" mode="aspectFit"></image>
      <view class="card-content">
        <text class="card-title">运营健康</text>
        <text class="card-desc">评估企业运营效率，发现改进空间</text>
      </view>
      <image class="arrow-icon" src="/images/arrow_right.png" mode="aspectFit"></image>
    </view>
    
    <view class="assessment-card" bindtap="navigateToAssessment" data-type="innovation">
      <image class="card-icon" src="/images/innovation_icon.png" mode="aspectFit"></image>
      <view class="card-content">
        <text class="card-title">创新能力</text>
        <text class="card-desc">评估企业创新水平与研发实力</text>
      </view>
      <image class="arrow-icon" src="/images/arrow_right.png" mode="aspectFit"></image>
    </view>
  </view>

  <view class="section-title">企业健康服务</view>
  
  <view class="service-grid">
    <view class="service-item" wx:for="{{services}}" wx:key="id" bindtap="navigateToService" data-id="{{item.id}}">
      <image class="service-icon" src="{{item.icon}}" mode="aspectFit"></image>
      <text class="service-name">{{item.name}}</text>
    </view>
  </view>

  <view class="section-title">企业健康资讯</view>
  
  <view class="news-list">
    <view class="news-item" wx:for="{{news}}" wx:key="id" bindtap="navigateToNewsDetail" data-id="{{item.id}}">
      <image class="news-image" src="{{item.image}}" mode="aspectFill"></image>
      <view class="news-content">
        <text class="news-title">{{item.title}}</text>
        <text class="news-date">{{item.date}}</text>
      </view>
    </view>
  </view>
</view> 