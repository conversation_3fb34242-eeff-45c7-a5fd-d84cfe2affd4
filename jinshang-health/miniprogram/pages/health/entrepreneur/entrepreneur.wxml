<view class="container">
  <view class="header">
    <image class="header-bg" src="/images/entrepreneur_header_bg.png" mode="aspectFill"></image>
    <view class="header-content">
      <text class="title">企业家健康</text>
      <text class="subtitle">为企业家提供身心健康管理服务</text>
    </view>
  </view>

  <view class="profile-card" wx:if="{{hasUserInfo}}">
    <view class="profile-info">
      <image class="avatar" src="{{userInfo.avatar || '/images/default_avatar.png'}}" mode="aspectFill"></image>
      <view class="user-info">
        <text class="username">{{userInfo.name || '未登录'}}</text>
        <text class="user-desc">{{userInfo.company || '暂无企业信息'}}</text>
      </view>
    </view>
    <view class="health-score">
      <text class="score-value">{{healthScore || '--'}}</text>
      <text class="score-label">健康指数</text>
    </view>
  </view>
  
  <view class="login-notice" wx:else bindtap="navigateToLogin">
    <text>登录后查看您的健康数据</text>
    <text class="login-btn">去登录</text>
  </view>

  <view class="section-title">健康管理</view>
  
  <view class="health-cards">
    <view class="health-card" bindtap="navigateToHealthModule" data-type="physical">
      <image class="card-icon" src="/images/physical_icon.png" mode="aspectFit"></image>
      <view class="card-content">
        <text class="card-title">身体健康</text>
        <text class="card-desc">体检报告、健康档案管理</text>
      </view>
      <image class="arrow-icon" src="/images/arrow_right.png" mode="aspectFit"></image>
    </view>
    
    <view class="health-card" bindtap="navigateToHealthModule" data-type="mental">
      <image class="card-icon" src="/images/mental_icon.png" mode="aspectFit"></image>
      <view class="card-content">
        <text class="card-title">心理健康</text>
        <text class="card-desc">情绪管理、压力测评</text>
      </view>
      <image class="arrow-icon" src="/images/arrow_right.png" mode="aspectFit"></image>
    </view>
    
    <view class="health-card" bindtap="navigateToHealthModule" data-type="lifestyle">
      <image class="card-icon" src="/images/lifestyle_icon.png" mode="aspectFit"></image>
      <view class="card-content">
        <text class="card-title">生活方式</text>
        <text class="card-desc">饮食、运动、睡眠建议</text>
      </view>
      <image class="arrow-icon" src="/images/arrow_right.png" mode="aspectFit"></image>
    </view>
  </view>

  <view class="section-title">健康服务</view>
  
  <view class="service-grid">
    <view class="service-item" wx:for="{{services}}" wx:key="id" bindtap="navigateToService" data-id="{{item.id}}">
      <image class="service-icon" src="{{item.icon}}" mode="aspectFit"></image>
      <text class="service-name">{{item.name}}</text>
    </view>
  </view>

  <view class="section-title">个性化健康计划</view>
  
  <view class="plan-card" bindtap="navigateToHealthPlan">
    <view class="plan-header">
      <text class="plan-title">专属健康计划</text>
      <text class="plan-status">{{healthPlan.status || '未开始'}}</text>
    </view>
    <view class="plan-progress">
      <progress percent="{{healthPlan.progress || 0}}" stroke-width="4" activeColor="#1AAD19" backgroundColor="#EAEAEA"></progress>
      <text class="progress-text">已完成 {{healthPlan.progress || 0}}%</text>
    </view>
    <view class="plan-goal">
      <text class="goal-label">本周目标：</text>
      <text class="goal-content">{{healthPlan.weeklyGoal || '暂无计划'}}</text>
    </view>
  </view>
  
  <view class="section-title">健康资讯</view>
  
  <view class="news-list">
    <view class="news-item" wx:for="{{news}}" wx:key="id" bindtap="navigateToNewsDetail" data-id="{{item.id}}">
      <image class="news-image" src="{{item.image}}" mode="aspectFill"></image>
      <view class="news-content">
        <text class="news-title">{{item.title}}</text>
        <text class="news-date">{{item.date}}</text>
      </view>
    </view>
  </view>
</view> 