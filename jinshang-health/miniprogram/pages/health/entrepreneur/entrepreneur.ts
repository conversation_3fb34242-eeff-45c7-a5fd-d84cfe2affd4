// 企业家健康页面
Page({
  data: {
    hasUserInfo: false,
    userInfo: {
      name: '',
      avatar: '',
      company: ''
    },
    healthScore: 85,
    
    // 健康服务列表
    services: [
      {
        id: 1,
        name: '健康体检',
        icon: '/images/checkup_icon.png'
      },
      {
        id: 2,
        name: '心理咨询',
        icon: '/images/psychology_icon.png'
      },
      {
        id: 3,
        name: '健康讲座',
        icon: '/images/lecture_icon.png'
      },
      {
        id: 4,
        name: '运动指导',
        icon: '/images/sport_icon.png'
      },
      {
        id: 5,
        name: '营养配餐',
        icon: '/images/nutrition_icon.png'
      },
      {
        id: 6,
        name: '医疗预约',
        icon: '/images/medical_icon.png'
      },
      {
        id: 7,
        name: '健康档案',
        icon: '/images/record_icon.png'
      },
      {
        id: 8,
        name: '更多服务',
        icon: '/images/more_services_icon.png'
      }
    ],
    
    // 健康计划
    healthPlan: {
      status: '进行中',
      progress: 45,
      weeklyGoal: '每天步行8000步，控制碳水摄入，每周进行3次30分钟有氧运动'
    },
    
    // 健康资讯列表
    news: [
      {
        id: 1,
        title: '企业家的心理健康：如何应对高压力工作环境',
        image: '/images/news4.png',
        date: '2023-10-18'
      },
      {
        id: 2,
        title: '对话专家：科学作息对提升工作效率的重要性',
        image: '/images/news5.png',
        date: '2023-10-16'
      },
      {
        id: 3,
        title: '企业家常见健康问题分析与预防措施',
        image: '/images/news6.png',
        date: '2023-10-10'
      }
    ]
  },
  
  onLoad() {
    console.log('企业家健康页面加载');
    this._checkLoginStatus();
    this._fetchEntrepreneurHealthData();
  },
  
  onShow() {
    // 页面显示时的逻辑
  },
  
  // 检查登录状态
  _checkLoginStatus() {
    // 这里模拟检查登录状态的逻辑
    const hasUserInfo = wx.getStorageSync('userInfo') ? true : false;
    if (hasUserInfo) {
      this.setData({
        hasUserInfo: true,
        userInfo: wx.getStorageSync('userInfo')
      });
    }
  },
  
  // 导航到登录页
  navigateToLogin() {
    wx.navigateTo({
      url: '/pages/profile/login/login'
    });
  },
  
  // 导航到健康模块详情页
  navigateToHealthModule(e: any) {
    const { type } = e.currentTarget.dataset;
    wx.navigateTo({
      url: `/pages/health/entrepreneur/module/module?type=${type}`
    });
  },
  
  // 导航到服务详情页
  navigateToService(e: any) {
    const { id } = e.currentTarget.dataset;
    wx.navigateTo({
      url: `/pages/health/entrepreneur/service/service_detail?id=${id}`
    });
  },
  
  // 导航到健康计划详情页
  navigateToHealthPlan() {
    wx.navigateTo({
      url: '/pages/health/entrepreneur/plan/plan'
    });
  },
  
  // 导航到资讯详情页
  navigateToNewsDetail(e: any) {
    const { id } = e.currentTarget.dataset;
    wx.navigateTo({
      url: `/pages/health/entrepreneur/news/news_detail?id=${id}`
    });
  },
  
  // 获取企业家健康数据
  _fetchEntrepreneurHealthData() {
    // 这里可以添加API请求获取实际数据
    // wx.request({
    //   url: 'https://api.example.com/entrepreneur-health',
    //   method: 'GET',
    //   success: (res) => {
    //     this.setData({
    //       healthScore: res.data.healthScore,
    //       services: res.data.services,
    //       healthPlan: res.data.healthPlan,
    //       news: res.data.news
    //     });
    //   },
    //   fail: (err) => {
    //     console.error('获取企业家健康数据失败', err);
    //   }
    // });
  }
}); 