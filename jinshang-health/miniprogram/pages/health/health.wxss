.container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx;
}

.header {
  width: 100%;
  text-align: center;
  margin-bottom: 30rpx;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.modules {
  width: 100%;
  display: flex;
  justify-content: space-around;
  margin: 40rpx 0;
}

.module-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 300rpx;
  height: 300rpx;
  background-color: #f8f8f8;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.module-icon {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 20rpx;
}

.module-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.description {
  width: 90%;
  background-color: #f5f5f5;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-top: 40rpx;
}

.desc-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 20rpx;
}

.desc-content {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
} 