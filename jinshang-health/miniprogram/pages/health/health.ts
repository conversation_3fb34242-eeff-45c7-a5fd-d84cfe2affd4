// 两个健康页面
Page({
  data: {
    modules: [
      { id: 1, name: '企业健康', icon: '/images/company_health.png', path: '/pages/health/enterprise/enterprise' },
      { id: 2, name: '企业家健康', icon: '/images/person_health.png', path: '/pages/health/entrepreneur/entrepreneur' }
    ]
  },
  
  onLoad() {
    console.log('两个健康页面加载');
  },
  
  onShow() {
    // 页面显示时的逻辑
  },
  
  navigateToModule(e: any) {
    const { path } = e.currentTarget.dataset;
    wx.navigateTo({
      url: path
    });
  }
}); 