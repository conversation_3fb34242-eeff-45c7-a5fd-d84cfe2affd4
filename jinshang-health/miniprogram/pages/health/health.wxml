<view class="container">
  <view class="header">
    <text class="title">两个健康</text>
  </view>
  
  <view class="modules">
    <view class="module-item" wx:for="{{modules}}" wx:key="id" bindtap="navigateToModule" data-path="{{item.path}}">
      <image class="module-icon" src="{{item.icon}}" mode="aspectFit"></image>
      <text class="module-name">{{item.name}}</text>
    </view>
  </view>
  
  <view class="description">
    <text class="desc-title">两个健康介绍</text>
    <text class="desc-content">两个健康是指企业健康和企业家健康，是促进非公有制经济健康发展和非公有制经济人士健康成长的重要举措。</text>
  </view>
</view> 