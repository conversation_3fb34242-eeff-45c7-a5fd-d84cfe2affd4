.container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.user-info {
  width: 100%;
  display: flex;
  align-items: center;
  padding: 40rpx 30rpx;
  background-color: #1AAD19;
  color: #fff;
}

.avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  background-color: #fff;
  margin-right: 30rpx;
}

.user-detail {
  display: flex;
  flex-direction: column;
}

.nickname {
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.login-btn {
  font-size: 24rpx;
  padding: 6rpx 20rpx;
  background-color: rgba(255, 255, 255, 0.2);
  color: #fff;
  border-radius: 30rpx;
  border: 1px solid #fff;
  width: auto;
  margin: 0;
  line-height: 1.6;
}

.menu-list {
  width: 100%;
  background-color: #fff;
  margin-top: 20rpx;
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #eee;
}

.menu-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 20rpx;
}

.menu-name {
  flex: 1;
  font-size: 30rpx;
  color: #333;
}

.arrow-icon {
  width: 30rpx;
  height: 30rpx;
}

.version-info {
  margin-top: 60rpx;
  font-size: 24rpx;
  color: #999;
} 