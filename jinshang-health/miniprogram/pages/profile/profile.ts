// 个人中心页面
Page({
  data: {
    userInfo: {
      avatarUrl: '',
      nickName: '未登录'
    },
    hasUserInfo: false,
    menuItems: [
      { id: 1, name: '我的健康档案', icon: '/images/health_record.png', path: '/pages/profile/health_record/health_record' },
      { id: 2, name: '我的企业', icon: '/images/my_enterprise.png', path: '/pages/profile/enterprise/enterprise' },
      { id: 3, name: '设置', icon: '/images/settings.png', path: '/pages/profile/settings/settings' }
    ]
  },
  
  onLoad() {
    console.log('个人中心页面加载');
  },
  
  onShow() {
    // 页面显示时的逻辑
  },
  
  getUserProfile() {
    wx.getUserProfile({
      desc: '用于完善用户资料',
      success: (res) => {
        this.setData({
          userInfo: res.userInfo,
          hasUserInfo: true
        });
      }
    });
  },
  
  navigateToMenuItem(e: any) {
    const { path } = e.currentTarget.dataset;
    wx.navigateTo({
      url: path
    });
  }
}); 