<view class="container">
  <view class="user-info">
    <image class="avatar" src="{{userInfo.avatarUrl || '/images/default_avatar.png'}}"></image>
    <view class="user-detail">
      <text class="nickname">{{userInfo.nickName}}</text>
      <button wx:if="{{!hasUserInfo}}" class="login-btn" bindtap="getUserProfile">点击登录</button>
    </view>
  </view>
  
  <view class="menu-list">
    <view class="menu-item" wx:for="{{menuItems}}" wx:key="id" bindtap="navigateToMenuItem" data-path="{{item.path}}">
      <image class="menu-icon" src="{{item.icon}}"></image>
      <text class="menu-name">{{item.name}}</text>
      <image class="arrow-icon" src="/images/arrow_right.png"></image>
    </view>
  </view>
  
  <view class="version-info">
    <text>晋商健康 v1.0.0</text>
  </view>
</view> 