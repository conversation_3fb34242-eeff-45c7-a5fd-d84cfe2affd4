{"pages": ["pages/index/index", "pages/resource/resource", "pages/health/health", "pages/health/enterprise/enterprise", "pages/health/entrepreneur/entrepreneur", "pages/profile/profile", "pages/logs/logs"], "window": {"navigationBarTextStyle": "black", "navigationBarTitleText": "晋商健康", "navigationBarBackgroundColor": "#ffffff"}, "tabBar": {"color": "#8a8a8a", "selectedColor": "#1AAD19", "backgroundColor": "#ffffff", "borderStyle": "black", "list": [{"pagePath": "pages/index/index", "text": "首页", "iconPath": "images/house.png", "selectedIconPath": "images/house1.png"}, {"pagePath": "pages/resource/resource", "text": "商业资源", "iconPath": "images/business_icon0.png", "selectedIconPath": "images/business_icon1.png"}, {"pagePath": "pages/health/health", "text": "两个健康", "iconPath": "images/health_icon_0.png", "selectedIconPath": "images/health_icon_1.png"}, {"pagePath": "pages/profile/profile", "text": "个人中心", "iconPath": "images/Icon5.png", "selectedIconPath": "images/Icon6.png"}], "position": "bottom"}, "style": "v2", "componentFramework": "glass-easel", "lazyCodeLoading": "requiredComponents", "miniApp": {"useAuthorizePage": true}}