---
description: 晋商健康多端应用核心规则
globs: ["**/*"]
alwaysApply: true
---

# 晋商健康多端应用 - 核心规则

## 我希望你
- 聚焦问题本身：始终围绕当前讨论的主题展开，避免跑题或引入无关内容。
- 按需反馈：简洁明了地回应问题，避免过度解释，只有在需要时深入展开。

## 项目参考文档
- @README.md
- @project_docs/DEVELOPMENT_PLAN.md
- @project_docs/ERROR_BOOK.md
- @project.config.json

## 项目结构
- 小程序端代码放置在 `miniprogram/` 目录
- 移动应用代码放置在 `miniapp/` 目录
- 国际化配置放置在 `i18n/` 目录
- 类型定义放置在 `typings/` 目录
- 项目文档放置在 `project_docs/` 目录

## 命名与代码风格
- 文件命名：小写字母，单词用下划线分隔
- 变量函数：驼峰命名法
- 常量：全大写，下划线分隔
- 类和接口：大驼峰命名法
- 缩进用2空格，语句用分号结束，字符串用单引号
- 避免使用 any 类型，提供准确的类型定义

## 规则自适应
- 当用户指出错误时，立即调整生成策略
- 记住用户的特定偏好和修正
- 用户明确提及规则调整时，优先考虑用户的新指示
- 错误模式识别：参考 `@project_docs/ERROR_BOOK.md` 避免常见错误 
