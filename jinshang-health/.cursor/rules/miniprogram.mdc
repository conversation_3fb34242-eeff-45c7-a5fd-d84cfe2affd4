---
description: 晋商健康小程序开发规则
globs: ["miniprogram/**/*"]
alwaysApply: false
---

# 晋商健康小程序开发规则

## 目录结构
- `pages/`: 页面文件
- `utils/`: 工具函数
- `images/`: 图片资源
- `icons/`: 图标资源
- `components/`: 组件（规划中）

## 页面开发规范
- 页面文件应包含 `.wxml`, `.wxss`, `.ts`, `.json` 四个文件
- 页面逻辑使用 TypeScript 编写，确保类型安全
- 页面样式使用 WXSS，避免内联样式
- 页面配置使用 JSON，设置导航栏、背景色等

## WXML 最佳实践
- 使用语义化标签，提高可读性
- 避免过深的标签嵌套，最好不超过5层
- 使用 `wx:for` 和 `wx:if` 时必须提供 `wx:key`
- 事件绑定使用驼峰命名法，如 `bindtap="onTapButton"`

## WXSS 样式规范
- 使用类选择器，避免标签选择器和ID选择器
- 类名使用 BEM 命名法（Block-Element-Modifier）
- 颜色值使用变量或统一的色彩系统
- 尺寸单位优先使用 rpx，适配不同屏幕

## 组件开发指南
- 组件应放置在 `components/` 目录下
- 组件应遵循单一职责原则
- 组件属性和事件应在 properties 和 methods 中明确定义
- 组件样式应使用 `styleIsolation` 进行隔离

## 生命周期使用指南
- `onLoad`: 页面加载时执行，获取路由参数
- `onShow`: 页面显示时执行，刷新数据
- `onHide`: 页面隐藏时执行，暂停操作
- `onUnload`: 页面卸载时执行，清理资源
- `onPullDownRefresh`: 下拉刷新时执行
- `onReachBottom`: 上拉加载时执行

## 数据管理
- 页面数据应在 data 中初始化
- 使用 setData 更新数据，避免直接修改 this.data
- 复杂数据处理应在 setData 前完成
- 避免频繁调用 setData，可合并多次更新
 