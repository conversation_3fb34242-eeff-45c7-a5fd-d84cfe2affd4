# IDE and editor files
.idea/
.vscode/
*.swp
*.swo
.editorconfig

# 系统文件
.DS_Store
.AppleDouble
.LSOverride
Icon
._*
.Spotlight-V100
.Trashes
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/

# 构建和依赖目录
node_modules/
miniprogram/node_modules/
miniapp/node_modules/
dist/
miniprogram_npm/
.miniprogram/
coverage/
.cache/

# 本地开发文件
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
*.log
logs/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# 临时文件
tmp/
temp/
*.tmp
*.temp
*.bak
*.backup
*~

# 私钥文件
private.*
*.key
*.pem

# 依赖相关
package-lock.json
yarn.lock
miniprogram/package-lock.json
miniapp/package-lock.json

# TypeScript编译
**/*.js.map


# 编译后的JavaScript文件（如果使用TypeScript）
**/miniprogram/**/*.js
!**/miniprogram/app.js

# 不要忽略项目文档
!project_docs/ 