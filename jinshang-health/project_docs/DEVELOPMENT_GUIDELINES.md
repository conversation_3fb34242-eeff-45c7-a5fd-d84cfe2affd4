# 晋商健康多端应用 - 开发指南

## 目录
- [开发环境配置](#开发环境配置)
- [项目结构](#项目结构)
- [开发工作流](#开发工作流)
- [代码规范](#代码规范)
- [组件开发规范](#组件开发规范)
- [页面开发规范](#页面开发规范)
- [网络请求规范](#网络请求规范)
- [状态管理](#状态管理)
- [性能优化](#性能优化)
- [安全规范](#安全规范)
- [测试规范](#测试规范)
- [常见问题](#常见问题)

## 开发环境配置

### 环境要求
- Node.js v16.0.0 或更高版本
- npm v7.0.0 或更高版本
- 微信开发者工具最新版本

### 初始环境配置

1. **安装依赖**
   ```bash
   npm install
   ```

2. **配置微信开发者工具**
   - 打开微信开发者工具
   - 导入项目（项目路径为本仓库根目录）
   - 在"详情"面板中，确保以下选项已启用：
     - 使用npm模块
     - 增强编译
     - ES6转ES5
     - 使用TypeScript

## 项目结构

```
jinshang-health/
├── miniprogram/         # 小程序代码
│   ├── components/      # 公共组件
│   │   ├── common/      # 通用组件
│   │   └── business/    # 业务组件
│   ├── pages/           # 页面
│   │   ├── index/       # 首页
│   │   ├── resource/    # 商业资源对接
│   │   ├── health/      # 两个健康
│   │   │   ├── enterprise/  # 企业健康
│   │   │   └── entrepreneur/# 企业家健康
│   │   └── profile/     # 个人中心
│   ├── utils/           # 工具函数
│   │   ├── api.ts       # API接口
│   │   ├── request.ts   # 网络请求
│   │   └── util.ts      # 通用工具
│   ├── services/        # 服务层
│   ├── models/          # 数据模型
│   ├── constants/       # 常量定义
│   ├── assets/          # 静态资源
│   ├── styles/          # 全局样式
│   └── app.*            # 小程序入口文件
├── miniapp/             # 移动应用代码（未来扩展）
├── i18n/                # 国际化配置
├── typings/             # TypeScript类型定义
├── project_docs/        # 项目文档
├── project.config.json  # 小程序项目配置
└── project.miniapp.json # 多端项目配置
```

## 开发工作流

### TypeScript编译
```bash
npm run compile
```

### 开发流程
1. 从主分支创建功能分支
2. 在功能分支上进行开发
3. 提交代码前进行代码检查
4. 创建Pull Request
5. 代码审核通过后合并到主分支

## 代码规范

### 命名规范

#### 文件命名
- 页面文件夹：使用小写字母，单词用下划线分隔，如 `user_profile`
- 组件文件夹：使用小写字母，单词用下划线分隔，如 `health_card`
- 工具类文件：使用小写字母，单词用下划线分隔，如 `date_util.ts`

#### 变量命名
- 变量和函数：使用驼峰命名法，如 `getUserInfo`
- 常量：使用全大写，单词用下划线分隔，如 `API_BASE_URL`
- 类和接口：使用大驼峰命名法，如 `UserService`
- 私有属性：使用下划线前缀，如 `_privateVar`

### 代码风格
- 使用TypeScript进行开发
- 缩进使用2个空格
- 语句结尾使用分号
- 字符串使用单引号
- 每个文件末尾保留一个空行
- 避免使用 `any` 类型，提供准确的类型定义

## 组件开发规范

### 组件结构
每个组件应包含以下文件：
- `component_name.ts`：组件逻辑
- `component_name.wxml`：组件模板
- `component_name.wxss`：组件样式
- `component_name.json`：组件配置

### 组件属性
- 组件属性应在 `properties` 中定义，并提供类型和默认值
- 使用 `observers` 监听属性变化

### 组件方法
- 内部方法使用下划线前缀，如 `_handleTap`
- 对外暴露的方法不使用下划线前缀，如 `updateData`

### 组件示例
```typescript
Component({
  properties: {
    title: {
      type: String,
      value: ''
    },
    items: {
      type: Array,
      value: []
    }
  },
  
  data: {
    activeIndex: 0
  },
  
  observers: {
    'items': function(items) {
      if (items.length > 0) {
        this._initializeItems();
      }
    }
  },
  
  methods: {
    _initializeItems() {
      // 内部初始化方法
    },
    
    handleItemTap(e) {
      const index = e.currentTarget.dataset.index;
      this.setData({
        activeIndex: index
      });
      this.triggerEvent('itemchange', { index, item: this.data.items[index] });
    },
    
    updateItems(newItems) {
      // 对外暴露的更新方法
      this.setData({
        items: newItems
      });
    }
  }
});
```

## 页面开发规范

### 页面结构
每个页面应包含以下文件：
- `page_name.ts`：页面逻辑
- `page_name.wxml`：页面模板
- `page_name.wxss`：页面样式
- `page_name.json`：页面配置

### 页面生命周期
- 在 `onLoad` 中进行初始化操作
- 在 `onShow` 中刷新页面数据
- 在 `onUnload` 中清理资源

### 页面数据加载
- 使用 `wx.showLoading` 和 `wx.hideLoading` 显示加载状态
- 使用 `try/catch` 处理异常情况
- 使用 `wx.showToast` 显示错误信息

### 页面示例
```typescript
Page({
  data: {
    userInfo: null,
    loading: true,
    error: ''
  },
  
  onLoad() {
    this._initializeData();
  },
  
  onShow() {
    this._refreshData();
  },
  
  onUnload() {
    // 清理资源
  },
  
  async _initializeData() {
    try {
      wx.showLoading({ title: '加载中' });
      const userInfo = await getUserInfo();
      this.setData({
        userInfo,
        loading: false
      });
    } catch (error) {
      this.setData({
        error: '获取用户信息失败',
        loading: false
      });
      wx.showToast({
        title: '获取用户信息失败',
        icon: 'none'
      });
    } finally {
      wx.hideLoading();
    }
  },
  
  async _refreshData() {
    // 刷新数据逻辑
  }
});
```

## 网络请求规范

- 所有API请求应通过统一的请求函数进行
- API地址应在常量文件中定义
- 请求应处理错误情况和超时
- 使用Promise或async/await处理异步操作

### 请求示例
```typescript
// constants/api.ts
export const API = {
  USER: {
    GET_INFO: '/user/info',
    UPDATE: '/user/update'
  }
};

// utils/request.ts
export const request = (url, method, data) => {
  return new Promise((resolve, reject) => {
    wx.request({
      url: BASE_URL + url,
      method,
      data,
      timeout: 10000,
      success: (res) => {
        if (res.statusCode === 200) {
          resolve(res.data);
        } else {
          reject(new Error(`请求失败: ${res.statusCode}`));
        }
      },
      fail: (err) => {
        reject(err);
      }
    });
  });
};

// 使用示例
import { API } from '../constants/api';
import { request } from '../utils/request';

const getUserInfo = async () => {
  try {
    return await request(API.USER.GET_INFO, 'GET');
  } catch (error) {
    console.error('获取用户信息失败', error);
    throw error;
  }
};
```

## 状态管理

- 使用全局状态管理简单数据（如用户信息）
- 复杂状态考虑使用发布订阅模式

### 全局状态示例
```typescript
// app.ts
App({
  globalData: {
    userInfo: null,
    systemInfo: null
  },
  
  onLaunch() {
    this._initSystemInfo();
  },
  
  _initSystemInfo() {
    const systemInfo = wx.getSystemInfoSync();
    this.globalData.systemInfo = systemInfo;
  },
  
  setUserInfo(userInfo) {
    this.globalData.userInfo = userInfo;
  }
});

// 在页面中使用
const app = getApp();
const userInfo = app.globalData.userInfo;
```

## 性能优化

- 避免频繁的setData操作
- 合理使用分页加载
- 图片资源进行压缩
- 使用 `wx:if` 和 `wx:for` 时提供合理的条件和key

### setData优化示例
```typescript
// 不推荐
this.setData({ a: 1 });
this.setData({ b: 2 });
this.setData({ c: 3 });

// 推荐
this.setData({
  a: 1,
  b: 2,
  c: 3
});
```

## 安全规范

- 敏感数据不要明文存储
- 使用HTTPS进行网络通信
- 用户输入数据进行验证和过滤

## 测试规范

- 组件和页面应进行功能测试
- 关键功能应有单元测试
- 上线前进行完整的回归测试

## 常见问题

### 1. npm依赖安装失败

尝试清除npm缓存后重新安装：
```bash
npm cache clean --force
npm install
```

### 2. 微信开发者工具无法识别npm包

确保在微信开发者工具中启用了"使用npm模块"选项，并点击工具栏中的"构建npm"按钮。

### 3. TypeScript编译错误

检查tsconfig.json配置是否正确，确保类型定义文件路径正确。

### 4. 页面无法正常跳转

- 检查app.json中是否已注册目标页面
- 检查页面路径是否正确（注意大小写）
- 检查是否超过了页面栈限制（最多10层）

### 5. 自定义组件无法显示

- 检查页面的json文件中是否正确引入了组件
- 检查组件路径是否正确
- 检查组件是否正确注册（Component函数调用）

### 6. setData性能问题

- 合并setData调用，一次更新多个数据
- 避免直接更新整个数组或对象，只更新变化的部分
- 使用节流或防抖技术限制setData调用频率

### 7. 小程序请求域名未配置

- 在微信公众平台后台添加对应域名到request合法域名列表
- 开发环境可临时关闭域名校验（仅用于开发） 