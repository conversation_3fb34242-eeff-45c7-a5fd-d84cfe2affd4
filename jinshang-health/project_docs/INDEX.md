# 晋商健康项目文档索引

本文档提供晋商健康多端应用项目所有文档的导航和索引，帮助团队成员快速找到所需信息。

## 核心文档

| 文档名称 | 描述 | 适用场景 |
|---------|------|---------|
| [README.md](../README.md) | 项目总览，包含项目简介、技术框架、项目结构和开发指南 | 初次了解项目、项目概览 |
| [PROJECT_ROADMAP.md](./PROJECT_ROADMAP.md) | 项目路线图，包含项目愿景、当前完成度、里程碑和任务规划 | 了解项目进度、规划开发任务 |
| [DEVELOPMENT_GUIDELINES.md](./DEVELOPMENT_GUIDELINES.md) | 开发指南，包含环境配置、代码规范和开发流程 | 配置开发环境、遵循开发规范 |
| [UI_DESIGN.md](./UI_DESIGN.md) | UI设计指南，包含设计原则、核心功能UI设计和组件规范 | 设计和实现用户界面 |
| [ERROR_BOOK.md](./ERROR_BOOK.md) | 错误记录簿，记录常见问题和解决方案 | 解决开发中遇到的问题 |

## 文档内容概览

### [README.md](../README.md)
项目的主要入口文档，提供项目的整体概述和基本信息。
- 项目简介和主要特点
- 技术框架和开发环境
- 项目结构和架构图
- 开发指南和部署发布
- 贡献指南和版本规划

### [PROJECT_ROADMAP.md](./PROJECT_ROADMAP.md)
项目的路线图和规划文档，整合了开发计划、项目分析和后续步骤。
- 项目概述和愿景
- 路线图概览（流程图）
- 当前完成度分析
- 近期任务详细规划
- 中长期目标概述
- 技术实现建议

### [DEVELOPMENT_GUIDELINES.md](./DEVELOPMENT_GUIDELINES.md)
开发指南和规范文档，整合了环境配置和小程序开发规范。
- 开发环境配置
- 项目结构说明
- 代码规范和命名规则
- 组件和页面开发规范
- 网络请求和状态管理
- 性能优化和安全规范
- 常见问题解决方案

### [UI_DESIGN.md](./UI_DESIGN.md)
UI设计指南文档，精简了商业资源UI设计文档。
- 设计原则和色彩规范
- 核心功能UI设计
  - 晋商地域网络搜索功能
  - 两个健康模块
  - 个人中心
- 组件设计规范
- 交互设计模式

### [ERROR_BOOK.md](./ERROR_BOOK.md)
错误记录簿，记录开发过程中遇到的常见问题和解决方案。
- 环境配置问题
- 小程序开发问题
- 多端开发问题
- TypeScript相关问题
- 网络请求问题
- 常见错误代码及解决方法

## 文档使用指南

### 新团队成员
1. 首先阅读 [README.md](../README.md) 了解项目概况
2. 参考 [DEVELOPMENT_GUIDELINES.md](./DEVELOPMENT_GUIDELINES.md) 配置开发环境
3. 查看 [PROJECT_ROADMAP.md](./PROJECT_ROADMAP.md) 了解项目进度和规划
4. 根据分配的任务，参考相关文档进行开发

### 开发人员
1. 遵循 [DEVELOPMENT_GUIDELINES.md](./DEVELOPMENT_GUIDELINES.md) 中的代码规范
2. 参考 [UI_DESIGN.md](./UI_DESIGN.md) 进行界面开发
3. 遇到问题时查阅 [ERROR_BOOK.md](./ERROR_BOOK.md)
4. 根据 [PROJECT_ROADMAP.md](./PROJECT_ROADMAP.md) 了解当前任务优先级

### 项目管理者
1. 使用 [PROJECT_ROADMAP.md](./PROJECT_ROADMAP.md) 跟踪项目进度
2. 参考 [README.md](../README.md) 中的架构图了解项目结构
3. 根据 [UI_DESIGN.md](./UI_DESIGN.md) 审核UI实现

## 文档维护规则

1. **文档更新**：当相关功能或规范发生变化时，应及时更新对应文档
2. **版本控制**：文档的重大变更应记录在文档的修订历史中
3. **内容一致**：确保不同文档之间的内容保持一致，避免冲突
4. **简明扼要**：文档内容应简明扼要，避免冗余
5. **实例说明**：适当使用代码示例和图表说明复杂概念

---

> 最后更新时间：2024年3月 