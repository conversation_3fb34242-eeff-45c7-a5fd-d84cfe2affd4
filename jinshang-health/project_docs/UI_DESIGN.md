# 晋商健康 UI设计指南

## 目录
- [设计原则](#设计原则)
- [核心功能UI设计](#核心功能ui设计)
  - [晋商地域网络搜索功能](#晋商地域网络搜索功能)
  - [两个健康模块](#两个健康模块)
  - [个人中心](#个人中心)
- [组件设计规范](#组件设计规范)
- [交互设计模式](#交互设计模式)

## 设计原则

### 整体风格
- **简洁实用**：界面设计简洁明了，突出核心功能
- **层次分明**：信息层次清晰，重要内容突出显示
- **一致性**：保持视觉元素、交互方式的一致性
- **易用性**：降低用户操作成本，提供直观的操作反馈

### 色彩规范
- **主色调**：#2B5BA1（深蓝色）- 代表专业、可靠
- **辅助色**：#F5A623（橙色）- 用于强调和引导
- **背景色**：#F8F8F8（浅灰色）- 页面背景
- **文字色**：
  - 主要文字：#333333
  - 次要文字：#666666
  - 辅助文字：#999999

### 字体规范
- 标题：18px，粗体
- 副标题：16px，粗体
- 正文：14px，常规
- 辅助文字：12px，常规

## 核心功能UI设计

### 晋商地域网络搜索功能

#### 功能概述
晋商地域网络搜索是本应用的核心差异化功能，旨在帮助晋商企业家快速找到特定地区的同行业晋商资源，打造基于地缘和产业的晋商企业家连接网络。

#### 关键页面设计

1. **资源页面入口**
   - 顶部搜索框：提供通用搜索功能
   - 四个功能入口卡片：商机浏览、晋商网络、行业资讯、政策解读
   - 热门商机列表：展示最新商机

2. **晋商网络搜索页面**
   - 快速地域选择栏：展示常用省份
   - 搜索类型选择：原籍地区/经营地区
   - 地区选择器：省-市-县三级联动
   - 产业类型选择：以标签方式呈现
   - 已选条件展示区：显示当前筛选条件
   - 操作按钮：开始搜索、保存筛选条件

3. **搜索结果页面**
   - 结果统计：显示符合条件的企业家数量
   - 视图切换：列表视图/地图视图
   - 企业家信息卡片：
     - 基本信息：头像、姓名、职位、产业类型
     - 地域信息：原籍地区和经营地区
     - 企业信息：企业名称
     - 操作按钮：查看详情、收藏、一键联系

4. **地图视图**
   - 中国地图：直观展示企业家地理分布
   - 标记点：代表企业家位置
   - 信息卡片：点击标记显示简要信息
   - 过滤选项：全部/已收藏/近期联系
   - 地域焦点切换：原籍地/经营地

5. **企业家详情页面**
   - 基本信息区：头像、姓名、企业、职位
   - 详细信息区：原籍地区、经营地区、产业类型、企业规模、成立时间
   - 业务概况：主营产品、销售区域、合作意向
   - 联系方式：电话、微信、名片
   - 同乡企业家推荐：来自同一原籍地的其他企业家

#### 交互流程
资源主页 → 晋商网络搜索页 → 搜索结果页（列表/地图视图）→ 企业家详情页 → 联系/收藏/分享

#### 设计要点
- 地域选择器支持多种精度的选择（省级、市级、县级）
- 搜索结果支持多种视图方式（列表/地图）
- 企业家信息卡片设计简洁，突出关键信息
- 详情页提供丰富的企业家信息和互动功能

### 两个健康模块

#### 功能概述
两个健康模块包含企业健康发展和企业家健康成长两个子模块，旨在通过数字化手段促进非公有制经济健康发展和非公有制经济人士健康成长。

#### 关键页面设计

1. **两个健康主页**
   - 健康概览：展示核心健康指标和最新动态
   - 导航入口：分别进入企业健康发展和企业家健康成长模块
   - 健康指标仪表盘：直观展示健康状况

2. **企业健康发展模块**
   - 企业健康评估页：多维度评估企业健康状况
   - 政策解读页：最新政策动态和解读
   - 发展指导页：基于评估结果提供发展建议
   - 亲清政商关系建设页：案例和指导

3. **企业家健康成长模块**
   - 健康监测页：身体健康指标监测
   - 心理健康页：心理健康评估和指导
   - 学习成长页：思想引领和企业家精神培养
   - 社会责任页：公益活动和社会责任践行
   - 医疗资源页：高端医疗资源对接

#### 设计要点
- 使用仪表盘、图表等可视化方式展示健康指标
- 评估结果采用直观的评分和建议形式
- 学习资源以卡片形式展示，便于浏览
- 健康监测数据支持趋势图展示

### 个人中心

#### 功能概述
个人中心提供用户信息管理、收藏管理、浏览历史和设置等功能，是用户个性化体验的重要组成部分。

#### 关键页面设计

1. **个人中心主页**
   - 用户信息区：头像、姓名、企业、职位
   - 功能菜单：我的收藏、浏览历史、设置等
   - 版本信息：当前版本号和更新提示

2. **我的收藏页面**
   - 分类标签：商机、企业家、政策等
   - 收藏列表：展示收藏内容
   - 批量操作：取消收藏、分享等

3. **浏览历史页面**
   - 时间线展示：按日期分组展示浏览记录
   - 清除功能：支持清除单条或全部历史

4. **设置页面**
   - 账号设置：修改密码、绑定手机等
   - 隐私设置：数据共享范围、联系方式可见性等
   - 通知设置：消息推送设置
   - 关于：版本信息、用户协议、隐私政策等

#### 设计要点
- 个人信息区设计简洁，突出用户身份
- 功能菜单层次清晰，便于快速访问
- 收藏和历史记录支持分类查看和管理
- 设置页面结构清晰，选项描述准确

## 组件设计规范

### 通用组件

1. **按钮组件**
   - 主要按钮：蓝色背景，白色文字，圆角5px
   - 次要按钮：白色背景，蓝色边框和文字，圆角5px
   - 文本按钮：无背景无边框，蓝色文字

2. **输入框组件**
   - 标准输入框：浅灰色背景，圆角5px，内边距12px
   - 搜索输入框：带搜索图标，圆角22px

3. **卡片组件**
   - 标准卡片：白色背景，轻微阴影，圆角8px
   - 功能卡片：带图标，标题和简介，可点击

4. **标签组件**
   - 信息标签：浅灰色背景，深灰色文字，圆角3px
   - 状态标签：不同颜色背景对应不同状态，圆角3px

5. **列表组件**
   - 标准列表：左侧图标/头像，右侧信息和箭头
   - 详情列表：标题和内容垂直排列，可展开

### 业务组件

1. **地域选择器组件**
   - 支持省-市-县三级联动
   - 支持模糊选择（如仅选择省份）
   - 支持多地区选择

2. **产业分类选择器组件**
   - 支持主分类和子分类选择
   - 以标签方式展示已选项
   - 支持搜索和历史记录

3. **企业家信息卡片组件**
   - 展示头像、姓名、职位、企业等基本信息
   - 展示原籍地区和经营地区
   - 提供快捷操作按钮

4. **健康指标仪表盘组件**
   - 环形进度条展示总体健康指数
   - 多维度雷达图展示各项指标
   - 支持时间维度的趋势展示

## 交互设计模式

### 导航模式
- 底部标签栏：首页、资源、健康、我的
- 页面内导航：顶部标签页或分段控制器
- 返回导航：左上角返回按钮，支持手势返回

### 列表与详情
- 列表页使用下拉刷新、上拉加载更多
- 列表项点击进入详情页
- 详情页支持分享、收藏等操作

### 表单交互
- 输入框获取焦点时键盘弹出，失去焦点时键盘收起
- 表单验证实时提示，错误信息清晰可见
- 提交按钮状态反馈（加载中、成功、失败）

### 反馈机制
- 操作成功：轻提示（Toast）
- 操作失败：错误提示（带有具体原因）
- 加载状态：加载指示器或骨架屏
- 空状态：友好的空状态提示和建议操作 