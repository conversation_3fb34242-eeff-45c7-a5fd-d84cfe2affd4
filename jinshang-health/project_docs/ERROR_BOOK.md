# 晋商健康多端应用 - 错误记录簿

本文档记录开发过程中遇到的常见错误和解决方案，帮助团队成员快速解决问题。

## 目录
1. [环境配置问题](#环境配置问题)
2. [小程序开发问题](#小程序开发问题)
3. [多端开发问题](#多端开发问题)
4. [TypeScript相关问题](#typescript相关问题)
5. [网络请求问题](#网络请求问题)

## 环境配置问题

| 问题 | 解决方案 |
|------|----------|
| **npm依赖安装失败** | 1. 清除npm缓存: `npm cache clean --force`<br>2. 使用淘宝镜像: `npm config set registry https://registry.npmmirror.com`<br>3. 重新安装依赖: `npm install` |
| **微信开发者工具无法识别npm包** | 1. 确保启用了"使用npm模块"选项<br>2. 点击工具栏中的"构建npm"按钮<br>3. 重启微信开发者工具 |

## 小程序开发问题

| 问题 | 解决方案 |
|------|----------|
| **页面无法正常跳转** | 1. 检查app.json中是否已注册目标页面<br>2. 检查页面路径是否正确（注意大小写）<br>3. 检查是否超过了页面栈限制（最多10层） |
| **自定义组件无法显示** | 1. 检查页面的json文件中是否正确引入了组件<br>2. 检查组件路径是否正确<br>3. 检查组件是否正确注册（Component函数调用） |
| **setData性能问题** | 1. 合并setData调用，一次更新多个数据<br>2. 避免直接更新整个数组或对象，只更新变化的部分<br>3. 使用节流或防抖技术限制setData调用频率 |

## 多端开发问题

| 问题 | 解决方案 |
|------|----------|
| **平台差异导致的兼容性问题** | 1. 使用条件编译处理平台差异<br>2. 创建平台特定的实现文件<br>3. 使用统一的API适配层 |
| **样式在不同平台显示不一致** | 1. 使用基础样式重置（reset.css）<br>2. 避免使用平台特定的样式属性<br>3. 为不同平台创建特定的样式类 |

## TypeScript相关问题

| 问题 | 解决方案 |
|------|----------|
| **类型定义错误** | 1. 确保安装了正确版本的类型定义文件（如miniprogram-api-typings）<br>2. 检查tsconfig.json中的配置是否正确<br>3. 为第三方库添加类型声明文件 |
| **微信API类型提示不完整** | 1. 更新miniprogram-api-typings到最新版本<br>2. 在typings目录下创建自定义的类型声明文件<br>3. 使用类型断言（as）解决临时问题 |

## 网络请求问题

| 问题 | 解决方案 |
|------|----------|
| **请求失败或超时** | 1. 检查网络连接是否正常<br>2. 检查API地址是否正确<br>3. 增加请求超时时间<br>4. 实现请求重试机制 |
| **小程序请求域名未配置** | 1. 在微信公众平台后台添加对应域名到request合法域名列表<br>2. 开发环境可临时关闭域名校验（仅用于开发） |

## 常见错误代码及解决方法

| 错误代码 | 描述 | 解决方法 |
|---------|------|----------|
| **-1** | 系统繁忙 | 稍后重试或检查请求参数 |
| **40029** | 无效的code | 确保code未被使用且未过期 |
| **41008** | 缺少code参数 | 检查登录流程，确保获取了code |
| **40226** | 高风险等级用户，小程序登录拦截 | 引导用户完成微信风险解除流程 |
| **12006** | 小程序未发布或已下架 | 检查小程序状态，确保已发布 |

---

> 注意：本文档将持续更新，团队成员遇到新问题时，请按照上述格式添加到相应分类中。 