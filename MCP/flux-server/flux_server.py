# /// script
# dependencies = [
#   "mcp",
#   "together",
#   "python-dotenv"
# ]
# ///

import os
import base64
from typing import Any
import asyncio
from datetime import datetime
from pathlib import Path
from mcp.server import Server, NotificationOptions
from mcp.server.models import InitializationOptions
import mcp.server.stdio
import mcp.types as types
from together import Together
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Initialize Together client
together_client = Together()

# Create server instance
server = Server("flux-image-server")

# Set up images directory
IMAGES_DIR = Path(__file__).parent / "generated_images"
IMAGES_DIR.mkdir(exist_ok=True)

@server.list_tools()
async def handle_list_tools() -> list[types.Tool]:
    """List available tools for image generation"""
    return [
        types.Tool(
            name="generate-image",
            description="Generate an image using FLUX AI",
            inputSchema={
                "type": "object",
                "properties": {
                    "prompt": {
                        "type": "string",
                        "description": "Text description of the image to generate"
                    },
                    "width": {
                        "type": "number",
                        "description": "Width of the generated image (512, 768, or 1024)",
                        "enum": [512, 768, 1024],
                        "default": 1024
                    },
                    "height": {
                        "type": "number",
                        "description": "Height of the generated image (512, 768, or 1024)",
                        "enum": [512, 768, 1024],
                        "default": 768
                    },
                    "steps": {
                        "type": "number",
                        "description": "Number of inference steps (1-100)",
                        "minimum": 1,
                        "maximum": 50,
                        "default": 50
                    },
                    "output_directory": {
                        "type": "string",
                        "description": "Custom directory to save the generated image (optional, can be absolute or relative path)"
                    }
                },
                "required": ["prompt"]
            }
        )
    ]

def save_image(image_b64: str, prompt: str, output_dir: Path | None = None) -> str:
    """Save base64 image data to file and return the path"""
    # Create a filename based on timestamp and first few words of prompt
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    # Take first 5 words of prompt and clean them for filename
    prompt_words = "_".join(prompt.split()[:5])
    prompt_words = "".join(c for c in prompt_words if c.isalnum() or c in "_-")
    filename = f"{timestamp}_{prompt_words}.png"
    
    # Use custom output directory if provided, otherwise use default
    if output_dir:
        # If path is not absolute, make it relative to the script directory
        if not output_dir.is_absolute():
            output_dir = Path(__file__).parent / output_dir
    else:
        output_dir = IMAGES_DIR
        
    # Ensure the directory exists
    output_dir.mkdir(exist_ok=True, parents=True)
    
    # Save the image
    image_path = output_dir / filename
    image_bytes = base64.b64decode(image_b64)
    with open(image_path, "wb") as f:
        f.write(image_bytes)
    
    return str(image_path)

@server.call_tool()
async def handle_call_tool(
    name: str, 
    arguments: dict[str, Any] | None
) -> list[types.TextContent | types.ImageContent]:
    """Handle tool execution requests"""
    if name != "generate-image":
        raise ValueError(f"Unknown tool: {name}")
    
    if not arguments:
        raise ValueError("Missing arguments")

    prompt = arguments.get("prompt")
    if not prompt:
        raise ValueError("Missing prompt")

    width = arguments.get("width", 1440)
    height = arguments.get("height", 1440)
    steps = arguments.get("steps", 50)
    
    # Get custom output directory if provided
    output_directory = arguments.get("output_directory")
    output_dir = Path(output_directory) if output_directory else None
    
    # Log the output directory for debugging
    if output_dir:
        print(f"Custom output directory requested: {output_dir}")

    try:
        # Generate image using Together API
        response = together_client.images.generate(
            prompt=prompt,
            model="black-forest-labs/FLUX.1-dev",
            width=width,
            height=height,
            steps=steps,
            n=1,
            response_format="b64_json"
        )

        # Extract base64 image data and save it
        image_b64 = response.data[0].b64_json
        saved_path = save_image(image_b64, prompt, output_dir)

        return [
            types.ImageContent(
                type="image",
                data=image_b64,
                mimeType="image/png"
            ),
            types.TextContent(
                type="text",
                text=f"Image saved to: {saved_path}"
            )
        ]
    except Exception as e:
        return [
            types.TextContent(
                type="text",
                text=f"Failed to generate image: {str(e)}"
            )
        ]

async def main():
    """Run the server"""
    async with mcp.server.stdio.stdio_server() as (read_stream, write_stream):
        await server.run(
            read_stream,
            write_stream,
            InitializationOptions(
                server_name="flux-image-server",
                server_version="0.1.0",
                capabilities=server.get_capabilities(
                    notification_options=NotificationOptions(),
                    experimental_capabilities={},
                ),
            ),
        )

if __name__ == "__main__":
    asyncio.run(main())