# FLUX AI Image Generation MCP Server

This MCP server enables <PERSON> to generate images using FLUX AI through the Together API.

## Setup

1. Install uv (if you haven't already):
```bash
curl -LsSf https://astral.sh/uv/install.sh | sh
```

2. Create and activate a virtual environment:
```bash
uv venv
source .venv/bin/activate  # On Unix/MacOS
# OR
.venv\Scripts\activate  # On Windows
```

3. Install dependencies:
```bash
uv pip install -r requirements.txt
```

4. Edit `.env` file and add your Together API key:
```
TOGETHER_API_KEY=your_api_key_here
```

5. Configure Claude for Desktop:
Edit `~/Library/Application Support/Claude/claude_desktop_config.json` (on MacOS) or `%APPDATA%\Claude\claude_desktop_config.json` (on Windows):

```json
{
    "mcpServers": {
        "flux": {
            "command": "uv",
            "args": ["--directory", "/absolute/path/to/flux-server", "run", "flux_server.py"]
        }
    }
}
```

6. Run the server:
```bash
uv run flux_server.py
```

## Usage

Once configured, you can ask <PERSON> to generate images with commands like:
- "Generate an image of a sunset over mountains"
- "Create an illustration of a futuristic city"

The server will use FLUX AI to generate the image and return it to Claude.