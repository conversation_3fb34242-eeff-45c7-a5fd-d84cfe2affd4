# FLUX AI 图像生成 MCP 服务器分析

FLUX AI 图像生成 MCP 服务器是一个基于 Model Context Protocol (MCP) 的服务器实现，它允许 Claude 通过 Together API 使用 FLUX AI 生成图像。这个服务器充当了 Claude 和 FLUX AI 图像生成能力之间的桥梁。

## 项目概述

FLUX AI 图像生成 MCP 服务器使用 Python 构建，利用 MCP 框架为 Claude 提供图像生成功能。服务器暴露了一个工具，Claude 可以使用这个工具基于文本提示生成图像。

## 核心组件

1. **MCP 服务器**：处理 Claude 和图像生成服务之间通信的核心组件。
2. **Together API 客户端**：用于与 FLUX AI 模型交互进行图像生成。
3. **图像存储**：生成的图像以时间戳和基于提示的文件名保存在本地。

## MCP 服务器实现逻辑

```mermaid
classDiagram
    class Server {
        +name: string
        +list_tools()
        +call_tool()
        +run()
    }
    
    class ToolHandler {
        +handle_list_tools()
        +handle_call_tool()
    }
    
    class ImageGenerator {
        +generate_image()
        +save_image()
    }
    
    class MCPSession {
        +read_stream
        +write_stream
        +process_messages()
    }
    
    Server --> ToolHandler
    Server --> MCPSession
    ToolHandler --> ImageGenerator
    ImageGenerator --> Together
```

服务器遵循 Model Context Protocol，这是一种标准化的方式，允许 AI 模型与外部工具和服务交互。以下是实现的工作方式：

1. **初始化**：服务器以名称初始化并设置必要的组件。
2. **工具注册**：服务器注册一个名为 "generate-image" 的工具，Claude 可以使用该工具生成图像。
3. **请求处理**：当 Claude 调用该工具时，服务器处理请求，验证参数，并将其转发给 Together API。
4. **图像生成**：Together API 使用 FLUX.1-dev 模型生成图像。
5. **图像存储**：生成的图像以基于时间戳和提示的文件名保存在本地。
6. **响应**：服务器返回图像数据和确认图像保存位置的文本消息。

## MCP 协议流程

```mermaid
sequenceDiagram
    participant Claude as Claude (客户端)
    participant MCP as MCP 服务器
    participant Together as Together API
    participant FileSystem as 文件系统

    Claude->>MCP: 初始化请求
    MCP->>Claude: 初始化响应（能力）
    Claude->>MCP: 初始化通知
    Note over Claude,MCP: 连接已建立

    Claude->>MCP: list_tools 请求
    MCP->>Claude: list_tools 响应（generate-image 工具）
    
    Claude->>MCP: call_tool 请求（generate-image）
    MCP->>Together: API 请求（提示、尺寸、步骤）
    Together->>MCP: 图像数据（base64）
    MCP->>FileSystem: 保存图像
    FileSystem->>MCP: 保存路径
    MCP->>Claude: call_tool 响应（图像 + 文本）
```

## 服务器实现细节

`flux_server.py` 中的服务器实现由几个关键组件组成：

1. **Server 类**：
   - 管理服务器名称和工具注册
   - 提供 list_tools() 和 call_tool() 方法
   - 运行服务器处理请求

2. **ToolHandler**：
   - 处理工具列表请求
   - 处理工具调用请求

3. **ImageGenerator**：
   - 生成图像（通过 Together API）
   - 保存图像到文件系统

4. **MCPSession**：
   - 管理读写流
   - 处理消息传递

## 代码流程

1. 服务器初始化并设置必要的组件：
   - 加载环境变量（Together API 密钥）
   - 初始化 Together 客户端
   - 创建服务器实例
   - 设置图像目录

2. 服务器注册 MCP 请求的处理程序：
   - `handle_list_tools()`：返回可用工具（generate-image）
   - `handle_call_tool()`：处理工具调用并生成图像

3. 当收到工具调用时：
   - 服务器验证参数
   - 调用 Together API 生成图像
   - 将图像保存到文件系统
   - 返回图像数据和确认消息

4. 主函数设置服务器：
   - 创建用于通信的 stdio 流
   - 使用功能初始化服务器
   - 运行服务器处理传入请求

## 配置和设置

服务器需要：
1. 存储在 `.env` 文件中的 Together API 密钥
2. Claude 桌面配置文件中的配置，以注册 MCP 服务器
3. 依赖项：mcp、together、python-dotenv

这种实现允许 Claude 通过标准化协议无缝地使用 FLUX AI 生成图像，增强 Claude 的功能，而无需更改核心模型。