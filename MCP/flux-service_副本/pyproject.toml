[project]
name = "flux-service"
version = "1.0"
description = "ai drawing"
readme = "README.md"
requires-python = ">=3.11"
dependencies = [
    "mcp>=1.0.0",
    "together",
    "pydantic",
    "anyio",
    "python-dotenv"
]

[[project.authors]]
name = "nicekate"
email = "<EMAIL>"

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project.scripts]
flux-service = "flux_service.server:run_server"
