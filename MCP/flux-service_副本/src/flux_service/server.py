import asyncio
import base64
import os
import sys
from pathlib import Path
from typing import Any, Sequence
import uuid

from dotenv import load_dotenv
from mcp.server.models import InitializationOptions
import mcp.types as types
from mcp.server import NotificationOptions, Server
from pydantic import AnyUrl
import mcp.server.stdio
from together import Together

def setup_environment():
    """Setup environment variables from .env file if it exists"""
    env_path = Path(__file__).resolve().parent.parent.parent / '.env'
    if env_path.exists():
        load_dotenv(dotenv_path=env_path)
    
    # Get API key from environment variables
    api_key = os.getenv('TOGETHER_API_KEY')
    if not api_key:
        print("Error: TOGETHER_API_KEY environment variable is not set", file=sys.stderr)
        print("Please create a .env file with TOGETHER_API_KEY=your_api_key or set it in the environment", file=sys.stderr)
        sys.exit(1)
    return api_key

# Setup environment and initialize Together client
api_key = setup_environment()
os.environ["TOGETHER_API_KEY"] = api_key
together_client = Together()

# Create server instance
server = Server("flux-service")

# Store notes as a simple key-value dict to demonstrate state management
notes: dict[str, str] = {}

# Store generated images as a simple key-value dict
generated_images: dict[str, str] = {}

# Create images directory if it doesn't exist
IMAGES_DIR = Path('/Users/<USER>/claude-connect/flux-image')
IMAGES_DIR.mkdir(parents=True, exist_ok=True)

@server.list_resources()
async def handle_list_resources() -> list[types.Resource]:
    """
    List available note resources.
    Each note is exposed as a resource with a custom note:// URI scheme.
    """
    resources = [
        types.Resource(
            uri=AnyUrl(f"note://internal/{name}"),
            name=f"Note: {name}",
            description=f"A simple note named {name}",
            mimeType="text/plain",
        )
        for name in notes
    ]
    
    # Add default image resource
    resources.append(
        types.Resource(
            uri=AnyUrl("flux://default/sample"),
            name="Sample Image Generation",
            mimeType="image/png",
            description="Generate an image using FLUX model"
        )
    )
    
    # Add generated images as resources
    for image_id, image_path in generated_images.items():
        resources.append(
            types.Resource(
                uri=AnyUrl(f"flux://images/{image_id}.png"),
                name=f"Generated Image: {image_id}",
                mimeType="image/png",
                description=f"Generated image with ID: {image_id}"
            )
        )
    
    return resources

@server.read_resource()
async def handle_read_resource(uri: AnyUrl) -> str:
    """
    Read a specific note's content by its URI.
    The note name is extracted from the URI host component.
    """
    if uri.scheme == "note":
        name = uri.path
        if name is not None:
            name = name.lstrip("/")
            return notes[name]
        raise ValueError(f"Note not found: {name}")
    elif uri.scheme == "flux":
        path = str(uri).replace("flux://", "")
        if path == "default/sample":
            # Generate a sample image
            response = together_client.images.generate(
                prompt="A beautiful landscape with mountains and a lake",
                model="black-forest-labs/FLUX.1-dev",
                width=1024,
                height=768,
                steps=28,
                n=1,
                response_format="b64_json"
            )
            return response.data[0].b64_json
        
        # Handle generated images
        image_filename = path.replace("images/", "")
        image_path = IMAGES_DIR / image_filename
        
        if image_path.exists():
            return base64.b64encode(image_path.read_bytes()).decode('utf-8')
        
        raise ValueError(f"Image not found: {image_filename}")
    else:
        raise ValueError(f"Unsupported URI scheme: {uri.scheme}")

@server.list_prompts()
async def handle_list_prompts() -> list[types.Prompt]:
    """
    List available prompts.
    Each prompt can have optional arguments to customize its behavior.
    """
    return [
        types.Prompt(
            name="summarize-notes",
            description="Creates a summary of all notes",
            arguments=[
                types.PromptArgument(
                    name="style",
                    description="Style of the summary (brief/detailed)",
                    required=False,
                )
            ],
        )
    ]

@server.get_prompt()
async def handle_get_prompt(
    name: str, arguments: dict[str, str] | None
) -> types.GetPromptResult:
    """
    Generate a prompt by combining arguments with server state.
    The prompt includes all current notes and can be customized via arguments.
    """
    if name != "summarize-notes":
        raise ValueError(f"Unknown prompt: {name}")

    style = (arguments or {}).get("style", "brief")
    detail_prompt = " Give extensive details." if style == "detailed" else ""

    return types.GetPromptResult(
        description="Summarize the current notes",
        messages=[
            types.PromptMessage(
                role="user",
                content=types.TextContent(
                    type="text",
                    text=f"Here are the current notes to summarize:{detail_prompt}\n\n"
                    + "\n".join(
                        f"- {name}: {content}"
                        for name, content in notes.items()
                    ),
                ),
            )
        ],
    )

@server.list_tools()
async def list_tools() -> list[types.Tool]:
    """List available image generation tools"""
    return [
        types.Tool(
            name="generate_image",
            description="Generate an image using FLUX model",
            inputSchema={
                "type": "object",
                "properties": {
                    "prompt": {
                        "type": "string",
                        "description": "The prompt to generate the image from"
                    },
                    "width": {
                        "type": "integer",
                        "description": "Width of the generated image (default: 1024)",
                        "default": 1024,
                        "minimum": 512,
                        "maximum": 2048
                    },
                    "height": {
                        "type": "integer",
                        "description": "Height of the generated image (default: 768)",
                        "default": 768,
                        "minimum": 512,
                        "maximum": 2048
                    },
                    "steps": {
                        "type": "integer",
                        "description": "Number of inference steps (default: 28)",
                        "default": 28,
                        "minimum": 1,
                        "maximum": 50
                    }
                },
                "required": ["prompt"]
            }
        )
    ]

@server.call_tool()
async def call_tool(name: str, arguments: Any) -> Sequence[types.TextContent | types.ImageContent]:
    """Handle tool execution requests"""
    if name != "generate_image":
        raise ValueError(f"Unknown tool: {name}")
    
    if not isinstance(arguments, dict) or "prompt" not in arguments:
        raise ValueError("Prompt is required")

    prompt = arguments["prompt"]
    width = min(max(int(arguments.get("width", 1024)), 512), 2048)
    height = min(max(int(arguments.get("height", 768)), 512), 2048)
    steps = min(max(int(arguments.get("steps", 28)), 1), 50)

    try:
        response = together_client.images.generate(
            prompt=prompt,
            model="black-forest-labs/FLUX.1-dev",
            width=width,
            height=height,
            steps=steps,
            n=1,
            response_format="b64_json"
        )
        
        # Generate a unique filename
        image_id = str(uuid.uuid4())
        image_filename = f"{image_id}.png"
        image_path = IMAGES_DIR / image_filename
        
        # Decode and save the image
        image_data = response.data[0].b64_json
        image_bytes = base64.b64decode(image_data)
        image_path.write_bytes(image_bytes)
        
        # Store the image reference
        generated_images[image_id] = str(image_path)
        
        # Generate image URL
        image_url = f"flux://images/{image_filename}"
        
        # Notify clients that resources have changed
        await server.request_context.session.send_resource_list_changed()
        
        return [
            types.TextContent(
                type="text",
                text=f"Image generated successfully!\nImage URL: {image_url}\nLocal path: {image_path}"
            ),
            types.ImageContent(
                type="image",
                mimeType="image/png",
                data=image_data
            )
        ]
        
    except Exception as e:
        raise ValueError(f"Failed to generate image: {str(e)}")

async def main():
    """Run the server."""
    async with mcp.server.stdio.stdio_server() as (read_stream, write_stream):
        await server.run(
            read_stream,
            write_stream,
            InitializationOptions(
                server_name="flux-service",
                server_version="1.0",
                capabilities=server.get_capabilities(
                    notification_options=NotificationOptions(),
                    experimental_capabilities={},
                ),
            ),
        )

def run_server():
    """Entry point for the server"""
    asyncio.run(main())

if __name__ == "__main__":
    run_server()