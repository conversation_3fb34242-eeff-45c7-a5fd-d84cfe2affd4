# Allergy Assistant Web App

A modern Next.js 14 application providing AI-powered guidance for allergy management and tracking.

## Project Structure

```
src/
├── app/                    # Next.js 14 App Router pages
├── components/            # Shared UI components
│   ├── ui/               # Base UI components
│   └── shared/           # Complex shared components
├── features/             # Feature-based modules
│   ├── user/            # User authentication & profile
│   ├── chat/            # AI chat functionality
│   ├── allergens/       # Allergen management
│   └── tracking/        # Symptom tracking
├── lib/                  # Core utilities and services
│   ├── api/             # API client and endpoints
│   ├── utils/           # Utility functions
│   └── config/          # Configuration files
├── hooks/               # Custom React hooks
├── store/               # State management (Redux)
└── types/               # TypeScript type definitions
```

## Key Features

- 🔐 User Authentication & Profile Management
- 🤖 AI-Powered Allergy Guidance
- 📊 Allergen Tracking and Analysis
- 🗓️ Symptom Journal
- 🚨 Emergency Action Plan Management
- 🎨 Modern UI with Tailwind CSS

## Tech Stack

- Next.js 14 (App Router)
- TypeScript
- Tailwind CSS
- Redux Toolkit
- Prisma (Database ORM)
- NextAuth.js (Authentication)

## Getting Started

1. Install dependencies:
   ```bash
   npm install
   ```

2. Set up environment variables:
   ```bash
   cp .env.example .env.local
   ```

3. Run the development server:
   ```bash
   npm run dev
   ```

4. Open [http://localhost:3000](http://localhost:3000)

## Development Guidelines

- Follow feature-based modular architecture
- Use TypeScript for type safety
- Implement responsive design with Tailwind CSS
- Write unit tests for critical functionality
- Follow Next.js 14 best practices 