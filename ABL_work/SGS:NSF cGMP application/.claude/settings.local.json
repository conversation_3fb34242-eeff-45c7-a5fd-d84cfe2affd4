{"permissions": {"allow": ["<PERSON><PERSON>(mkdir:*)", "Bash(node:*)", "Bash(find:*)", "Bash(ls:*)", "Bash(./document-tools.sh:*)", "Bash(./generate-pdf.sh:*)", "Bash(git add:*)", "<PERSON><PERSON>(mv:*)", "Bash(git rm:*)", "Bash(grep:*)", "Bash(npm install:*)", "Bash(rm:*)", "<PERSON><PERSON>(chmod:*)", "Bash(./generate-html.sh:*)", "Bash(git commit:*)", "Bash(git filter-branch:*)", "Bash(git reset:*)", "<PERSON><PERSON>(true)", "<PERSON>sh(git check-ignore:*)", "<PERSON><PERSON>(sed:*)", "<PERSON><PERSON>(realpath:*)", "Bash(kill:*)", "<PERSON><PERSON>(curl:*)", "<PERSON><PERSON>(pkill:*)", "Bash(npm start)", "Bash(echo)", "<PERSON><PERSON>(cat:*)"], "deny": []}}