#!/bin/bash

# 文档 PDF 生成便捷脚本 (支持 SOP、MMR 和 SPEC)
# 调用 Document_PDF_Tools 文件夹中的统一工具

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

# 检查 ABL-Document-System/Document-Support-Tools/pdf-generators 目录是否存在
if [ ! -d "ABL-Document-System/Document-Support-Tools/pdf-generators" ]; then
    echo -e "${RED}错误: ABL-Document-System/Document-Support-Tools/pdf-generators 目录不存在${NC}"
    exit 1
fi

# 进入 pdf-generators 目录并执行
cd "ABL-Document-System/Document-Support-Tools/pdf-generators"

if [ $# -eq 0 ]; then
    # 没有参数 - 批量生成
    echo -e "${YELLOW}正在批量生成所有文档 PDF...${NC}"
    ./document-tools.sh
elif [ "$1" = "-h" ] || [ "$1" = "--help" ]; then
    # 显示帮助
    echo "文档 PDF 生成工具 (支持 SOP、MMR 和 SPEC)"
    echo "======================================"
    echo ""
    echo "用法:"
    echo "  ./generate-pdf.sh                    # 批量生成所有文档的PDF"
    echo "  ./generate-pdf.sh SOP                # 批量生成所有SOP文档的PDF"
    echo "  ./generate-pdf.sh MMR                # 批量生成所有MMR文档的PDF"
    echo "  ./generate-pdf.sh SPEC               # 批量生成所有SPEC文档的PDF"
    echo "  ./generate-pdf.sh RMS                # 批量生成所有RMS文档的PDF"
    echo "  ./generate-pdf.sh MGMT               # 批量生成所有SOP管理文档的PDF"
    echo "  ./generate-pdf.sh INTERNAL           # [已废弃] 使用MGMT替代"
    echo "  ./generate-pdf.sh [文件名]           # 生成指定文件的PDF"
    echo ""
    echo "支持的文档类型:"
    echo "  - SOP: ABL-SOP-X-XXX_title.md"
    echo "  - MMR: ABL-MMR-XXX-Product-Name.md"
    echo "  - SPEC: ABL-SPEC-XXX-Product-Name.md"
    echo "  - RMS: ABL-RMS-XXX-Material-Name.md"
    echo "  - MGMT: ABL-SOP-Master-Directory.md, ABL-SOP-Gap-Analysis.md 等管理文档"
    echo "  - INTERNAL: [已废弃] 使用MGMT替代"
    echo ""
    echo "示例:"
    echo "  ./generate-pdf.sh"
    echo "  ./generate-pdf.sh SOP"
    echo "  ./generate-pdf.sh MMR"
    echo "  ./generate-pdf.sh SPEC"
    echo "  ./generate-pdf.sh RMS"
    echo "  ./generate-pdf.sh ABL-SOP-A-001_sop_writing_guidelines.md"
    echo "  ./generate-pdf.sh ABL-MMR-001-Vitamin-C-Tablet.md"
    echo "  ./generate-pdf.sh ABL-SPEC-001-Vitamin-C-Tablet.md"
    echo "  ./generate-pdf.sh ABL-RMS-001-Vitamin-C.md"
    echo "  ./generate-pdf.sh 'SOP markdown file/ABL-SOP-A-001_sop_writing_guidelines.md'"
    echo "  ./generate-pdf.sh '/full/path/to/document.md'"
elif [ "$1" = "SOP" ] || [ "$1" = "sop" ]; then
    # 批量生成SOP
    echo -e "${YELLOW}正在批量生成所有 SOP 文档 PDF...${NC}"
    ./document-tools.sh SOP
elif [ "$1" = "MMR" ] || [ "$1" = "mmr" ]; then
    # 批量生成MMR
    echo -e "${YELLOW}正在批量生成所有 MMR 文档 PDF...${NC}"
    ./document-tools.sh MMR
elif [ "$1" = "SPEC" ] || [ "$1" = "spec" ]; then
    # 批量生成SPEC
    echo -e "${YELLOW}正在批量生成所有 SPEC 文档 PDF...${NC}"
    ./document-tools.sh SPEC
elif [ "$1" = "RMS" ] || [ "$1" = "rms" ]; then
    # 批量生成RMS
    echo -e "${YELLOW}正在批量生成所有 RMS 文档 PDF...${NC}"
    ./document-tools.sh RMS
elif [ "$1" = "MGMT" ] || [ "$1" = "mgmt" ]; then
    # 批量生成SOP管理文档
    echo -e "${YELLOW}正在批量生成所有 SOP 管理文档 PDF...${NC}"
    ./document-tools.sh MGMT
elif [ "$1" = "INTERNAL" ] || [ "$1" = "internal" ]; then
    # 批量生成内部文档（已废弃，重定向到MGMT）
    echo -e "${YELLOW}⚠️  INTERNAL选项已废弃，请使用MGMT选项${NC}"
    echo -e "${YELLOW}正在自动重定向到MGMT处理...${NC}"
    ./document-tools.sh MGMT
else
    # 有参数 - 生成指定文件
    echo -e "${YELLOW}正在生成指定文件的 PDF...${NC}"
    
    # 处理不同类型的路径输入
    if [[ "$1" = /* ]]; then
        # 绝对路径，直接传递
        ./document-tools.sh "$1"
    elif [[ "$1" == *"/"* ]]; then
        # 相对路径，需要从主目录解析
        cd ../../..
        resolved_path=$(realpath "$1" 2>/dev/null || echo "$1")
        cd "ABL-Document-System/Document-Support-Tools/pdf-generators"
        ./document-tools.sh "$resolved_path"
    else
        # 纯文件名，让内部工具智能查找
        ./document-tools.sh "$1"
    fi
fi

# 返回原目录
cd ../../..

echo -e "${GREEN}✅ 操作完成！PDF 文件保存在相应的 pdf_version 目录${NC}" 