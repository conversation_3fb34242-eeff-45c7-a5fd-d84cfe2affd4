# 常见编程错误预防

## CSS选择器错误

### ❌ 无效操作符
```javascript
// 错误：CSS中不存在 != 操作符
element.querySelector('option[value!="all"]')

// 正确：使用DOM方法
const options = element.querySelectorAll('option');
const filtered = Array.from(options).filter(opt => opt.value !== 'all');
```

## 错误处理规范

### ❌ 模糊的错误信息
```javascript
catch (error) {
    console.error('Operation failed');
}
```

### ✅ 具体的错误信息  
```javascript
catch (error) {
    console.error('Failed to load documents:', error);
    this.addConsoleMessage(`❌ Failed to load documents: ${error.message}`, 'error');
}
```

## DOM操作安全

### ❌ 未检查元素存在
```javascript
document.getElementById('myId').style.display = 'none';
```

### ✅ 检查元素存在
```javascript
const element = document.getElementById('myId');
if (element) {
    element.style.display = 'none';
}
```