#!/bin/bash

# 文档 PDF 生成工具
# 支持Base64图片处理，简单可靠

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m'

# 显示帮助
show_help() {
    echo "文档生成工具 (PDF & HTML支持)"
    echo "================================"
    echo ""
    echo "用法:"
    echo "  ./document-tools.sh                    # 批量生成所有文档的PDF"
    echo "  ./document-tools.sh SOP                # 批量生成所有SOP文档的PDF"
    echo "  ./document-tools.sh MMR                # 批量生成所有MMR文档的PDF"
    echo "  ./document-tools.sh SPEC               # 批量生成所有SPEC文档的PDF"
    echo "  ./document-tools.sh RMS                # 批量生成所有RMS文档的PDF"
    echo "  ./document-tools.sh REC                # 批量生成所有REC记录文档的PDF"
    echo "  ./document-tools.sh MGMT               # 批量生成所有SOP管理文档的PDF"
    echo "  ./document-tools.sh INTERNAL           # [已废弃] 使用MGMT替代"
    echo "  ./document-tools.sh [文件名]           # 生成指定文件的PDF"
    echo "  ./document-tools.sh flowchart          # 流程图相关操作"
    echo "  ./document-tools.sh html               # HTML生成相关操作"
    echo ""
    echo "HTML生成操作:"
    echo "  ./document-tools.sh html               # 生成所有REC文档的交互式HTML表单"
    echo "  ./document-tools.sh html REC           # 生成所有REC文档的HTML"
    echo "  ./document-tools.sh html <文件名>      # 生成指定REC文档的HTML"
    echo ""
    echo "流程图操作:"
    echo "  ./document-tools.sh flowchart          # 生成所有mermaid文件的流程图"
    echo "  ./document-tools.sh flowchart <文件名> # 生成指定mermaid文件的流程图"
    echo ""
    echo "支持的文档类型:"
    echo "  - SOP: ABL-SOP-X-XXX_title.md"
    echo "  - MMR: ABL-MMR-XXX-Product-Name.md"
    echo "  - SPEC: ABL-SPEC-XXX-Product-Name.md"
    echo "  - RMS: ABL-RMS-XXX-Material-Name.md"
    echo "  - REC: ABL-REC-XXX-Record-Name.md"
    echo "  - MGMT: ABL-SOP-Master-Directory.md, ABL-SOP-Gap-Analysis.md 等管理文档"
    echo "  - INTERNAL: [已废弃] 使用MGMT替代"
    echo ""
    echo "流程图文件位置:"
    echo "  - 源文件: ../../Process-Flowcharts/mermaid-sources/*.mmd"
    echo "  - 输出: ../../Process-Flowcharts/generated-flowcharts/*.png"
    echo ""
    echo "特性:"
    echo "  ✅ 自动检测图片并使用Base64编码"
    echo "  ✅ 支持PNG、JPG、SVG等格式"
    echo "  ✅ 完全离线处理，无需网络"
    echo "  ✅ 保持原有专业文档格式"
    echo ""
    echo "示例:"
    echo "  ./document-tools.sh"
    echo "  ./document-tools.sh SOP"
    echo "  ./document-tools.sh ABL-SOP-A-001_example.md"
    echo "  ./document-tools.sh flowchart"
    echo "  ./document-tools.sh flowchart vitamin-c-manufacturing-professional.mmd"
}

# 检查Node.js依赖
check_dependencies() {
    if ! command -v node &> /dev/null; then
        echo -e "${RED}错误: 未找到 Node.js，请先安装 Node.js${NC}"
        exit 1
    fi
    
    if [ ! -f "package.json" ]; then
        echo -e "${RED}错误: 未找到 package.json，请确保在正确的目录中运行${NC}"
        exit 1
    fi
    
    if [ ! -d "node_modules" ]; then
        echo -e "${YELLOW}正在安装依赖...${NC}"
        npm install
    fi
}

# 生成流程图
generate_flowchart() {
    local filename="$1"
    
    echo -e "${BLUE}🎨 流程图生成工具${NC}"
    
    # 确保输出目录存在
    if [ ! -d "../../Process-Flowcharts/generated-flowcharts" ]; then
        echo -e "${YELLOW}创建输出目录: ../../Process-Flowcharts/generated-flowcharts${NC}"
        mkdir -p "../../Process-Flowcharts/generated-flowcharts"
    fi
    
    if [ -z "$filename" ]; then
        # 没有指定文件名，生成所有mermaid文件
        echo -e "${YELLOW}正在扫描 ../../Process-Flowcharts/mermaid-sources/ 目录...${NC}"

if [ ! -d "../../Process-Flowcharts/mermaid-sources" ]; then
    echo -e "${RED}错误: ../../Process-Flowcharts/mermaid-sources 目录不存在${NC}"
            exit 1
        fi
        
        # 查找所有.mmd文件
        local mmd_files=(../../Process-Flowcharts/mermaid-sources/*.mmd)

        if [ ! -e "${mmd_files[0]}" ]; then
            echo -e "${RED}错误: 在 ../../Process-Flowcharts/mermaid-sources/ 中未找到 .mmd 文件${NC}"
            exit 1
        fi
        
        local count=0
        local success=0
        
        echo -e "${YELLOW}找到 ${#mmd_files[@]} 个 mermaid 文件${NC}"
        echo ""
        
        for mmd_file in "${mmd_files[@]}"; do
            if [ -f "$mmd_file" ]; then
                local basename=$(basename "$mmd_file" .mmd)
                local output_file="../../Process-Flowcharts/generated-flowcharts/${basename}.png"
                
                echo -e "${YELLOW}[$(($count + 1))/${#mmd_files[@]}] 处理: $basename.mmd${NC}"
                
                if node flowchart-generator.js file "$mmd_file" "$output_file"; then
                    echo -e "${GREEN}  ✅ 生成成功: $basename.png${NC}"
                    ((success++))
                else
                    echo -e "${RED}  ❌ 生成失败: $basename.mmd${NC}"
                fi
                
                ((count++))
                echo ""
            fi
        done
        
        echo -e "${BLUE}📊 生成统计:${NC}"
        echo -e "  总文件数: $count"
        echo -e "  成功: ${GREEN}$success${NC}"
        echo -e "  失败: ${RED}$(($count - $success))${NC}"
        
        if [ $success -eq $count ]; then
            echo -e "${GREEN}🎉 所有流程图生成完成！${NC}"
        else
            echo -e "${YELLOW}⚠️  部分流程图生成失败${NC}"
        fi
        
    else
        # 指定了文件名，生成单个文件
        local mmd_file="../../Process-Flowcharts/mermaid-sources/$filename"
        
        # 如果文件名没有.mmd扩展名，自动添加
        if [[ "$filename" != *.mmd ]]; then
            mmd_file="../../Process-Flowcharts/mermaid-sources/${filename}.mmd"
        fi
        
        if [ ! -f "$mmd_file" ]; then
            echo -e "${RED}错误: 文件 '$mmd_file' 不存在${NC}"
            echo -e "${YELLOW}可用的 mermaid 文件:${NC}"
            if [ -d "../../Process-Flowcharts/mermaid-sources" ]; then
                ls -1 ../../Process-Flowcharts/mermaid-sources/*.mmd 2>/dev/null | sed 's|.*/||' || echo "  (无 .mmd 文件)"
            fi
            exit 1
        fi
        
        local basename=$(basename "$mmd_file" .mmd)
        local output_file="../../Process-Flowcharts/generated-flowcharts/${basename}.png"
        
        echo -e "${YELLOW}正在生成流程图: $basename${NC}"
        
        if node flowchart-generator.js file "$mmd_file" "$output_file"; then
            echo -e "${GREEN}✅ 流程图生成成功: $basename.png${NC}"
        else
            echo -e "${RED}❌ 流程图生成失败${NC}"
            exit 1
        fi
    fi
}

# 生成单个PDF
generate_single() {
    local file="$1"
    local full_path=""
    local found_file=false
    
    echo -e "${BLUE}🔍 正在查找文件: $file${NC}"
    
    # 情况1: 如果是绝对路径，直接使用
    if [[ "$file" = /* ]]; then
        full_path="$file"
        echo -e "${YELLOW}  → 检测为绝对路径${NC}"
    
    # 情况2: 如果包含路径分隔符，当作相对路径处理
    elif [[ "$file" == *"/"* ]]; then
        full_path="$file"
        echo -e "${YELLOW}  → 检测为相对路径${NC}"
    
    # 情况3: 纯文件名，智能查找
    else
        echo -e "${YELLOW}  → 检测为文件名，开始智能查找...${NC}"
        
        # 根据文件名模式确定优先查找目录
        if [[ "$file" == *"ABL-SOP-"* ]]; then
            echo -e "${YELLOW}  → SOP文档模式，优先查找SOP目录${NC}"
            # 先检查management目录
            if [[ "$file" == *"Master-Directory"* ]] || [[ "$file" == *"Gap-Analysis"* ]]; then
                full_path="../../Core-Documents/SOP-Standard-Procedures/management/$file"
            else
                full_path="../../Core-Documents/SOP-Standard-Procedures/source/$file"
            fi
        elif [[ "$file" == *"ABL-MMR-"* ]]; then
            echo -e "${YELLOW}  → MMR文档模式，优先查找MMR目录${NC}"
            full_path="../../Core-Documents/MMR-Manufacturing-Records/source/$file"
        elif [[ "$file" == *"ABL-SPEC-"* ]]; then
            echo -e "${YELLOW}  → SPEC文档模式，优先查找SPEC目录${NC}"
            full_path="../../Core-Documents/SPEC-Product-Specifications/source/$file"
        elif [[ "$file" == *"ABL-RMS-"* ]]; then
            echo -e "${YELLOW}  → RMS文档模式，优先查找RMS目录${NC}"
            full_path="../../Core-Documents/RMS-Raw-Material-Specifications/source/$file"
        elif [[ "$file" == *"ABL-REC-"* ]]; then
            echo -e "${YELLOW}  → REC文档模式，优先查找REC目录${NC}"
            full_path="../../Core-Documents/REC-Records-Templates/source/$file"
        else
            echo -e "${YELLOW}  → 未知模式，默认查找SOP目录${NC}"
            full_path="../../Core-Documents/SOP-Standard-Procedures/source/$file"
        fi
    fi
    
    # 验证文件是否存在
    if [ -f "$full_path" ]; then
        found_file=true
        echo -e "${GREEN}  ✅ 找到文件: $full_path${NC}"
    else
        echo -e "${RED}  ❌ 文件不存在: $full_path${NC}"
        
        # 如果是纯文件名且没找到，尝试在其他目录查找
        if [[ "$file" != *"/"* ]]; then
            echo -e "${YELLOW}  → 尝试在其他目录查找...${NC}"
            
            # 如果刚才查找SOP，现在查找MMR、SPEC、RMS和REC
            if [[ "$full_path" == *"SOP-Standard-Procedures"* ]]; then
                alt_path="../../Core-Documents/MMR-Manufacturing-Records/source/$file"
                if [ -f "$alt_path" ]; then
                    full_path="$alt_path"
                    found_file=true
                    echo -e "${GREEN}  ✅ 在MMR目录找到: $alt_path${NC}"
                else
                    alt_path="../../Core-Documents/SPEC-Product-Specifications/source/$file"
                    if [ -f "$alt_path" ]; then
                        full_path="$alt_path"
                        found_file=true
                        echo -e "${GREEN}  ✅ 在SPEC目录找到: $alt_path${NC}"
                    else
                        alt_path="../../Core-Documents/RMS-Raw-Material-Specifications/source/$file"
                        if [ -f "$alt_path" ]; then
                            full_path="$alt_path"
                            found_file=true
                            echo -e "${GREEN}  ✅ 在RMS目录找到: $alt_path${NC}"
                        else
                            alt_path="../../Core-Documents/REC-Records-Templates/source/$file"
                            if [ -f "$alt_path" ]; then
                                full_path="$alt_path"
                                found_file=true
                                echo -e "${GREEN}  ✅ 在REC目录找到: $alt_path${NC}"
                            fi
                        fi
                    fi
                fi
            # 如果刚才查找MMR，现在查找SOP、SPEC、RMS和REC
            elif [[ "$full_path" == *"MMR-Manufacturing-Records"* ]]; then
                alt_path="../../Core-Documents/SOP-Standard-Procedures/source/$file"
                if [ -f "$alt_path" ]; then
                    full_path="$alt_path"
                    found_file=true
                    echo -e "${GREEN}  ✅ 在SOP目录找到: $alt_path${NC}"
                else
                    alt_path="../../Core-Documents/SPEC-Product-Specifications/source/$file"
                    if [ -f "$alt_path" ]; then
                        full_path="$alt_path"
                        found_file=true
                        echo -e "${GREEN}  ✅ 在SPEC目录找到: $alt_path${NC}"
                    else
                        alt_path="../../Core-Documents/RMS-Raw-Material-Specifications/source/$file"
                        if [ -f "$alt_path" ]; then
                            full_path="$alt_path"
                            found_file=true
                            echo -e "${GREEN}  ✅ 在RMS目录找到: $alt_path${NC}"
                        else
                            alt_path="../../Core-Documents/REC-Records-Templates/source/$file"
                            if [ -f "$alt_path" ]; then
                                full_path="$alt_path"
                                found_file=true
                                echo -e "${GREEN}  ✅ 在REC目录找到: $alt_path${NC}"
                            fi
                        fi
                    fi
                fi
            # 如果刚才查找SPEC，现在查找SOP、MMR、RMS和REC
            elif [[ "$full_path" == *"SPEC-Product-Specifications"* ]]; then
                alt_path="../../Core-Documents/SOP-Standard-Procedures/source/$file"
                if [ -f "$alt_path" ]; then
                    full_path="$alt_path"
                    found_file=true
                    echo -e "${GREEN}  ✅ 在SOP目录找到: $alt_path${NC}"
                else
                    alt_path="../../Core-Documents/MMR-Manufacturing-Records/source/$file"
                    if [ -f "$alt_path" ]; then
                        full_path="$alt_path"
                        found_file=true
                        echo -e "${GREEN}  ✅ 在MMR目录找到: $alt_path${NC}"
                    else
                        alt_path="../../Core-Documents/RMS-Raw-Material-Specifications/source/$file"
                        if [ -f "$alt_path" ]; then
                            full_path="$alt_path"
                            found_file=true
                            echo -e "${GREEN}  ✅ 在RMS目录找到: $alt_path${NC}"
                        else
                            alt_path="../../Core-Documents/REC-Records-Templates/source/$file"
                            if [ -f "$alt_path" ]; then
                                full_path="$alt_path"
                                found_file=true
                                echo -e "${GREEN}  ✅ 在REC目录找到: $alt_path${NC}"
                            fi
                        fi
                    fi
                fi
            # 如果刚才查找RMS，现在查找SOP、MMR、SPEC和REC
            elif [[ "$full_path" == *"RMS-Raw-Material-Specifications"* ]]; then
                alt_path="../../Core-Documents/SOP-Standard-Procedures/source/$file"
                if [ -f "$alt_path" ]; then
                    full_path="$alt_path"
                    found_file=true
                    echo -e "${GREEN}  ✅ 在SOP目录找到: $alt_path${NC}"
                else
                    alt_path="../../Core-Documents/MMR-Manufacturing-Records/source/$file"
                    if [ -f "$alt_path" ]; then
                        full_path="$alt_path"
                        found_file=true
                        echo -e "${GREEN}  ✅ 在MMR目录找到: $alt_path${NC}"
                    else
                        alt_path="../../Core-Documents/SPEC-Product-Specifications/source/$file"
                        if [ -f "$alt_path" ]; then
                            full_path="$alt_path"
                            found_file=true
                            echo -e "${GREEN}  ✅ 在SPEC目录找到: $alt_path${NC}"
                        else
                            alt_path="../../Core-Documents/REC-Records-Templates/source/$file"
                            if [ -f "$alt_path" ]; then
                                full_path="$alt_path"
                                found_file=true
                                echo -e "${GREEN}  ✅ 在REC目录找到: $alt_path${NC}"
                            fi
                        fi
                    fi
                fi
            # 如果刚才查找REC，现在查找SOP、MMR、SPEC和RMS
            elif [[ "$full_path" == *"REC-Records-Templates"* ]]; then
                alt_path="../../Core-Documents/SOP-Standard-Procedures/source/$file"
                if [ -f "$alt_path" ]; then
                    full_path="$alt_path"
                    found_file=true
                    echo -e "${GREEN}  ✅ 在SOP目录找到: $alt_path${NC}"
                else
                    alt_path="../../Core-Documents/MMR-Manufacturing-Records/source/$file"
                    if [ -f "$alt_path" ]; then
                        full_path="$alt_path"
                        found_file=true
                        echo -e "${GREEN}  ✅ 在MMR目录找到: $alt_path${NC}"
                    else
                        alt_path="../../Core-Documents/SPEC-Product-Specifications/source/$file"
                        if [ -f "$alt_path" ]; then
                            full_path="$alt_path"
                            found_file=true
                            echo -e "${GREEN}  ✅ 在SPEC目录找到: $alt_path${NC}"
                        else
                            alt_path="../../Core-Documents/RMS-Raw-Material-Specifications/source/$file"
                            if [ -f "$alt_path" ]; then
                                full_path="$alt_path"
                                found_file=true
                                echo -e "${GREEN}  ✅ 在RMS目录找到: $alt_path${NC}"
                            fi
                        fi
                    fi
                fi
            fi
        fi
    fi
    
    # 如果还是没找到，显示详细错误信息并退出
    if [ "$found_file" = false ]; then
        echo -e "${RED}❌ 错误: 无法找到文件 '$file'${NC}"
        echo -e "${YELLOW}📋 建议检查:${NC}"
        echo -e "  1. 文件名是否正确"
        echo -e "  2. 文件是否在以下目录:"
        echo -e "     - ../../Core-Documents/SOP-Standard-Procedures/source/"
        echo -e "     - ../../Core-Documents/MMR-Manufacturing-Records/source/"
        echo -e "     - ../../Core-Documents/SPEC-Product-Specifications/source/"
        echo -e "     - ../../Core-Documents/RMS-Raw-Material-Specifications/source/"
        echo -e "     - ../../Core-Documents/REC-Records-Templates/source/"
        echo -e "  3. 如使用路径，请确认路径正确"
        echo -e "${YELLOW}💡 示例用法:${NC}"
        echo -e "  ./document-tools.sh ABL-SOP-A-001_example.md"
        echo -e "  ./document-tools.sh ../../Core-Documents/SOP-Standard-Procedures/source/ABL-SOP-A-001_example.md"
        echo -e "  ./document-tools.sh /full/path/to/file.md"
        exit 1
    fi
    
    echo -e "${YELLOW}正在生成 PDF: $full_path${NC}"
    
    # 检测文档类型并选择生成器
    if [[ "$(basename "$full_path")" == *"ABL-REC-"* ]]; then
        echo -e "${BLUE}📋 检测到记录文档，使用专用记录生成器${NC}"
        node generate-record-pdf.js "$full_path"
    elif grep -q "!\[.*\](.*)" "$full_path"; then
        echo -e "${BLUE}📸 检测到图片，使用Base64编码处理${NC}"
        node generate-document-pdf.js "$full_path"
    else
        echo -e "${YELLOW}📄 使用标准生成器${NC}"
        node generate-document-pdf.js "$full_path"
    fi
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ PDF 生成成功${NC}"
    else
        echo -e "${RED}❌ PDF 生成失败${NC}"
        exit 1
    fi
}

# 批量生成SOP PDF
generate_sop_batch() {
    echo -e "${YELLOW}正在批量生成所有 SOP 文档 PDF...${NC}"
    
    if [ -d "../../Core-Documents/SOP-Standard-Procedures/source" ]; then
        echo -e "${YELLOW}处理 SOP 文档...${NC}"
        
        # 检查是否有包含图片的文件
        local has_images=false
        for file in "../../Core-Documents/SOP-Standard-Procedures/source"/*.md; do
            if [ -f "$file" ] && grep -q "!\[.*\](.*)" "$file"; then
                has_images=true
                break
            fi
        done
        
        if [ "$has_images" = true ]; then
            echo -e "${BLUE}📸 检测到图片内容，使用Base64处理${NC}"
            # 逐个处理source目录中的文件以支持图片
            for file in "../../Core-Documents/SOP-Standard-Procedures/source"/*.md; do
                if [ -f "$file" ] && [[ "$(basename "$file")" == *"ABL-SOP-"* ]]; then
                    echo -e "${YELLOW}处理: $(basename "$file")${NC}"
                    if grep -q "!\[.*\](.*)" "$file"; then
                        node generate-document-pdf.js "$file"
                    else
                        node generate-document-pdf.js "$file"
                    fi
                fi
            done
            
            # 处理management目录中的文件
            if [ -d "../../Core-Documents/SOP-Standard-Procedures/management" ]; then
                echo -e "${YELLOW}处理 SOP Management 文档...${NC}"
                for file in "../../Core-Documents/SOP-Standard-Procedures/management"/*.md; do
                    if [ -f "$file" ]; then
                        echo -e "${YELLOW}处理: $(basename "$file")${NC}"
                        node generate-document-pdf.js "$file"
                    fi
                done
            fi
        else
            echo -e "${YELLOW}📄 无图片内容，使用标准批量处理${NC}"
            node generate-document-pdf.js batch "../../Core-Documents/SOP-Standard-Procedures/source"
            
            # 处理management目录中的文件
            if [ -d "../../Core-Documents/SOP-Standard-Procedures/management" ]; then
                echo -e "${YELLOW}处理 SOP Management 文档...${NC}"
                for file in "../../Core-Documents/SOP-Standard-Procedures/management"/*.md; do
                    if [ -f "$file" ]; then
                        echo -e "${YELLOW}处理: $(basename "$file")${NC}"
                        node generate-document-pdf.js "$file"
                    fi
                done
            fi
        fi
        
        if [ $? -eq 0 ]; then
            echo -e "${GREEN}✅ SOP 批量生成完成${NC}"
        else
            echo -e "${RED}❌ SOP 批量生成失败${NC}"
            exit 1
        fi
    else
        echo -e "${RED}错误: SOP source 目录不存在${NC}"
        exit 1
    fi
}

# 批量生成MMR PDF
generate_mmr_batch() {
    echo -e "${YELLOW}正在批量生成所有 MMR 文档 PDF...${NC}"
    
    if [ -d "../../Core-Documents/MMR-Manufacturing-Records/source" ]; then
        echo -e "${YELLOW}处理 MMR 文档...${NC}"
        
        # 检查是否有包含图片的文件
        local has_images=false
        for file in "../../Core-Documents/MMR-Manufacturing-Records/source"/*.md; do
            if [ -f "$file" ] && grep -q "!\[.*\](.*)" "$file"; then
                has_images=true
                break
            fi
        done
        
        if [ "$has_images" = true ]; then
            echo -e "${BLUE}📸 检测到图片内容，使用Base64处理${NC}"
            # 逐个处理文件以支持图片
            for file in "../../Core-Documents/MMR-Manufacturing-Records/source"/*.md; do
                if [ -f "$file" ] && [[ "$(basename "$file")" == *"ABL-MMR-"* ]]; then
                    echo -e "${YELLOW}处理: $(basename "$file")${NC}"
                    if grep -q "!\[.*\](.*)" "$file"; then
                        node generate-document-pdf.js "$file"
                    else
                        node generate-document-pdf.js "$file"
                    fi
                fi
            done
        else
            echo -e "${YELLOW}📄 无图片内容，使用标准批量处理${NC}"
            node generate-document-pdf.js batch "../../Core-Documents/MMR-Manufacturing-Records/source"
        fi
        
        if [ $? -eq 0 ]; then
            echo -e "${GREEN}✅ MMR 批量生成完成${NC}"
        else
            echo -e "${RED}❌ MMR 批量生成失败${NC}"
            exit 1
        fi
    else
        echo -e "${RED}错误: MMR source 目录不存在${NC}"
        exit 1
    fi
}

# 批量生成SPEC PDF
generate_spec_batch() {
    echo -e "${YELLOW}正在批量生成所有 SPEC 文档 PDF...${NC}"
    
    if [ -d "../../Core-Documents/SPEC-Product-Specifications/source" ]; then
        echo -e "${YELLOW}处理 SPEC 文档...${NC}"
        
        # 检查是否有包含图片的文件
        local has_images=false
        for file in "../../Core-Documents/SPEC-Product-Specifications/source"/*.md; do
            if [ -f "$file" ] && grep -q "!\[.*\](.*)" "$file"; then
                has_images=true
                break
            fi
        done
        
        if [ "$has_images" = true ]; then
            echo -e "${BLUE}📸 检测到图片内容，使用Base64处理${NC}"
            # 逐个处理文件以支持图片
            for file in "../../Core-Documents/SPEC-Product-Specifications/source"/*.md; do
                if [ -f "$file" ] && [[ "$(basename "$file")" == *"ABL-SPEC-"* ]]; then
                    echo -e "${YELLOW}处理: $(basename "$file")${NC}"
                    if grep -q "!\[.*\](.*)" "$file"; then
                        node generate-document-pdf.js "$file"
                    else
                        node generate-document-pdf.js "$file"
                    fi
                fi
            done
        else
            echo -e "${YELLOW}📄 无图片内容，使用标准批量处理${NC}"
            node generate-document-pdf.js batch "../../Core-Documents/SPEC-Product-Specifications/source"
        fi
        
        if [ $? -eq 0 ]; then
            echo -e "${GREEN}✅ SPEC 批量生成完成${NC}"
        else
            echo -e "${RED}❌ SPEC 批量生成失败${NC}"
            exit 1
        fi
    else
        echo -e "${RED}错误: SPEC source 目录不存在${NC}"
        exit 1
    fi
}

# 批量生成RMS PDF
generate_rms_batch() {
    echo -e "${YELLOW}正在批量生成所有 RMS 文档 PDF...${NC}"
    
    if [ -d "../../Core-Documents/RMS-Raw-Material-Specifications/source" ]; then
        echo -e "${YELLOW}处理 RMS 文档...${NC}"
        
        # 检查是否有包含图片的文件
        local has_images=false
        for file in "../../Core-Documents/RMS-Raw-Material-Specifications/source"/*.md; do
            if [ -f "$file" ] && grep -q "!\[.*\](.*)" "$file"; then
                has_images=true
                break
            fi
        done
        
        if [ "$has_images" = true ]; then
            echo -e "${BLUE}📸 检测到图片内容，使用Base64处理${NC}"
            # 逐个处理文件以支持图片
            for file in "../../Core-Documents/RMS-Raw-Material-Specifications/source"/*.md; do
                if [ -f "$file" ] && [[ "$(basename "$file")" == *"ABL-RMS-"* ]]; then
                    echo -e "${YELLOW}处理: $(basename "$file")${NC}"
                    if grep -q "!\[.*\](.*)" "$file"; then
                        node generate-document-pdf.js "$file"
                    else
                        node generate-document-pdf.js "$file"
                    fi
                fi
            done
        else
            echo -e "${YELLOW}📄 无图片内容，使用标准批量处理${NC}"
            node generate-document-pdf.js batch "../../Core-Documents/RMS-Raw-Material-Specifications/source"
        fi
        
        if [ $? -eq 0 ]; then
            echo -e "${GREEN}✅ RMS 批量生成完成${NC}"
        else
            echo -e "${RED}❌ RMS 批量生成失败${NC}"
            exit 1
        fi
    else
        echo -e "${RED}错误: RMS source 目录不存在${NC}"
        exit 1
    fi
}

# 批量生成REC PDF
generate_rec_batch() {
    echo -e "${YELLOW}正在批量生成所有 REC 记录文档 PDF...${NC}"
    
    if [ -d "../../Core-Documents/REC-Records-Templates/source" ]; then
        echo -e "${YELLOW}处理 REC 记录文档...${NC}"
        
        # 使用专用记录生成器处理所有REC文件
        for file in "../../Core-Documents/REC-Records-Templates/source"/*.md; do
            if [ -f "$file" ] && [[ "$(basename "$file")" == *"ABL-REC-"* ]]; then
                echo -e "${YELLOW}处理: $(basename "$file")${NC}"
                node generate-record-pdf.js "$file"
            fi
        done
        
        if [ $? -eq 0 ]; then
            echo -e "${GREEN}✅ REC 批量生成完成${NC}"
        else
            echo -e "${RED}❌ REC 批量生成失败${NC}"
            exit 1
        fi
    else
        echo -e "${RED}错误: REC source 目录不存在${NC}"
        exit 1
    fi
}

# 批量生成内部文档 PDF (已废弃，使用MGMT替代)
generate_internal_batch() {
    echo -e "${YELLOW}⚠️  INTERNAL选项已废弃，请使用MGMT选项${NC}"
    echo -e "${YELLOW}正在自动重定向到MGMT处理...${NC}"
    generate_mgmt_batch
}

# 批量生成SOP管理文档 PDF
generate_mgmt_batch() {
    echo -e "${YELLOW}正在批量生成所有 SOP 管理文档 PDF...${NC}"
    
    if [ -d "../../Core-Documents/SOP-Standard-Procedures/management" ]; then
        echo -e "${YELLOW}处理 SOP Management 文档...${NC}"
        
        local processed=0
        local failed=0
        
        for file in "../../Core-Documents/SOP-Standard-Procedures/management"/*.md; do
            if [ -f "$file" ]; then
                echo -e "${YELLOW}处理: $(basename "$file")${NC}"
                if node generate-document-pdf.js "$file"; then
                    echo -e "${GREEN}  ✅ 生成成功: $(basename "$file")${NC}"
                    ((processed++))
                else
                    echo -e "${RED}  ❌ 生成失败: $(basename "$file")${NC}"
                    ((failed++))
                fi
            else
                echo -e "${YELLOW}⚠️  文件不存在: $(basename "$file")${NC}"
            fi
        done
        
        echo -e "${BLUE}📊 SOP 管理文档生成统计:${NC}"
        echo -e "  成功: ${GREEN}$processed${NC}"
        echo -e "  失败: ${RED}$failed${NC}"
        
        if [ $failed -eq 0 ]; then
            echo -e "${GREEN}✅ SOP 管理文档批量生成完成${NC}"
        else
            echo -e "${YELLOW}⚠️  部分 SOP 管理文档生成失败${NC}"
        fi
    else
        echo -e "${RED}错误: SOP management 目录不存在${NC}"
        exit 1
    fi
}

# HTML生成功能
generate_html() {
    local target="$1"
    
    echo -e "${YELLOW}正在生成HTML文档...${NC}"
    
    # 调用HTML生成脚本
    if [ -f "./generate-html.sh" ]; then
        if [ -z "$target" ]; then
            # 无参数，生成所有REC文档HTML
            echo -e "${YELLOW}生成所有REC文档的交互式HTML表单...${NC}"
            ./generate-html.sh
        elif [ "$target" = "REC" ] || [ "$target" = "rec" ]; then
            # 生成REC文档HTML
            echo -e "${YELLOW}生成REC文档HTML...${NC}"
            ./generate-html.sh REC
        elif [[ "$target" == *.md ]]; then
            # 生成指定文档HTML
            echo -e "${YELLOW}生成指定文档HTML: $target${NC}"
            ./generate-html.sh "$target"
        else
            echo -e "${RED}❌ 不支持的HTML生成目标: $target${NC}"
            echo -e "${YELLOW}支持的选项: REC, <filename.md>${NC}"
            exit 1
        fi
        
        if [ $? -eq 0 ]; then
            echo -e "${GREEN}✅ HTML生成完成${NC}"
        else
            echo -e "${RED}❌ HTML生成失败${NC}"
            exit 1
        fi
    else
        echo -e "${RED}❌ HTML生成脚本不存在: ./generate-html.sh${NC}"
        exit 1
    fi
}

# 批量生成所有PDF
generate_batch() {
    echo -e "${YELLOW}正在批量生成所有文档 PDF...${NC}"
    
    # 处理SOP文档
    if [ -d "../../Core-Documents/SOP-Standard-Procedures/source" ]; then
        echo -e "${YELLOW}处理 SOP 文档...${NC}"
        generate_sop_batch
    fi
    
    # 处理MMR文档
    if [ -d "../../Core-Documents/MMR-Manufacturing-Records/source" ]; then
        echo -e "${YELLOW}处理 MMR 文档...${NC}"
        generate_mmr_batch
    fi
    
    # 处理SPEC文档
    if [ -d "../../Core-Documents/SPEC-Product-Specifications/source" ]; then
        echo -e "${YELLOW}处理 SPEC 文档...${NC}"
        generate_spec_batch
    fi
    
    # 处理RMS文档
    if [ -d "../../Core-Documents/RMS-Raw-Material-Specifications/source" ]; then
        echo -e "${YELLOW}处理 RMS 文档...${NC}"
        generate_rms_batch
    fi
    
    # 处理REC文档
    if [ -d "../../Core-Documents/REC-Records-Templates/source" ]; then
        echo -e "${YELLOW}处理 REC 记录文档...${NC}"
        generate_rec_batch
    fi
    
    echo -e "${GREEN}✅ 批量生成完成${NC}"
}

# 主逻辑
main() {
    # 检查依赖
    check_dependencies
    
    if [ $# -eq 0 ]; then
        # 没有参数 - 批量生成所有
        generate_batch
    elif [ "$1" = "-h" ] || [ "$1" = "--help" ]; then
        # 显示帮助
        show_help
    elif [ "$1" = "flowchart" ]; then
        # 流程图操作
        generate_flowchart "$2"
    elif [ "$1" = "html" ]; then
        # HTML生成操作
        generate_html "$2"
    elif [ "$1" = "SOP" ] || [ "$1" = "sop" ]; then
        # 批量生成SOP
        generate_sop_batch
    elif [ "$1" = "MMR" ] || [ "$1" = "mmr" ]; then
        # 批量生成MMR
        generate_mmr_batch
    elif [ "$1" = "SPEC" ] || [ "$1" = "spec" ]; then
        # 批量生成SPEC
        generate_spec_batch
    elif [ "$1" = "RMS" ] || [ "$1" = "rms" ]; then
        # 批量生成RMS
        generate_rms_batch
    elif [ "$1" = "REC" ] || [ "$1" = "rec" ]; then
        # 批量生成REC
        generate_rec_batch
    elif [ "$1" = "MGMT" ] || [ "$1" = "mgmt" ]; then
        # 批量生成SOP管理文档
        generate_mgmt_batch
    elif [ "$1" = "INTERNAL" ] || [ "$1" = "internal" ]; then
        # 批量生成内部文档
        generate_internal_batch
    else
        # 生成指定文件
        generate_single "$1"
    fi
}

# 运行主函数
main "$@" 