const puppeteer = require('puppeteer');
const fs = require('fs');
const path = require('path');
const config = require('./puppeteer.config.js');

// 检测文档类型
function detectDocumentType(fileName) {
  const baseName = path.basename(fileName, '.md');
  
  // 检测内部文档：以ABL-SOP-开头但没有紧接着的单独字母-数字模式
  if (baseName.match(/^ABL-SOP-[^A-Z]-/) || baseName.match(/^ABL-SOP-[A-Za-z]{2,}/)) {
    return 'internal';
  }
  
  if (baseName.match(/ABL-SOP-[A-Z]-\d+/)) {
    return 'sop';
  } else if (baseName.match(/ABL-MMR-\d+/)) {
    return 'mmr';
  } else if (baseName.match(/ABL-SPEC-\d+/)) {
    return 'spec';
  } else if (baseName.match(/ABL-RMS-\d+/)) {
    return 'rms';
  }
  
  return 'unknown';
}

// 从文件名解析文档信息
function parseDocumentFileName(fileName) {
  const baseName = path.basename(fileName, '.md');
  const docType = detectDocumentType(fileName);
  
  if (docType === 'sop') {
    const parts = baseName.split('_');
    const docNumber = parts[0];
    const docTitleParts = parts.slice(1);
    
    const docTitle = docTitleParts
      .map(word => {
        if (word.toLowerCase() === 'sop') return 'SOP';
        if (word.toLowerCase() === 'cgmp') return 'cGMP';
        if (word.toLowerCase() === 'qa') return 'QA';
        if (word.toLowerCase() === 'qc') return 'QC';
        return word.charAt(0).toUpperCase() + word.slice(1).toLowerCase();
      })
      .join(' ');
    
    return { docType: 'sop', docNumber, docTitle };
  } else if (docType === 'mmr') {
    const parts = baseName.split('-');
    const mmrNumber = parts.slice(0, 3).join('-');
    const productName = parts.slice(3).join('-').replace(/-/g, ' ');
    const mmrTitle = `Master Manufacturing Record - ${productName}`;
    
    return { docType: 'mmr', docNumber: mmrNumber, docTitle: mmrTitle, productName };
  } else if (docType === 'spec') {
    const parts = baseName.split('-');
    const specNumber = parts.slice(0, 3).join('-');
    const productName = parts.slice(3).join('-').replace(/-/g, ' ');
    const specTitle = `Product Specification - ${productName}`;
    
    return { docType: 'spec', docNumber: specNumber, docTitle: specTitle, productName };
  } else if (docType === 'rms') {
    const parts = baseName.split('-');
    const rmsNumber = parts.slice(0, 3).join('-');
    const materialName = parts.slice(3).join('-').replace(/-/g, ' ');
    const rmsTitle = `Raw Material Specification - ${materialName}`;
    
    return { docType: 'rms', docNumber: rmsNumber, docTitle: rmsTitle, materialName };
  } else if (docType === 'internal') {
    // 处理内部文档（如Master-Directory, Gap-Analysis等）
    const title = baseName.replace(/^ABL-SOP-/, '').replace(/-/g, ' ')
      .split(' ')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .join(' ');
    
    return { docType: 'internal', docNumber: baseName, docTitle: title };
  }
  
  return { docType: 'unknown', docNumber: baseName, docTitle: baseName };
}

// 从YAML frontmatter提取信息
function extractYAMLInfo(markdownContent) {
  const yamlMatch = markdownContent.match(/^---\n([\s\S]*?)\n---/);
  if (!yamlMatch) return {};
  
  const yamlContent = yamlMatch[1];
  const info = {};
  
  const lines = yamlContent.split('\n');
  for (const line of lines) {
    if (line.trim().startsWith('#') || !line.includes(':') || line.trim() === '') continue;
    
    const colonIndex = line.indexOf(':');
    if (colonIndex === -1) continue;
    
    const key = line.substring(0, colonIndex).trim();
    const value = line.substring(colonIndex + 1).trim().replace(/['"]/g, '');
    
    if (key && value) {
      switch (key) {
        case 'doc_title': info.docTitle = value; break;
        case 'doc_number': info.docNumber = value; break;
        case 'revision': info.revision = value; break;
        case 'effective_date': info.effectiveDate = value; break;
        case 'company_name': info.companyName = value; break;
        case 'confidentiality_level': info.confidentialityLevel = value; break;
        case 'sop_title': if (!info.docTitle) info.docTitle = value; break;
        case 'sop_number': if (!info.docNumber) info.docNumber = value; break;
      }
    }
  }
  
  return info;
}

// 将图片转换为Base64编码
function imageToBase64(imagePath) {
  try {
    if (!fs.existsSync(imagePath)) {
      console.warn(`⚠️  图片文件不存在: ${imagePath}`);
      return null;
    }
    
    const imageBuffer = fs.readFileSync(imagePath);
    const ext = path.extname(imagePath).toLowerCase();
    const mimeTypes = {
      '.png': 'image/png',
      '.jpg': 'image/jpeg',
      '.jpeg': 'image/jpeg',
      '.gif': 'image/gif',
      '.svg': 'image/svg+xml',
      '.webp': 'image/webp'
    };
    
    const mimeType = mimeTypes[ext] || 'image/png';
    return `data:${mimeType};base64,${imageBuffer.toString('base64')}`;
  } catch (error) {
    console.error(`❌ 转换图片失败: ${imagePath}`, error.message);
    return null;
  }
}

// Markdown到HTML转换，支持Base64图片处理
function convertMarkdownToHTML(markdown, markdownFilePath) {
  // 移除YAML frontmatter
  let html = markdown.replace(/^---\n[\s\S]*?\n---\n/, '');
  
  // 获取Markdown文件所在目录，用于解析相对路径
  const markdownDir = path.dirname(path.resolve(markdownFilePath));
  
  // 处理图片 - 转换为Base64
  html = html.replace(/!\[([^\]]*)\]\(([^)]+)\)/g, (match, alt, src) => {
    console.log(`🖼️  处理图片: ${src}`);
    
    // 如果是网络URL，直接使用
    if (src.startsWith('http://') || src.startsWith('https://')) {
      return `<img src="${src}" alt="${alt}" style="max-width: 100%; height: auto; margin: 10pt 0;">`;
    }
    
    // 处理相对路径
    let imagePath;
    if (path.isAbsolute(src)) {
      imagePath = src;
    } else {
      imagePath = path.resolve(markdownDir, src);
    }
    
    // 检查文件是否存在
    if (!fs.existsSync(imagePath)) {
      console.warn(`⚠️  图片文件不存在: ${imagePath}`);
      return `<p style="color: red; border: 1px solid red; padding: 10pt; margin: 10pt 0;">❌ 图片未找到: ${src}</p>`;
    }
    
    // 转换为Base64
    const imgSrc = imageToBase64(imagePath);
    if (!imgSrc) {
      return `<p style="color: red; border: 1px solid red; padding: 10pt; margin: 10pt 0;">❌ 图片转换失败: ${src}</p>`;
    }
    
    return `<img src="${imgSrc}" alt="${alt}" style="max-width: 100%; height: auto; margin: 10pt 0; border: 1px solid #ddd; padding: 5pt;">`;
  });
  
  // 处理Markdown链接
  html = html.replace(/\[([^\]]+)\]\([^)]+\)/g, '$1');
  
  // 处理文本格式
  html = html.replace(/\*\*([^*]+)\*\*/g, '<strong>$1</strong>');
  html = html.replace(/\*([^*]+)\*/g, '<em>$1</em>');
  
  // 处理标题
  html = html.replace(/^#### (.*$)/gm, '<h4>$1</h4>');
  html = html.replace(/^### (.*$)/gm, '<h3>$1</h3>');
  html = html.replace(/^## (.*$)/gm, '<h2>$1</h2>');
  html = html.replace(/^# (.*$)/gm, '<h1>$1</h1>');
  
  // 处理列表和表格，同时标记appendix部分
  const lines = html.split('\n');
  const processedLines = [];
  let inList = false;
  let listLevel = 0;
  let inTable = false;
  let inAppendix = false;
  
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i];
    
    // 检测appendix部分
    if (line.match(/<h1>.*[Aa]ppendix.*<\/h1>/)) {
      if (inAppendix) {
        processedLines.push('</div>');
      }
      inAppendix = true;
      processedLines.push('<div class="appendix-section">');
      processedLines.push(line);
      continue;
    }
    // 检测新的主要章节，结束appendix
    else if (line.match(/<h1>(?!.*[Aa]ppendix).*<\/h1>/)) {
      if (inAppendix) {
        processedLines.push('</div>');
        inAppendix = false;
      }
      processedLines.push(line);
      continue;
    }
    
    const listMatch = line.match(/^(\s*)- (.*)$/);
    
    if (listMatch) {
      const indent = listMatch[1].length;
      const content = listMatch[2];
      const currentLevel = Math.floor(indent / 3);
      
      if (inTable) {
        processedLines.push('</table>');
        inTable = false;
      }
      
      if (!inList) {
        processedLines.push('<ul>');
        inList = true;
        listLevel = currentLevel;
      } else if (currentLevel > listLevel) {
        processedLines.push('<ul>');
        listLevel = currentLevel;
      } else if (currentLevel < listLevel) {
        for (let j = listLevel; j > currentLevel; j--) {
          processedLines.push('</ul>');
        }
        listLevel = currentLevel;
      }
      
      processedLines.push(`<li>${content}</li>`);
    } else {
      if (inList) {
        for (let j = 0; j <= listLevel; j++) {
          processedLines.push('</ul>');
        }
        inList = false;
        listLevel = 0;
      }
      
      // 表格处理
      if (line.includes('|') && line.trim() !== '') {
        const cells = line.split('|').map(cell => cell.trim()).filter(cell => cell !== '');
        
        if (cells.every(cell => cell.match(/^-+$/))) {
          // 分隔行，关闭thead，开始tbody
          if (inTable) {
            processedLines.push('</thead>');
            processedLines.push('<tbody>');
          }
          continue;
        }
        
        if (cells.length > 0) {
          if (!inTable) {
            processedLines.push('<table border="1" style="border-collapse: collapse; width: 100%;">');
            processedLines.push('<thead>');
            inTable = true;
            const row = cells.map(cell => 
              `<th style="padding: 8px; border: 1px solid #333;">${cell}</th>`
            ).join('');
            processedLines.push(`<tr>${row}</tr>`);
          } else {
            const row = cells.map(cell => 
              `<td style="padding: 8px; border: 1px solid #333;">${cell}</td>`
            ).join('');
            processedLines.push(`<tr>${row}</tr>`);
          }
        }
      } else {
        if (inTable) {
          // 检查是否需要关闭tbody或thead
          const lastLine = processedLines[processedLines.length - 1];
          if (lastLine && lastLine.includes('<tr>')) {
            // 如果最后一行是表格行，需要关闭相应的标签
            if (lastLine.includes('<th')) {
              processedLines.push('</thead>');
            } else {
              processedLines.push('</tbody>');
            }
          }
          processedLines.push('</table>');
          inTable = false;
        }
        
        if (line.match(/^-{3,}$/)) {
          processedLines.push('<hr style="border: none; border-top: 1px solid #333; margin: 20pt 0;">');
        } else if (line.trim() !== '' && !line.match(/^#{1,4}\s/)) {
          processedLines.push(`<p>${line}</p>`);
        } else if (line.trim() !== '') {
          processedLines.push(line);
        }
      }
    }
  }
  
  // 关闭未关闭的标签
  if (inList) {
    for (let j = 0; j <= listLevel; j++) {
      processedLines.push('</ul>');
    }
  }
  if (inTable) {
    // 检查是否需要关闭tbody或thead
    const lastLine = processedLines[processedLines.length - 1];
    if (lastLine && lastLine.includes('<tr>')) {
      // 如果最后一行是表格行，需要关闭相应的标签
      if (lastLine.includes('<th')) {
        processedLines.push('</thead>');
      } else {
        processedLines.push('</tbody>');
      }
    }
    processedLines.push('</table>');
  }
  if (inAppendix) {
    processedLines.push('</div>');
  }
  
  html = processedLines.join('\n');
  html = html.replace(/<p><\/p>/g, '');
  html = html.replace(/\n\s*\n/g, '\n');
  
  return `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="UTF-8">
      <title>Document</title>
      <style>
        body { 
          font-family: 'Times New Roman', serif; 
          font-size: 12pt; 
          line-height: 1.3; 
          margin: 0; 
          padding: 15px; 
        }
        h1 { font-size: 16pt; margin: 12pt 0 8pt 0; }
        h2 { font-size: 14pt; margin: 8pt 0 6pt 0; }
        h3 { font-size: 12pt; margin: 7pt 0 5pt 0; }
        h4 { font-size: 11pt; margin: 6pt 0 4pt 0; }
        ul { list-style-type: none; padding-left: 18pt; margin: 8pt 0 5pt 0; }
        li { margin: 5pt 0; line-height: 1.3; }
        p { margin: 8pt 0; line-height: 1.3; }
        table { 
          width: 100%; 
          border-collapse: collapse; 
          margin: 10pt 0; 
          page-break-inside: auto;
          break-inside: auto;
        }
        th, td { 
          padding: 8pt; 
          border: 1px solid #333; 
          text-align: left; 
          page-break-inside: avoid;
          break-inside: avoid;
        }
        th { 
          background-color: #f0f0f0; 
          font-weight: bold;
          page-break-after: avoid;
          break-after: avoid;
        }
        td { background-color: white; }
        tr { 
          page-break-inside: avoid; 
          break-inside: avoid;
          page-break-after: auto;
          break-after: auto;
        }
        /* 表格标题行在新页面重复显示 */
        thead { 
          display: table-header-group;
          page-break-inside: avoid;
          break-inside: avoid;
        }
        tbody { 
          display: table-row-group;
          page-break-inside: auto;
          break-inside: auto;
        }
        /* 避免表格标题后立即分页 */
        thead tr:last-child {
          page-break-after: avoid;
          break-after: avoid;
        }
        /* 确保至少有几行数据和标题在同一页 */
        tbody tr:nth-child(-n+2) {
          page-break-before: avoid;
          break-before: avoid;
        }
        img { 
          max-width: 100%; 
          height: auto; 
          margin: 10pt 0; 
          display: block;
          border: 1px solid #ddd;
          padding: 5pt;
          page-break-inside: avoid;
          break-inside: avoid;
          page-break-before: auto;
          break-before: auto;
        }
        /* Appendix section keeps together with images */
        .appendix-section {
          page-break-inside: avoid;
          break-inside: avoid;
          page-break-before: auto;
          break-before: auto;
        }
        .appendix-section h1,
        .appendix-section h2,
        .appendix-section h3 {
          page-break-after: avoid;
          break-after: avoid;
        }
        .appendix-section p + img,
        .appendix-section h1 + h2,
        .appendix-section h2 + img,
        .appendix-section h3 + img {
          page-break-before: avoid;
          break-before: avoid;
        }
      </style>
    </head>
    <body>
      ${html}
    </body>
    </html>
  `;
}

// 简化的PDF生成函数（只支持Base64图片）
async function generateDocumentPDF(markdownFilePath, outputDir = null) {
  try {
    // 读取Markdown文件
    const markdownContent = fs.readFileSync(markdownFilePath, 'utf8');
    
    // 检查是否包含图片
    const hasImages = /!\[([^\]]*)\]\(([^)]+)\)/.test(markdownContent);
    
    // 解析文档信息
    const fileInfo = parseDocumentFileName(markdownFilePath);
    const docType = fileInfo.docType;
    
    if (!outputDir) {
      if (docType === 'mmr') {
        outputDir = '../../Core-Documents/MMR-Manufacturing-Records/published';
      } else if (docType === 'spec') {
        outputDir = '../../Core-Documents/SPEC-Product-Specifications/published';
      } else if (docType === 'rms') {
        outputDir = '../../Core-Documents/RMS-Raw-Material-Specifications/published';
      } else if (docType === 'internal') {
        // 检查文件是否来自management目录
        if (markdownFilePath.includes('/management/')) {
          outputDir = '../../Core-Documents/SOP-Standard-Procedures/management';
        } else {
          outputDir = '../../Core-Documents/SOP-Standard-Procedures/published';
        }
      } else {
        outputDir = '../../Core-Documents/SOP-Standard-Procedures/published';
      }
    }
    
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true });
    }
    
    const yamlInfo = extractYAMLInfo(markdownContent);
    const docInfo = {
      companyName: yamlInfo.companyName || 'American Best Life Inc',
      docTitle: yamlInfo.docTitle || fileInfo.docTitle,
      docNumber: yamlInfo.docNumber || fileInfo.docNumber,
      revision: yamlInfo.revision || '00',
      effectiveDate: yamlInfo.effectiveDate || '2025-06-01',
      confidentialityLevel: yamlInfo.confidentialityLevel || 'Confidential',
      docType: docType
    };
    
    console.log(`📋 ${docType.toUpperCase()} 文档信息:`);
    console.log(`  标题: ${docInfo.docTitle}`);
    console.log(`  编号: ${docInfo.docNumber}`);
    console.log(`  版本: Rev.${docInfo.revision}`);
    if (hasImages) {
      console.log(`  🖼️  包含图片，使用Base64编码处理`);
    }
    
    const baseName = path.basename(markdownFilePath, '.md');
    const outputFileName = `${baseName}.pdf`;
    const outputPath = path.join(outputDir, outputFileName);
    
    // 为内部文档使用不同的配置（无header/footer）
    let pdfConfig;
    if (docType === 'internal') {
      console.log(`📋 内部文档，将生成无页眉页脚的PDF`);
      pdfConfig = {
        format: 'A4',
        margin: {
          top: '2cm',
          bottom: '2cm',
          left: '1.2cm',
          right: '1.2cm'
        },
        displayHeaderFooter: false,
        headerTemplate: '',
        footerTemplate: '',
        printBackground: true,
        preferCSSPageSize: false,
        landscape: false,
        scale: 1
      };
    } else {
      pdfConfig = config.getConfigWithVariables({
        COMPANY_NAME: docInfo.companyName,
        SOP_TITLE: docInfo.docTitle,
        SOP_NUMBER: docInfo.docNumber,
        REVISION: docInfo.revision,
        EFFECTIVE_DATE: docInfo.effectiveDate,
        CONFIDENTIALITY_LEVEL: docInfo.confidentialityLevel
      });
    }
    
    // 启动浏览器
    const browser = await puppeteer.launch({
      headless: true,
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });
    const page = await browser.newPage();
    
    // 转换Markdown为HTML，处理图片
    const htmlContent = convertMarkdownToHTML(markdownContent, markdownFilePath);
    
    await page.setContent(htmlContent, { waitUntil: 'networkidle0' });
    
    // 等待图片加载完成
    if (hasImages) {
      console.log('⏳ 等待图片加载完成...');
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // 检查图片是否加载成功
      const imageLoadStatus = await page.evaluate(() => {
        const images = document.querySelectorAll('img');
        const results = [];
        images.forEach((img, index) => {
          results.push({
            index,
            loaded: img.complete && img.naturalHeight !== 0,
            width: img.naturalWidth,
            height: img.naturalHeight
          });
        });
        return results;
      });
      
      console.log('🖼️  图片加载状态:');
      imageLoadStatus.forEach(img => {
        const status = img.loaded ? '✅' : '❌';
        console.log(`  ${status} 图片 ${img.index + 1}: ${img.width}x${img.height}`);
      });
    }
    
    // 生成PDF
    await page.pdf({
      path: outputPath,
      format: pdfConfig.format,
      margin: pdfConfig.margin,
      displayHeaderFooter: pdfConfig.displayHeaderFooter,
      headerTemplate: pdfConfig.headerTemplate,
      footerTemplate: pdfConfig.footerTemplate,
      printBackground: pdfConfig.printBackground,
      preferCSSPageSize: pdfConfig.preferCSSPageSize,
      landscape: pdfConfig.landscape,
      scale: pdfConfig.scale
    });
    
    await browser.close();
    
    console.log(`✅ ${docType.toUpperCase()} PDF 已生成: ${outputPath}`);
    return outputPath;
    
  } catch (error) {
    console.error('❌ 生成PDF时出错:', error);
    throw error;
  }
}

// 批量处理多个文档文件
async function batchGenerateDocumentPDFs(inputDir, outputDir = null) {
  const allFiles = fs.readdirSync(inputDir)
    .filter(file => file.endsWith('.md') && 
                   (file.includes('ABL-SOP-') || file.includes('ABL-MMR-') || file.includes('ABL-SPEC-') || file.includes('ABL-RMS-')) && 
                   !file.includes('Master-Directory'))
    .map(file => path.join(inputDir, file));
  
  const sopFiles = allFiles.filter(file => detectDocumentType(file) === 'sop');
  const mmrFiles = allFiles.filter(file => detectDocumentType(file) === 'mmr');
  const specFiles = allFiles.filter(file => detectDocumentType(file) === 'spec');
  const rmsFiles = allFiles.filter(file => detectDocumentType(file) === 'rms');
  
  console.log(`🔍 找到 ${sopFiles.length} 个 SOP 文件，${mmrFiles.length} 个 MMR 文件，${specFiles.length} 个 SPEC 文件，${rmsFiles.length} 个 RMS 文件`);
  
  // 处理SOP文件
  if (sopFiles.length > 0) {
    console.log(`\n📋 处理 SOP 文档...`);
    const sopOutputDir = outputDir || '../../Core-Documents/SOP-Standard-Procedures/published';
    for (const file of sopFiles) {
      console.log(`\n📝 处理 SOP: ${path.basename(file)}`);
      try {
        await generateDocumentPDF(file, sopOutputDir);
      } catch (error) {
        console.error(`❌ 处理文件 ${file} 时出错:`, error.message);
      }
    }
  }
  
  // 处理MMR文件
  if (mmrFiles.length > 0) {
    console.log(`\n📋 处理 MMR 文档...`);
    const mmrOutputDir = outputDir || '../../Core-Documents/MMR-Manufacturing-Records/published';
    for (const file of mmrFiles) {
      console.log(`\n📝 处理 MMR: ${path.basename(file)}`);
      try {
        await generateDocumentPDF(file, mmrOutputDir);
      } catch (error) {
        console.error(`❌ 处理文件 ${file} 时出错:`, error.message);
      }
    }
  }
  
  // 处理SPEC文件
  if (specFiles.length > 0) {
    console.log(`\n📋 处理 SPEC 文档...`);
    const specOutputDir = outputDir || '../../Core-Documents/SPEC-Product-Specifications/published';
    for (const file of specFiles) {
      console.log(`\n📝 处理 SPEC: ${path.basename(file)}`);
      try {
        await generateDocumentPDF(file, specOutputDir);
      } catch (error) {
        console.error(`❌ 处理文件 ${file} 时出错:`, error.message);
      }
    }
  }
  
  // 处理RMS文件
  if (rmsFiles.length > 0) {
    console.log(`\n📋 处理 RMS 文档...`);
    const rmsOutputDir = outputDir || '../../Core-Documents/RMS-Raw-Material-Specifications/published';
    for (const file of rmsFiles) {
      console.log(`\n📝 处理 RMS: ${path.basename(file)}`);
      try {
        await generateDocumentPDF(file, rmsOutputDir);
      } catch (error) {
        console.error(`❌ 处理文件 ${file} 时出错:`, error.message);
      }
    }
  }
  
  console.log(`\n🎉 批量处理完成！`);
  if (sopFiles.length > 0) console.log(`📁 SOP PDF 保存在: ${outputDir || '../../Core-Documents/SOP-Standard-Procedures/published'}`);
  if (mmrFiles.length > 0) console.log(`📁 MMR PDF 保存在: ${outputDir || '../../Core-Documents/MMR-Manufacturing-Records/published'}`);
  if (specFiles.length > 0) console.log(`📁 SPEC PDF 保存在: ${outputDir || '../../Core-Documents/SPEC-Product-Specifications/published'}`);
  if (rmsFiles.length > 0) console.log(`📁 RMS PDF 保存在: ${outputDir || '../../Core-Documents/RMS-Raw-Material-Specifications/published'}`);
}

// 导出函数
module.exports = { 
  generateDocumentPDF,
  batchGenerateDocumentPDFs,
  parseDocumentFileName,
  detectDocumentType,
  extractYAMLInfo,
  imageToBase64,
  convertMarkdownToHTML
};

// 命令行使用
if (require.main === module) {
  const args = process.argv.slice(2);
  
  if (args.length === 0) {
    console.log('文档 PDF 生成工具 (Base64图片支持)');
    console.log('====================================');
    console.log('');
    console.log('用法:');
    console.log('  node generate-document-pdf.js <markdown文件> [输出目录]');
    console.log('  node generate-document-pdf.js batch <输入目录> [输出目录]');
    console.log('');
    console.log('特性:');
    console.log('  - 自动检测并处理图片（Base64编码）');
    console.log('  - 支持SOP和MMR文档格式');
    console.log('  - 保持原有专业页眉页脚格式');
    console.log('');
    console.log('示例:');
    console.log('  node generate-document-pdf.js document-with-images.md');
    console.log('  node generate-document-pdf.js batch ../SOP\\ markdown\\ file');
    process.exit(1);
  }
  
  if (args[0] === 'batch') {
    const inputDir = args[1] || './';
    const outputDir = args[2] || null;
    batchGenerateDocumentPDFs(inputDir, outputDir).catch(console.error);
  } else {
    const inputFile = args[0];
    const outputDir = args[1] || null;
    generateDocumentPDF(inputFile, outputDir).catch(console.error);
  }
} 