# ABL Document Support Tools

ABL文档系统支持工具集 - 用于自动化文档生成、管理和处理的专业工具套件。

## 工具概览

### 📊 Document Dashboard
**目录**: `document-dashboard/`  
**功能**: 可视化Web界面，用于管理ABL文档系统操作

- **启动方式**: 双击根目录 `Launch-Dashboard.command` 或 `npm start`
- **访问地址**: http://localhost:3000
- **主要功能**:
  - 🚀 一键生成所有或特定类型文档
  - 📁 实时文件浏览器，双击打开PDF/HTML文件
  - 📟 彩色编码的实时控制台反馈
  - 📊 实时文档统计和分类
  - 🔄 WebSocket自动刷新
  - ⌨️ 快捷键支持

**技术栈**: Node.js + Express + WebSocket + Chokidar

### 🔧 PDF Generators
**目录**: `pdf-generators/`  
**功能**: 核心PDF和HTML生成引擎

**主要工具**:
- `generate-document-pdf.js` - 文档PDF生成器
- `generate-record-pdf.js` - 记录PDF生成器
- `generate-record-html.js` - 交互式HTML表单生成器
- `flowchart-generator.js` - Mermaid流程图生成器
- `document-tools.sh` - 批量处理脚本
- `puppeteer.config.js` - PDF渲染配置

**支持格式**: 
- 输入: Markdown + YAML frontmatter
- 输出: 高质量PDF + 交互式HTML
- 图片: PNG/JPG/SVG自动处理，Base64编码

### 📈 Flowchart Tools
**目录**: `flowchart-tools/`  
**功能**: 流程图生成和管理

**主要功能**:
- Mermaid DSL转PNG流程图
- 自动化流程图批量生成
- 高分辨率矢量图形输出

## 使用方法

### 快速启动
```bash
# 启动可视化Dashboard（推荐）
双击根目录的 Launch-Dashboard.command

# 或者手动启动
cd document-dashboard
npm install
npm start
```

### 命令行使用
```bash
# 生成所有文档
cd pdf-generators
./document-tools.sh

# 生成特定类型
./document-tools.sh html      # 生成HTML
./document-tools.sh flowchart # 生成流程图

# 单独生成
node generate-document-pdf.js ../path/to/source.md
```

### 脚本集成
Dashboard自动调用根目录的脚本：
- `generate-pdf.sh` - 主PDF生成脚本
- `generate-html.sh` - HTML生成脚本

## 技术细节

### 文档处理流程
1. **扫描源文件**: 自动检测Markdown文件和YAML元数据
2. **内容处理**: 图片Base64编码，流程图渲染
3. **模板应用**: 应用企业级PDF模板和HTML样式
4. **输出生成**: 高质量PDF和交互式HTML表单
5. **文件监控**: 实时监控变化并自动刷新

### 路径配置
Dashboard通过以下路径配置管理文档：
```javascript
PROJECT_ROOT = path.resolve(__dirname, '../../../..')
PUBLISHED_PATHS = {
    SOP: 'ABL-Document-System/Core-Documents/SOP-Standard-Procedures/published',
    MMR: 'ABL-Document-System/Core-Documents/MMR-Manufacturing-Records/published',
    RMS: 'ABL-Document-System/Core-Documents/RMS-Raw-Material-Specifications/published',
    SPEC: 'ABL-Document-System/Core-Documents/SPEC-Product-Specifications/published',
    REC: 'ABL-Document-System/Core-Documents/REC-Records-Templates/published'
};
```

### 集成点
- **文件监控**: Chokidar监控published目录变化
- **脚本执行**: 子进程调用现有生成脚本
- **实时通信**: WebSocket推送状态更新
- **错误处理**: 完整的错误捕获和用户反馈

## 故障排除

### 常见问题
1. **端口占用**: 
   ```bash
   PORT=3001 npm start
   ```

2. **脚本权限**:
   ```bash
   chmod +x ../../../generate-pdf.sh
   chmod +x ../../../generate-html.sh
   ```

3. **依赖问题**:
   ```bash
   cd pdf-generators && npm install
   cd ../document-dashboard && npm install
   ```

4. **WebSocket连接**:
   - 自动降级到轮询模式
   - 检查防火墙设置

### 性能优化
- 文件监控使用防抖动机制
- PDF生成使用缓存和增量更新
- WebSocket连接池管理
- 内存使用监控

## 🛡️ 开发规范

### 代码质量参考
**位置**: `development-guidelines/common-errors.md`

基本的编程错误预防指南，包含实际遇到的错误案例和解决方案。

## 扩展开发

### 添加新脚本
编辑 `document-dashboard/server/app.js`:
```javascript
const SCRIPTS = {
    'new-script': 'path/to/new-script.sh',
    // ...
};
```

### 自定义文档类型
修改 `PUBLISHED_PATHS` 对象添加新文档类别。

### 样式定制
编辑 `document-dashboard/public/styles.css` 进行界面定制。

## 安全注意事项
- Dashboard仅监听localhost，不接受网络连接
- 文件操作限制在项目目录内
- 脚本执行使用当前用户权限
- 单用户环境，无需身份验证
- 遵循基本编程规范

---

**维护状态**: ✅ 活跃开发中  
**兼容性**: Node.js 14+, 现代浏览器  
**更新频率**: 随项目需求定期更新