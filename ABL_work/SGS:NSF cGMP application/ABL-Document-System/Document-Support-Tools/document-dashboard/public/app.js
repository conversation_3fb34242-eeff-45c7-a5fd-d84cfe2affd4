// ABL Document System Dashboard - Client Side JavaScript

class DocumentDashboard {
    constructor() {
        this.documents = {};
        this.currentView = 'grid';
        this.currentSort = 'modified-desc';
        this.currentLetterFilter = 'all';
        this.currentSubpartFilter = 'all';
        this.regulatoryConfig = null;
        this.ws = null;
        this.init();
    }

    async init() {
        this.setupWebSocket();
        await this.loadRegulatoryConfig();
        this.loadDocuments();
        this.setupEventListeners();
        this.startPeriodicRefresh();
    }

    // WebSocket for real-time updates
    setupWebSocket() {
        const wsProtocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
        const wsUrl = `${wsProtocol}//${window.location.host}`;
        
        try {
            this.ws = new WebSocket(wsUrl);
            
            this.ws.onmessage = (event) => {
                const data = JSON.parse(event.data);
                this.handleWebSocketMessage(data);
            };
            
            this.ws.onclose = () => {
                console.log('WebSocket disconnected, attempting to reconnect...');
                setTimeout(() => this.setupWebSocket(), 5000);
            };
        } catch (error) {
            console.log('WebSocket not available, using polling instead');
        }
    }

    handleWebSocketMessage(data) {
        switch (data.type) {
            case 'console':
                this.addConsoleMessage(data.message, data.level);
                break;
            case 'status':
                this.updateStatus(data.status);
                break;
            case 'documents_updated':
                this.loadDocuments();
                break;
        }
    }

    // Load regulatory configuration
    async loadRegulatoryConfig() {
        try {
            console.log('Fetching regulatory config from /api/regulatory-config');
            const response = await fetch('/api/regulatory-config');
            console.log('Response status:', response.status);
            
            if (response.ok) {
                this.regulatoryConfig = await response.json();
                console.log('Regulatory config loaded successfully');
                console.log('Config has all_subparts:', !!this.regulatoryConfig.all_subparts);
                if (this.regulatoryConfig.all_subparts) {
                    console.log('Subpart keys:', Object.keys(this.regulatoryConfig.all_subparts));
                }
                this.populateSubpartOptions();
            } else {
                const errorText = await response.text();
                console.error('Failed to load regulatory config, status:', response.status, 'error:', errorText);
            }
        } catch (error) {
            console.error('Failed to load regulatory config:', error);
        }
    }

    // Load and display documents
    async loadDocuments() {
        try {
            console.log('Loading documents from /api/documents');
            const response = await fetch('/api/documents');
            console.log('Response status:', response.status, response.statusText);
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            const data = await response.json();
            console.log('Documents loaded successfully:', Object.keys(data));
            this.documents = data;
            this.renderDocuments();
            this.updateCounts();
            this.updateLastUpdate();
            
            // Ensure subpart options are populated after DOM is ready
            const subpartFilter = document.getElementById('subpart-filter');
            if (this.regulatoryConfig && subpartFilter && subpartFilter.options.length <= 1) {
                setTimeout(() => this.populateSubpartOptions(), 100);
            }
        } catch (error) {
            console.error('Failed to load documents:', error);
            this.addConsoleMessage(`❌ Failed to load documents: ${error.message}`, 'error');
        }
    }

    renderDocuments() {
        const container = document.getElementById('file-container');
        container.innerHTML = '';

        const allFiles = [];
        
        // Collect all files from all document types
        Object.keys(this.documents).forEach(type => {
            this.documents[type].forEach(file => {
                allFiles.push({ ...file, type });
            });
        });

        // Apply letter filter
        let filteredFiles = this.applyLetterFilter(allFiles);
        
        // Apply subpart filter
        filteredFiles = this.applySubpartFilter(filteredFiles);

        // Apply sort order
        const sortedFiles = this.applySortOrder(filteredFiles);

        // Update container class for view mode
        container.className = this.currentView === 'list' ? 'file-list' : 'file-grid';

        sortedFiles.forEach(file => {
            const fileElement = this.createFileElement(file);
            container.appendChild(fileElement);
        });
    }

    createFileElement(file) {
        const div = document.createElement('div');
        const isListView = this.currentView === 'list';
        div.className = isListView ? 'file-item-list' : 'file-item';
        div.onclick = () => this.openFile(file);

        const icon = this.getFileIcon(file);
        const size = this.formatFileSize(file.size);
        const date = new Date(file.modified).toLocaleDateString();
        const cfrBadges = this.getCFRBadges(file);

        if (isListView) {
            div.innerHTML = `
                <div class="file-icon">${icon}</div>
                <div class="file-info">
                    <div class="file-name">${file.name}${cfrBadges}</div>
                    <div class="file-meta">${file.type} • ${size} • ${date}</div>
                </div>
            `;
        } else {
            div.innerHTML = `
                <div class="file-icon">${icon}</div>
                <div class="file-name">${file.name}</div>
                <div class="file-meta">${file.type} • ${size} • ${date}${cfrBadges}</div>
            `;
        }

        return div;
    }

    getFileIcon(file) {
        if (file.name.endsWith('.pdf')) return '📄';
        if (file.name.endsWith('.html')) return '🌐';
        if (file.name.endsWith('.png')) return '🖼️';
        return '📄';
    }

    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    async openFile(file) {
        try {
            const response = await fetch('/api/open-file', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ filePath: file.path })
            });

            if (response.ok) {
                this.addConsoleMessage(`📂 Opened: ${file.name}`, 'success');
            } else {
                this.addConsoleMessage(`❌ Failed to open: ${file.name}`, 'error');
            }
        } catch (error) {
            console.error('Failed to open file:', error);
            this.addConsoleMessage(`❌ Error opening: ${file.name}`, 'error');
        }
    }

    updateCounts() {
        Object.keys(this.documents).forEach(type => {
            // Convert type to element ID (handle SOP-MGMT special case)
            const elementId = type === 'SOP-MGMT' ? 'sop-mgmt-count' : `${type.toLowerCase()}-count`;
            const countElement = document.getElementById(elementId);
            if (countElement) {
                countElement.textContent = this.documents[type].length;
            }
        });
    }

    updateLastUpdate() {
        const now = new Date().toLocaleString();
        document.getElementById('last-update').textContent = `Last updated: ${now}`;
    }

    // Script execution
    async executeScript(scriptName) {
        const button = event.target;
        const originalText = button.textContent;
        
        // Update button state
        button.disabled = true;
        button.innerHTML = '<span class="loading"></span> Running...';
        
        this.updateStatus('busy');
        this.addConsoleMessage(`🚀 Starting: ${this.getScriptDisplayName(scriptName)}`, 'info');

        try {
            const response = await fetch('/api/execute-script', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ script: scriptName })
            });

            const result = await response.json();
            
            if (result.success) {
                this.addConsoleMessage(`✅ Completed: ${this.getScriptDisplayName(scriptName)}`, 'success');
                if (result.output) {
                    result.output.split('\n').forEach(line => {
                        if (line.trim()) {
                            this.addConsoleMessage(line, 'info');
                        }
                    });
                }
                // Refresh documents after successful generation
                setTimeout(() => this.loadDocuments(), 1000);
            } else {
                this.addConsoleMessage(`❌ Failed: ${this.getScriptDisplayName(scriptName)}`, 'error');
                if (result.error) {
                    this.addConsoleMessage(result.error, 'error');
                }
            }
        } catch (error) {
            console.error('Script execution failed:', error);
            this.addConsoleMessage(`❌ Error: ${this.getScriptDisplayName(scriptName)}`, 'error');
        } finally {
            // Restore button state
            button.disabled = false;
            button.innerHTML = originalText;
            this.updateStatus('ready');
        }
    }

    getScriptDisplayName(scriptName) {
        const names = {
            'generate-all': 'Generate All Documents',
            'generate-sop': 'Generate SOP Documents',
            'generate-sop-mgmt': 'Generate SOP Management Documents',
            'generate-mmr': 'Generate MMR Documents',
            'generate-rms': 'Generate RMS Documents',
            'generate-spec': 'Generate SPEC Documents',
            'generate-rec': 'Generate REC Documents',
            'generate-flowchart': 'Generate Flowcharts',
            'generate-html': 'Generate HTML Forms',
            'generate-html-rec': 'Generate REC HTML Forms'
        };
        return names[scriptName] || scriptName;
    }

    // Console management
    addConsoleMessage(message, level = 'info') {
        const console = document.getElementById('console-output');
        const div = document.createElement('div');
        div.className = `console-line ${level}`;
        
        const timestamp = new Date().toLocaleTimeString();
        div.textContent = `[${timestamp}] ${message}`;
        
        console.appendChild(div);
        console.scrollTop = console.scrollHeight;

        // Keep only last 100 messages
        while (console.children.length > 100) {
            console.removeChild(console.firstChild);
        }
    }

    clearConsole() {
        document.getElementById('console-output').innerHTML = '';
        this.addConsoleMessage('Console cleared', 'info');
    }

    // Status management
    updateStatus(status) {
        const indicator = document.getElementById('status-indicator');
        indicator.className = `status-${status}`;
        indicator.textContent = status.charAt(0).toUpperCase() + status.slice(1);
    }

    // View controls
    setView(viewType) {
        this.currentView = viewType;
        document.getElementById('list-view').classList.toggle('active', viewType === 'list');
        document.getElementById('grid-view').classList.toggle('active', viewType === 'grid');
        
        // Re-render documents to apply correct styling
        this.renderDocuments();
    }

    // Sort controls
    setSortOrder(sortOrder) {
        this.currentSort = sortOrder;
        this.renderDocuments();
    }

    applySortOrder(files) {
        const sortedFiles = [...files];
        
        switch (this.currentSort) {
            case 'name-asc':
                return sortedFiles.sort((a, b) => a.name.localeCompare(b.name));
            case 'name-desc':
                return sortedFiles.sort((a, b) => b.name.localeCompare(a.name));
            case 'modified-asc':
                return sortedFiles.sort((a, b) => new Date(a.modified) - new Date(b.modified));
            case 'modified-desc':
            default:
                return sortedFiles.sort((a, b) => new Date(b.modified) - new Date(a.modified));
        }
    }

    // Letter filter controls
    setLetterFilter(letter) {
        this.currentLetterFilter = letter;
        this.renderDocuments();
    }

    applyLetterFilter(files) {
        if (this.currentLetterFilter === 'all') {
            return files;
        }
        
        return files.filter(file => {
            // Extract SOP letter from filename (e.g., ABL-SOP-A-001 -> A)
            const sopMatch = file.name.match(/ABL-SOP-([A-K])-\d+/i);
            if (sopMatch) {
                return sopMatch[1].toUpperCase() === this.currentLetterFilter;
            }
            
            // For non-SOP files, show them when 'all' is selected
            return this.currentLetterFilter === 'all';
        });
    }

    // Extract SOP letter for display purposes
    getSOPLetter(file) {
        const sopMatch = file.name.match(/ABL-SOP-([A-K])-\d+/i);
        return sopMatch ? sopMatch[1].toUpperCase() : null;
    }

    // Get CFR badges for a file
    getCFRBadges(file) {
        if (!this.regulatoryConfig) return '';
        
        const sopLetter = this.getSOPLetter(file);
        if (!sopLetter) return '';
        
        const categoryConfig = this.regulatoryConfig.sop_categories[sopLetter];
        if (!categoryConfig || !categoryConfig.applicable_subparts) return '';
        
        let badges = '';
        const has111 = categoryConfig.applicable_subparts.some(s => s.startsWith('111.'));
        const has117 = categoryConfig.applicable_subparts.some(s => s.startsWith('117.'));
        
        if (has111) {
            badges += '<span class="cfr-badge cfr-111">CFR 111</span>';
        }
        
        if (has117) {
            badges += '<span class="cfr-badge cfr-117">CFR 117</span>';
        }
        
        return badges;
    }

    // Subpart filter controls
    setSubpartFilter(subpart) {
        this.currentSubpartFilter = subpart;
        this.renderDocuments();
    }

    // Populate all available subpart options
    populateSubpartOptions() {
        const subpartSelect = document.getElementById('subpart-filter');
        if (!subpartSelect || !this.regulatoryConfig) {
            console.log('Cannot populate subpart options:', {
                subpartSelect: !!subpartSelect,
                regulatoryConfig: !!this.regulatoryConfig
            });
            return;
        }
        
        // Clear existing options except 'All'
        subpartSelect.innerHTML = '<option value="all">All CFR Subparts</option>';
        
        if (!this.regulatoryConfig.all_subparts) {
            console.log('No all_subparts found in config');
            return;
        }
        
        // Add options for each subpart, sorted by regulation and then by subpart
        const subpartKeys = Object.keys(this.regulatoryConfig.all_subparts).sort();
        console.log('Adding subpart options:', subpartKeys);
        
        subpartKeys.forEach(subpartKey => {
            const subpartInfo = this.regulatoryConfig.all_subparts[subpartKey];
            const option = document.createElement('option');
            option.value = subpartKey;
            option.textContent = subpartInfo.name;
            subpartSelect.appendChild(option);
        });
        
        console.log('Subpart options populated, total options:', subpartSelect.options.length);
    }

    // Apply subpart filter to files
    applySubpartFilter(files) {
        if (this.currentSubpartFilter === 'all') {
            return files;
        }
        
        return files.filter(file => {
            // Extract SOP letter from filename
            const sopLetter = this.getSOPLetter(file);
            if (!sopLetter) {
                return false; // Non-SOP files are filtered out when subpart filter is active
            }
            
            const categoryConfig = this.regulatoryConfig.sop_categories[sopLetter];
            if (!categoryConfig || !categoryConfig.applicable_subparts) {
                return false;
            }
            
            // Check if the document applies to the selected subpart
            return categoryConfig.applicable_subparts.includes(this.currentSubpartFilter);
        });
    }

    // Event listeners
    setupEventListeners() {
        // Category card clicks
        document.querySelectorAll('.category-card').forEach(card => {
            card.addEventListener('click', () => {
                const type = card.dataset.type;
                this.filterByType(type);
            });
        });

        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey || e.metaKey) {
                switch (e.key) {
                    case 'r':
                        e.preventDefault();
                        this.refreshDocuments();
                        break;
                    case 'g':
                        e.preventDefault();
                        this.executeScript('generate-all');
                        break;
                }
            }
        });
    }

    filterByType(type) {
        const container = document.getElementById('file-container');
        // Support both grid and list view item classes
        const items = container.querySelectorAll('.file-item, .file-item-list');
        
        items.forEach(item => {
            const fileMetaElement = item.querySelector('.file-meta');
            if (fileMetaElement) {
                const fileType = fileMetaElement.textContent.split(' • ')[0];
                if (type === 'ALL' || fileType === type) {
                    item.style.display = this.currentView === 'list' ? 'flex' : 'block';
                } else {
                    item.style.display = 'none';
                }
            }
        });
    }

    async refreshDocuments() {
        this.addConsoleMessage('🔄 Refreshing documents...', 'info');
        await this.loadDocuments();
        this.addConsoleMessage('✅ Documents refreshed', 'success');
    }

    startPeriodicRefresh() {
        // Disabled automatic refresh to prevent interface reset
        // Use WebSocket for real-time updates instead
        // setInterval(() => {
        //     this.loadDocuments();
        // }, 30000);
    }
}

// Global functions for HTML onclick events
window.executeScript = function(scriptName) {
    dashboard.executeScript(scriptName);
};

window.refreshDocuments = function() {
    dashboard.refreshDocuments();
};

window.clearConsole = function() {
    dashboard.clearConsole();
};

window.setView = function(viewType) {
    dashboard.setView(viewType);
};

window.setSortOrder = function(sortOrder) {
    dashboard.setSortOrder(sortOrder);
};

window.setLetterFilter = function(letter) {
    dashboard.setLetterFilter(letter);
};

window.setSubpartFilter = function(subpart) {
    dashboard.setSubpartFilter(subpart);
};

// Initialize dashboard when page loads
let dashboard;
document.addEventListener('DOMContentLoaded', () => {
    dashboard = new DocumentDashboard();
});