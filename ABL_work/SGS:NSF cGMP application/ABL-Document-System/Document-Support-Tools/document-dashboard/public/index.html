<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ABL Document System Dashboard</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <h1>🏢 ABL Document System Dashboard</h1>
            <div class="header-info">
                <span id="status-indicator" class="status-ready">Ready</span>
                <span id="last-update">Last updated: Loading...</span>
            </div>
        </div>
    </header>

    <!-- Toolbar -->
    <div class="toolbar">
        <div class="toolbar-section">
            <h3>📄 PDF Generation</h3>
            <button class="btn btn-primary" onclick="executeScript('generate-all')">
                🚀 Generate All Documents
            </button>
            <div class="button-group">
                <button class="btn btn-secondary" onclick="executeScript('generate-sop')">
                    📋 SOP
                </button>
                <button class="btn btn-secondary" onclick="executeScript('generate-sop-mgmt')">
                    📋 SOP-MGMT
                </button>
                <button class="btn btn-secondary" onclick="executeScript('generate-mmr')">
                    🏭 MMR
                </button>
                <button class="btn btn-secondary" onclick="executeScript('generate-rms')">
                    🧪 RMS
                </button>
                <button class="btn btn-secondary" onclick="executeScript('generate-spec')">
                    📊 SPEC
                </button>
                <button class="btn btn-secondary" onclick="executeScript('generate-rec')">
                    📝 REC
                </button>
            </div>
        </div>
        
        <div class="toolbar-section">
            <h3>🌐 HTML & Others</h3>
            <button class="btn btn-accent" onclick="executeScript('generate-html')">
                🌐 All HTML Forms
            </button>
            <button class="btn btn-accent" onclick="executeScript('generate-html-rec')">
                📝 REC HTML Forms
            </button>
            <button class="btn btn-secondary" onclick="executeScript('generate-flowchart')">
                🔄 Flowcharts
            </button>
            <button class="btn btn-outline" onclick="refreshDocuments()">
                🔄 Refresh Files
            </button>
        </div>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Document Categories -->
        <div class="document-categories">
            <div class="category-card" data-type="SOP">
                <div class="category-header">
                    <h3>📋 SOP Documents</h3>
                    <span class="count" id="sop-count">0</span>
                </div>
                <p>Standard Operating Procedures</p>
            </div>
            
            <div class="category-card" data-type="SOP-MGMT">
                <div class="category-header">
                    <h3>📋 SOP Management</h3>
                    <span class="count" id="sop-mgmt-count">0</span>
                </div>
                <p>Master Directory & Gap Analysis</p>
            </div>
            
            <div class="category-card" data-type="MMR">
                <div class="category-header">
                    <h3>🏭 MMR Documents</h3>
                    <span class="count" id="mmr-count">0</span>
                </div>
                <p>Master Manufacturing Records</p>
            </div>
            
            <div class="category-card" data-type="RMS">
                <div class="category-header">
                    <h3>🧪 RMS Documents</h3>
                    <span class="count" id="rms-count">0</span>
                </div>
                <p>Raw Material Specifications</p>
            </div>
            
            <div class="category-card" data-type="SPEC">
                <div class="category-header">
                    <h3>📊 SPEC Documents</h3>
                    <span class="count" id="spec-count">0</span>
                </div>
                <p>Product Specifications</p>
            </div>
            
            <div class="category-card" data-type="REC">
                <div class="category-header">
                    <h3>📝 REC Documents</h3>
                    <span class="count" id="rec-count">0</span>
                </div>
                <p>Records Templates</p>
            </div>
        </div>

        <!-- File Browser -->
        <div class="file-browser">
            <div class="browser-header">
                <!-- Row 1: Title -->
                <div class="title-row">
                    <h3>📁 Published Documents</h3>
                </div>
                
                <!-- Row 2: View and Sort Controls -->
                <div class="controls-row-1">
                    <div class="view-controls">
                        <button class="btn-small" onclick="setView('list')" id="list-view">📄 List</button>
                        <button class="btn-small active" onclick="setView('grid')" id="grid-view">⊞ Grid</button>
                    </div>
                    
                    <div class="sort-controls">
                        <label for="sort-select">Sort:</label>
                        <select id="sort-select" onchange="setSortOrder(this.value)">
                            <option value="modified-desc">📅 Latest First</option>
                            <option value="modified-asc">📅 Oldest First</option>
                            <option value="name-asc">🔤 Name A-Z</option>
                            <option value="name-desc">🔤 Name Z-A</option>
                        </select>
                    </div>
                </div>
                
                <!-- Row 3: Filter Controls -->
                <div class="controls-row-2">
                    <div class="filter-controls">
                        <label for="letter-filter">SOP Letter:</label>
                        <select id="letter-filter" onchange="setLetterFilter(this.value)">
                            <option value="all">All Letters</option>
                            <option value="A">A - Quality Management</option>
                            <option value="B">B - Personnel</option>
                            <option value="C">C - Facilities & Equipment</option>
                            <option value="D">D - Materials</option>
                            <option value="E">E - Production Control</option>
                            <option value="F">F - Quality Control</option>
                            <option value="G">G - Manufacturing</option>
                            <option value="H">H - Packaging & Labeling</option>
                            <option value="I">I - Holding & Distribution</option>
                            <option value="J">J - Returns & Complaints</option>
                            <option value="K">K - Records</option>
                        </select>
                    </div>
                    
                    <div class="subpart-controls">
                        <label for="subpart-filter">CFR Subpart:</label>
                        <select id="subpart-filter" onchange="setSubpartFilter(this.value)">
                            <option value="all">All CFR Subparts</option>
                            <!-- Options will be populated dynamically -->
                        </select>
                    </div>
                </div>
            </div>
            
            <div class="file-grid" id="file-container">
                <!-- Files will be loaded here -->
            </div>
        </div>
    </div>

    <!-- Console Output -->
    <div class="console-section">
        <div class="console-header">
            <h3>📟 Console Output</h3>
            <button class="btn-small" onclick="clearConsole()">🗑️ Clear</button>
        </div>
        <div class="console" id="console-output">
            <div class="console-line">🚀 ABL Document System Dashboard initialized</div>
            <div class="console-line">✅ Ready to generate and manage documents</div>
        </div>
    </div>

    <script src="app.js"></script>
</body>
</html>