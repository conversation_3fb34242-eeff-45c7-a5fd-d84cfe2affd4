// ABL Document System Dashboard - Server
const express = require('express');
const path = require('path');
const { exec } = require('child_process');
const fs = require('fs');
const WebSocket = require('ws');
const http = require('http');
const chokidar = require('chokidar');

const app = express();
const server = http.createServer(app);
const wss = new WebSocket.Server({ server });

// Middleware
app.use(express.json());
app.use(express.static(path.join(__dirname, '../public')));

// File paths relative to project root
const PROJECT_ROOT = path.resolve(__dirname, '../../../..');
const CONFIG_PATH = path.join(PROJECT_ROOT, 'ABL-Document-System/Document-Support-Tools/config');
const PUBLISHED_PATHS = {
    SOP: path.join(PROJECT_ROOT, 'ABL-Document-System/Core-Documents/SOP-Standard-Procedures/published'),
    'SOP-MGMT': path.join(PROJECT_ROOT, 'ABL-Document-System/Core-Documents/SOP-Standard-Procedures/management'),
    MMR: path.join(PROJECT_ROOT, 'ABL-Document-System/Core-Documents/MMR-Manufacturing-Records/published'),
    RMS: path.join(PROJECT_ROOT, 'ABL-Document-System/Core-Documents/RMS-Raw-Material-Specifications/published'),
    SPEC: path.join(PROJECT_ROOT, 'ABL-Document-System/Core-Documents/SPEC-Product-Specifications/published'),
    REC: path.join(PROJECT_ROOT, 'ABL-Document-System/Core-Documents/REC-Records-Templates/published')
};

// Scripts mapping
const SCRIPTS = {
    'generate-all': `"${path.join(PROJECT_ROOT, 'generate-pdf.sh')}"`,
    'generate-sop': `"${path.join(PROJECT_ROOT, 'generate-pdf.sh')}" SOP`,
    'generate-sop-mgmt': `"${path.join(PROJECT_ROOT, 'generate-pdf.sh')}" MGMT`,
    'generate-mmr': `"${path.join(PROJECT_ROOT, 'generate-pdf.sh')}" MMR`,
    'generate-rms': `"${path.join(PROJECT_ROOT, 'generate-pdf.sh')}" RMS`,
    'generate-spec': `"${path.join(PROJECT_ROOT, 'generate-pdf.sh')}" SPEC`,
    'generate-rec': `"${path.join(PROJECT_ROOT, 'generate-pdf.sh')}" REC`,
    'generate-flowchart': `cd "${path.join(PROJECT_ROOT, 'ABL-Document-System/Document-Support-Tools/pdf-generators')}" && ./document-tools.sh flowchart`,
    'generate-html': `"${path.join(PROJECT_ROOT, 'generate-html.sh')}"`,
    'generate-html-rec': `"${path.join(PROJECT_ROOT, 'generate-html.sh')}" REC`
};

// WebSocket connections
const clients = new Set();

wss.on('connection', (ws) => {
    clients.add(ws);
    console.log('Client connected');
    
    ws.on('close', () => {
        clients.delete(ws);
        console.log('Client disconnected');
    });
});

// Broadcast to all connected clients
function broadcast(data) {
    clients.forEach(client => {
        if (client.readyState === WebSocket.OPEN) {
            client.send(JSON.stringify(data));
        }
    });
}

// File watcher for real-time updates
function setupFileWatcher() {
    const watchPaths = Object.values(PUBLISHED_PATHS);
    const watcher = chokidar.watch(watchPaths, {
        ignored: /^\./, 
        persistent: true,
        ignoreInitial: true
    });

    watcher.on('add', () => {
        broadcast({ type: 'documents_updated' });
    });

    watcher.on('unlink', () => {
        broadcast({ type: 'documents_updated' });
    });

    console.log('📁 File watcher initialized for published directories');
}

// Utility functions
function getFileInfo(filePath) {
    try {
        const stats = fs.statSync(filePath);
        return {
            name: path.basename(filePath),
            path: filePath,
            size: stats.size,
            modified: stats.mtime.toISOString(),
            isFile: stats.isFile()
        };
    } catch (error) {
        console.error(`Error getting file info for ${filePath}:`, error);
        return null;
    }
}

function scanDirectory(dirPath) {
    try {
        if (!fs.existsSync(dirPath)) {
            console.log(`Directory does not exist: ${dirPath}`);
            return [];
        }

        const files = fs.readdirSync(dirPath);
        return files
            .map(file => getFileInfo(path.join(dirPath, file)))
            .filter(fileInfo => fileInfo && fileInfo.isFile)
            .filter(fileInfo => !fileInfo.name.startsWith('.')) // Skip hidden files
            .sort((a, b) => new Date(b.modified) - new Date(a.modified));
    } catch (error) {
        console.error(`Error scanning directory ${dirPath}:`, error);
        return [];
    }
}

// API Routes - Documents endpoint with CFR filtering support
app.get('/api/documents', (req, res) => {
    try {
        const { cfr, subpart, letter } = req.query;
        const documents = {};
        
        Object.keys(PUBLISHED_PATHS).forEach(type => {
            documents[type] = scanDirectory(PUBLISHED_PATHS[type]);
        });

        // Apply CFR filtering if requested
        if (cfr || subpart || letter) {
            const filteredDocuments = applyRegulatoryFilter(documents, { cfr, subpart, letter });
            return res.json(filteredDocuments);
        }

        res.json(documents);
    } catch (error) {
        console.error('Error loading documents:', error);
        res.status(500).json({ error: 'Failed to load documents' });
    }
});

app.post('/api/open-file', (req, res) => {
    const { filePath } = req.body;
    
    if (!filePath || !fs.existsSync(filePath)) {
        return res.status(400).json({ error: 'File not found' });
    }

    // Use system's default application to open the file
    const command = process.platform === 'darwin' ? 'open' : 
                   process.platform === 'win32' ? 'start' : 'xdg-open';
    
    exec(`${command} "${filePath}"`, (error) => {
        if (error) {
            console.error('Error opening file:', error);
            return res.status(500).json({ error: 'Failed to open file' });
        }
        
        res.json({ success: true });
    });
});

app.post('/api/execute-script', (req, res) => {
    const { script } = req.body;
    
    if (!SCRIPTS[script]) {
        return res.status(400).json({ error: 'Unknown script' });
    }

    broadcast({ type: 'status', status: 'busy' });
    broadcast({ type: 'console', message: `Starting: ${script}`, level: 'info' });

    const command = SCRIPTS[script];
    
    const child = exec(command, { 
        cwd: PROJECT_ROOT,
        maxBuffer: 1024 * 1024 // 1MB buffer
    });

    let output = '';
    let hasError = false;

    child.stdout.on('data', (data) => {
        const message = data.toString().trim();
        output += message + '\n';
        if (message) {
            broadcast({ type: 'console', message, level: 'info' });
        }
    });

    child.stderr.on('data', (data) => {
        const message = data.toString().trim();
        if (message) {
            hasError = true;
            broadcast({ type: 'console', message, level: 'error' });
        }
    });

    child.on('close', (code) => {
        broadcast({ type: 'status', status: 'ready' });
        
        if (code === 0 && !hasError) {
            broadcast({ type: 'console', message: `✅ Script completed: ${script}`, level: 'success' });
            broadcast({ type: 'documents_updated' });
            res.json({ success: true, output: output.trim() });
        } else {
            broadcast({ type: 'console', message: `❌ Script failed: ${script}`, level: 'error' });
            res.json({ success: false, error: `Script exited with code ${code}`, output: output.trim() });
        }
    });

    child.on('error', (error) => {
        broadcast({ type: 'status', status: 'ready' });
        broadcast({ type: 'console', message: `❌ Script error: ${error.message}`, level: 'error' });
        res.json({ success: false, error: error.message });
    });
});

// Configuration API endpoints
app.get('/api/regulatory-config', (req, res) => {
    try {
        const configFile = path.join(CONFIG_PATH, 'regulatory-mapping.json');
        
        if (!fs.existsSync(configFile)) {
            return res.status(404).json({ error: 'Regulatory configuration not found' });
        }
        
        const configData = fs.readFileSync(configFile, 'utf8');
        const config = JSON.parse(configData);
        
        res.json(config);
    } catch (error) {
        console.error('Error loading regulatory config:', error);
        res.status(500).json({ error: 'Failed to load regulatory configuration' });
    }
});

app.get('/api/dashboard-settings', (req, res) => {
    try {
        const settingsFile = path.join(CONFIG_PATH, 'dashboard-settings.json');
        
        if (!fs.existsSync(settingsFile)) {
            return res.status(404).json({ error: 'Dashboard settings not found' });
        }
        
        const settingsData = fs.readFileSync(settingsFile, 'utf8');
        const settings = JSON.parse(settingsData);
        
        res.json(settings);
    } catch (error) {
        console.error('Error loading dashboard settings:', error);
        res.status(500).json({ error: 'Failed to load dashboard settings' });
    }
});


// Apply regulatory filtering
function applyRegulatoryFilter(documents, filters) {
    try {
        const configFile = path.join(CONFIG_PATH, 'regulatory-mapping.json');
        if (!fs.existsSync(configFile)) {
            return documents; // Return unfiltered if no config
        }
        
        const configData = fs.readFileSync(configFile, 'utf8');
        const config = JSON.parse(configData);
        
        const filteredDocuments = {};
        
        Object.keys(documents).forEach(type => {
            filteredDocuments[type] = documents[type].filter(file => {
                // Extract SOP letter from filename
                const sopMatch = file.name.match(/ABL-SOP-([A-K])-\d+/i);
                if (!sopMatch) {
                    return !filters.letter; // Include non-SOP files only if no letter filter
                }
                
                const sopLetter = sopMatch[1].toUpperCase();
                const categoryConfig = config.sop_categories[sopLetter];
                
                if (!categoryConfig) return false;
                
                // Apply letter filter
                if (filters.letter && filters.letter !== 'all' && sopLetter !== filters.letter) {
                    return false;
                }
                
                // Apply CFR filter
                if (filters.cfr && filters.cfr !== 'all') {
                    const cfrSubparts = filters.cfr === '111' ? 
                        categoryConfig.cfr_111_subparts : 
                        categoryConfig.cfr_117_subparts;
                    
                    if (!cfrSubparts || cfrSubparts.length === 0) {
                        return false;
                    }
                    
                    // Apply subpart filter
                    if (filters.subpart && filters.subpart !== 'all') {
                        return cfrSubparts.includes(filters.subpart);
                    }
                }
                
                return true;
            });
        });
        
        return filteredDocuments;
    } catch (error) {
        console.error('Error applying regulatory filter:', error);
        return documents; // Return unfiltered on error
    }
}

// Health check endpoint
app.get('/api/health', (req, res) => {
    res.json({ 
        status: 'ok', 
        timestamp: new Date().toISOString(),
        projectRoot: PROJECT_ROOT,
        configPath: CONFIG_PATH,
        publishedPaths: PUBLISHED_PATHS
    });
});

// Catch-all route for SPA
app.get('*', (req, res) => {
    res.sendFile(path.join(__dirname, '../public/index.html'));
});

// Error handling middleware
app.use((error, req, res, next) => {
    console.error('Unhandled error:', error);
    res.status(500).json({ error: 'Internal server error' });
});

// Start server
const PORT = process.env.PORT || 3000;

server.listen(PORT, () => {
    console.log('🚀 ABL Document System Dashboard');
    console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
    console.log(`📱 Server running at: http://localhost:${PORT}`);
    console.log(`📁 Project root: ${PROJECT_ROOT}`);
    console.log('💡 Use Ctrl+C to stop the server');
    console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
    
    // Initialize file watcher
    setupFileWatcher();
});

// Graceful shutdown
process.on('SIGINT', () => {
    console.log('\n🛑 Shutting down ABL Document Dashboard...');
    server.close(() => {
        console.log('✅ Server closed');
        process.exit(0);
    });
});

module.exports = app;