# CFR 111/117 法规筛选功能实施计划

## 📋 项目概述
为ABL文档系统Dashboard添加CFR 111/117法规subpart筛选功能，实现基于法规要求的文档分类和快速检索。

## 🎯 实施目标
- 添加CFR 111和CFR 117法规筛选功能
- 支持按subpart（子部分）筛选SOP文档
- 建立文档与法规的智能映射关系
- 提供直观的用户界面

---

## 📅 实施阶段计划

### 阶段一：基础架构搭建 (已完成部分)
- [x] **排序功能**：实现A-Z、Z-A、时间排序
- [x] **SOP字母筛选**：实现A-K类别筛选
- [x] **UI框架**：Grid/List视图切换
- [ ] **配置目录创建**：Document-Support-Tools/config/
- [ ] **TODO文档建立**：Reference-Materials中的实施计划

### 阶段二：配置文件系统 (计划中)
- [ ] **regulatory-mapping.json**：主配置文件
- [ ] **cfr-subparts.json**：法规子部分详细信息
- [ ] **dashboard-settings.json**：界面配置
- [ ] **document-categories.json**：文档分类配置

### 阶段三：后端API扩展 (计划中)
- [ ] **配置读取API**：/api/regulatory-config
- [ ] **文档筛选API**：/api/documents?cfr=111&subpart=A
- [ ] **映射更新API**：/api/update-mapping
- [ ] **配置验证机制**：确保数据完整性

### 阶段四：前端功能实现 (计划中)
- [ ] **CFR筛选下拉菜单**：CFR 111 | CFR 117 | All
- [ ] **Subpart筛选器**：A、B、C等子部分选择
- [ ] **文档标签显示**：每个文档显示关联的法规
- [ ] **快速筛选组合**：多条件组合筛选

### 阶段五：高级功能 (未来扩展)
- [ ] **智能推荐**：基于当前文档推荐相关法规文档
- [ ] **法规覆盖分析**：显示法规遵循完整性
- [ ] **配置可视化编辑**：在线编辑映射关系
- [ ] **导出功能**：按法规导出文档清单

---

## 🗂️ 配置文件结构设计

### Document-Support-Tools/config/
```
config/
├── regulatory-mapping.json      # 主配置：SOP字母与CFR的映射
├── cfr-subparts.json           # CFR子部分详细信息
├── dashboard-settings.json     # Dashboard界面配置
└── document-categories.json    # 文档分类元数据
```

### regulatory-mapping.json 结构
```json
{
  "version": "1.0.0",
  "last_updated": "2024-12-23",
  "sop_categories": {
    "A": {
      "name": "Quality Management System",
      "cfr_111_subparts": ["A"],
      "cfr_117_subparts": ["A", "B"],
      "primary_regulations": ["111.1", "111.3", "117.4"],
      "document_count": 8
    }
  }
}
```

---

## 🎨 UI/UX 设计规划

### 筛选器界面布局
```
[📁 Published Documents]                    [View: Grid/List]

Sort: [Latest First ▼]  SOP Letter: [All Letters ▼]  CFR: [All Regulations ▼]  Subpart: [All Subparts ▼]
```

### 文档显示增强
```
📄 ABL-SOP-A-001-Document-Control.pdf
   🏷️ CFR 111.A | CFR 117.A | Quality Management
   📅 2024-12-20 | 📊 SOP | 💾 1.2MB
```

---

## ⚙️ 技术实现细节

### SOP字母与CFR映射逻辑
| SOP字母 | CFR 111 Subparts | CFR 117 Subparts | 功能领域 |
|---------|------------------|------------------|----------|
| A | A (General) | A, B (General, cGMP) | 质量管理系统 |
| B | B (Personnel) | B (cGMP) | 人员管理 |
| C | C, D (Plant, Equipment) | B (cGMP) | 设施设备 |
| D | E, G (Components) | B (cGMP) | 原料管理 |
| E | E, H, I (Production) | C (HARPC) | 生产控制 |
| F | F, J (QC, Lab) | B (cGMP) | 质量控制 |
| G | K (Manufacturing) | B (cGMP) | 制造运营 |
| H | L (Packaging) | B (cGMP) | 包装标签 |
| I | M (Distribution) | B (cGMP) | 储存分发 |
| J | N, O (Returns, Complaints) | G (Supply Chain) | 退货投诉 |
| K | P (Records) | F (Records) | 记录管理 |

### 筛选算法逻辑
```javascript
function applyRegulatoryFilter(files, cfrType, subpart) {
  return files.filter(file => {
    const sopLetter = extractSOPLetter(file.name);
    const mapping = regulatoryConfig.sop_categories[sopLetter];
    
    if (cfrType === '111') {
      return mapping.cfr_111_subparts.includes(subpart);
    } else if (cfrType === '117') {
      return mapping.cfr_117_subparts.includes(subpart);
    }
    return true; // 显示所有
  });
}
```

---

## 📊 实施进度跟踪

### 当前状态 (2024-12-23)
- ✅ 基础Grid/List视图：100%
- ✅ 排序功能：100%
- ✅ SOP字母筛选：100%
- ⏳ 配置文件系统：0%
- ⏳ CFR法规筛选：0%
- ⏳ 后端API扩展：0%

### 下一步行动
1. **立即执行**：创建config目录和基础配置文件
2. **本周完成**：实现regulatory-mapping.json配置
3. **下周计划**：扩展Dashboard API支持配置读取
4. **月度目标**：完成CFR筛选功能的完整实现

---

## 🚀 快速启动指南

### 开发者快速开始
```bash
# 1. 创建配置目录
mkdir -p ABL-Document-System/Document-Support-Tools/config

# 2. 启动Dashboard
cd ABL-Document-System/Document-Support-Tools/document-dashboard
npm start

# 3. 编辑配置文件
vi ../config/regulatory-mapping.json
```

### 配置文件编辑
```json
{
  "version": "1.0.0",
  "sop_categories": {
    "A": {
      "name": "Quality Management System",
      "cfr_111_subparts": ["A"],
      "cfr_117_subparts": ["A", "B"]
    }
  }
}
```

---

## 📝 变更日志

### v1.0 (2024-12-23)
- 初始TODO文档创建
- 完成基础排序和SOP字母筛选功能
- 规划CFR法规筛选实施方案

### 未来版本规划
- v1.1: 基础CFR筛选功能
- v1.2: 高级组合筛选
- v1.3: 智能推荐和分析
- v2.0: 配置可视化管理界面

---

## 🤝 维护和支持

### 文档维护
- **负责人**：开发团队
- **更新频率**：每次功能更新后
- **审核流程**：代码审查时同步更新

### 配置管理
- **配置文件位置**：Document-Support-Tools/config/
- **备份策略**：Git版本控制
- **访问权限**：开发团队和质量保证团队

### 技术支持
- **问题反馈**：通过Git Issues
- **功能请求**：产品需求文档
- **紧急支持**：直接联系开发团队