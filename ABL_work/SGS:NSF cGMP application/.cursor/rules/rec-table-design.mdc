---
description: REC Records Templates Design Guidelines - Updated for Generated HTML Style
globs: **/REC-Records-Templates/**/*.md
alwaysApply: false
---
# REC Records Templates Design Guide

## Core Design Philosophy

### 1. Dual Output Support
- **PDF Generation**: Static documents for archival and distribution
- **HTML Generation**: Interactive forms for online filling and submission
- **Unified Source**: Single Markdown file generates both formats seamlessly

### 2. Document Structure Standards
```markdown
---
doc_title: "Record Title"
doc_number: "ABL-REC-XXX"
revision: "00"
---

<style>
/* Custom styles embedded in Markdown */
</style>

<div class="page-container">
<!-- HTML content here -->
</div>
```

### 3. Fixed Width Precision Control
- **Container Width**: `190mm` (optimized for Letter paper)
- **Font Sizing**: `12px` for main content, `11px` for headers, `9-10px` for details
- **Millimeter Units**: Use `mm` for precise paper layout control
- **Page Break Control**: `page-break-inside: avoid` for table groups

### 4. Modern HTML Table Approach
```html
<table border="1" style="border-collapse: collapse; font-size: 12px; margin-bottom: 8px; width: 190mm;">
  <tr>
    <td style="padding: 4px; background-color: #f0f0f0; font-weight: bold; font-size: 11px; width: 35mm;">Label</td>
    <td style="padding: 4px; width: 60mm;" contenteditable="true">&nbsp;</td>
  </tr>
</table>
```

## Practical Application Templates

### 1. Training Information Section (4x2 Grid)
```html
<div style="page-break-inside: avoid;">
<table border="1" style="border-collapse: collapse; font-size: 12px; margin-bottom: 8px; width: 190mm;">
  <tr>
    <td style="padding: 4px; background-color: #f0f0f0; font-weight: bold; font-size: 11px; width: 35mm;">Training Topic</td>
    <td style="padding: 4px; width: 60mm;" contenteditable="true">&nbsp;</td>
    <td style="padding: 4px; background-color: #f0f0f0; font-weight: bold; font-size: 11px; width: 35mm;">Training Date</td>
    <td style="padding: 4px; width: 60mm;" contenteditable="true">&nbsp;</td>
  </tr>
  <tr>
    <td style="padding: 4px; background-color: #f0f0f0; font-weight: bold; font-size: 11px;">Training Location</td>
    <td style="padding: 4px;" contenteditable="true">&nbsp;</td>
    <td style="padding: 4px; background-color: #f0f0f0; font-weight: bold; font-size: 11px;">Instructor</td>
    <td style="padding: 4px;" contenteditable="true">&nbsp;</td>
  </tr>
</table>
</div>
```

### 2. Assessment Content Section (Mixed Layout)
```html
<div style="page-break-inside: avoid;">
<table border="1" style="border-collapse: collapse; font-size: 12px; margin-bottom: 8px; width: 190mm;">
  <tr>
    <td style="padding: 4px; background-color: #f0f0f0; font-weight: bold; font-size: 11px; width: 40mm;">Training Content Summary</td>
    <td style="padding: 4px; height: 60px; vertical-align: top; width: 55mm;" contenteditable="true">&nbsp;</td>
    <td style="padding: 4px; background-color: #f0f0f0; font-weight: bold; font-size: 11px; width: 40mm;">Assessment Method</td>
    <td style="padding: 4px; width: 55mm; font-size: 9px;">
      <div><input type="checkbox"> Written Test <input type="checkbox"> Oral Test</div>
      <div style="margin-top: 2px;"><input type="checkbox"> Practical <input type="checkbox"> Discussion</div>
    </td>
  </tr>
</table>
</div>
```

### 3. Attendance List Section (Standard Table)
```html
<div style="page-break-inside: avoid;">
<table border="1" style="border-collapse: collapse; font-size: 11px; width: 190mm;">
  <tr style="background-color: #f0f0f0;">
    <th style="text-align: center; padding: 3px; width: 25mm;">No.</th>
    <th style="text-align: center; padding: 3px; width: 55mm;">Name</th>
    <th style="text-align: center; padding: 3px; width: 55mm;">Department</th>
    <th style="text-align: center; padding: 3px; width: 55mm;">Signature</th>
  </tr>
  <tr>
    <td style="text-align: center; padding: 2px;">1</td>
    <td style="padding: 2px;" contenteditable="true">&nbsp;</td>
    <td style="padding: 2px;" contenteditable="true">&nbsp;</td>
    <td style="padding: 2px;" contenteditable="true">&nbsp;</td>
  </tr>
</table>
</div>
```

## Essential CSS Styling Framework

### Page Container Standards
```css
.page-container {
    max-width: 210mm; /* Letter纸宽度 */
    width: 100%;
    margin: 0 auto;
    padding: 10px;
    box-sizing: border-box;
    background: white;
    min-height: 279mm; /* Letter纸高度 */
    font-family: "SimSun", "宋体", Arial, sans-serif;
}

@media print {
    .page-container {
        max-width: none;
        margin: 0;
        padding: 8mm;
        min-height: auto;
    }
}
```

### Form Element Standards
```css
/* Editable fields styling */
span[contenteditable="true"] {
    border-bottom: 1px solid #000;
    display: inline-block;
    width: [specified]mm;
}

/* Checkbox styling */
input[type="checkbox"] {
    margin-right: 5px;
    vertical-align: middle;
}
```

## Width Allocation Strategy

### Training Record Layout (190mm total)
- **Label Columns**: `35-40mm` (fixed content)
- **Input Columns**: `55-60mm` (user content)
- **Serial Numbers**: `25mm` (minimal space)
- **Names/Departments**: `55mm` (adequate space)
- **Signatures**: `55mm` (handwriting space)

## Quality Assurance Standards

### 1. Document Header Requirements
- Generated HTML must include document header table
- Display: Title | Document Number | Version
- Styling: Professional, consistent formatting

### 2. Form Functionality
- All input fields must have `contenteditable="true"`
- Checkboxes properly functional
- Print-optimized styling
- Responsive design for different screen sizes

### 3. Generation Workflow
```bash
# HTML Generation Command
./generate-html.sh REC

# Output Location
ABL-Document-System/Core-Documents/REC-Records-Templates/published/
```

### 4. Dual Format Compatibility
- Same Markdown source generates both PDF and HTML
- Consistent styling across formats
- Embedded CSS in Markdown controls both outputs
- Header information extracted from YAML frontmatter

## Best Practices

### 1. Maintenance Efficiency
- **Single Source**: One Markdown file for both PDF and HTML
- **Style Control**: CSS embedded in Markdown
- **Version Management**: YAML frontmatter for metadata

### 2. Professional Standards
- Consistent font sizing and spacing
- Professional color scheme (#f0f0f0 for headers)
- Clean, minimal styling approach
- Print-optimized layout

### 3. User Experience
- Intuitive form filling
- Clear visual hierarchy
- Easy printing and saving
- Cross-browser compatibility

Reference: [ABL-REC-001-Training-Record.md](mdc:ABL-Document-System/Core-Documents/REC-Records-Templates/source/ABL-REC-001-Training-Record.md)
