---
description: 
globs: ABL-Document-System/Core-Documents/SOP-Standard-Procedures/source/*.md
alwaysApply: false
---
# SOP Workflow Guide

## SOP Creation Process

1. **Reference Master Directory**: Check [ABL-SOP-Master-Directory.md](mdc:ABL-Document-System/Core-Documents/SOP-Standard-Procedures/source/ABL-SOP-Master-Directory.md) for SOP number, CFR requirements, and implementation priority
2. **Use Template**: Copy structure from [sop_template.md](mdc:ABL-Document-System/Reference-Materials/Document-Templates/sop_template.md) (YAML frontmatter, standard sections, formatting)
3. **Template Compliance**: Strictly follow the template format and do not add extra sections or content that are not present in the template (e.g., "End of Document" statements, confidentiality notices)
4. **Ensure Compliance**: Reference 21 CFR Part 111 and 21 CFR Part 117 sections and include electronic record controls 21 CFR Part 11
5. **Reference Organization Structure**: Check [ABL_Organizational_Structure.md](mdc:ABL-Document-System/Reference-Materials/Company-Information/Organizational-Structure/ABL_Organizational_Structure.md) to align responsibilities with the company's organizational structure, but only include departments and roles that have direct involvement in the specific SOP's procedures. Avoid mechanically adding all departments - focus on actual business process participants and their reporting relationships.
6. **Responsibilities Structure**: Keep responsibilities focused on specific roles rather than broad departments. Limit key responsible persons number per SOP, consolidate under QA when appropriate. Only assign responsibilities to departments that actually participate in or are affected by the specific procedure.
7. **Scope Writing**: Write scope as a single, unified description (2.1 only) that comprehensively covers what the SOP applies to, including activities, materials, processes, and timeframe. Avoid multiple separate scope items that can create fragmentation and redundancy.
8. **Procedure Organization**: Prefer maximum 2 levels (5.1.1) by combining related steps into comprehensive statements. Use 3rd level letter format (5.1.1.a) only for critical control points or when regulatory compliance requires specific separation. Use action-oriented language, eliminate redundancy, and avoid over-granular breakdown that creates excessive sub-items.
9. **Standardized Date**: All SOPs created or updated should use the following standardized effective date: **2025-06-01**
10. **Associated Records**: Minimize record quantity while meeting regulatory requirements - keep records functionally focused with single clear purposes and logically related data elements. List only record names marked as "To be established" without detailed descriptions.
11. **References**: Only cite core CFR regulatory requirements (21 CFR Part 111, 117, 11), do not include FDA guidance documents or other non-mandatory references
12. **Directory Updates**: After SOP completion, update two locations: (a) Change status from 📋 Planned to ✅ Complete in the main SOP table, (b) Add ✅ to the corresponding item in Implementation Priority section.


