# 工作文件夹和临时资料
国内赵丽沟通/
NSF:SGS要求+国内模板/
2025.05.13 GMP认证两个产品的资料(1)/
Giao_version_SOP(Drug Standard)/
# Node.js 相关文件
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
# package-lock.json 应该被提交以确保依赖版本一致性

# 生成的PDF文件目录
SOP_pdf_version/
MMR_pdf_version/
*.pdf

# 生成的HTML文件 (REC记录模板的交互表单)
ABL-Document-System/Core-Documents/REC-Records-Templates/published/*.html
ABL-Document-System/Core-Documents/REC-Records-Templates/published/ABL-REC-*.html

# macOS 系统文件
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Windows 系统文件
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/

# Microsoft Office 临时文件
~$*
*.tmp
~*.tmp
*.temp
~*.temp

# Word 文档临时文件
~$*.doc*
~$*.dot*
~$*.rtf

# Excel 临时文件
~$*.xls*
~$*.xlsx*

# PowerPoint 临时文件
~$*.ppt*
~$*.pptx*

# 其他 Office 临时文件
*.~vsd*

# 备份文件
*.bak
*.backup
*~

# 日志文件
*.log

# 压缩文件（可选）
# *.zip
# *.rar
# *.7z

# IDE 和编辑器文件
.vscode/
.idea/
.crossnote/
*.swp
*.swo
*~

# 临时文件夹
temp/
tmp/

