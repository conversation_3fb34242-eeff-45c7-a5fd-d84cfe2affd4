# OTC活性成分检查器

一个简单的工具，用于检查成分是否为FDA认可的OTC（非处方药）活性成分，并验证其浓度是否在允许范围内。

## 功能特点

- 从预定义列表中选择OTC活性成分
- 输入成分浓度进行验证
- 显示成分是否为FDA认可的OTC活性成分
- 检查输入的浓度是否在FDA允许的范围内
- 简洁直观的用户界面

## 使用方法

1. 确保安装了Python 3.6或更高版本
2. 运行程序：
   ```
   python otc_checker.py
   ```
3. 从下拉列表中选择一个成分
4. 输入浓度值
5. 点击"检查"按钮查看结果

## 数据来源

程序使用`otc_data.json`文件存储OTC活性成分数据。该文件包含以下信息：
- 成分名称
- 允许的最小浓度
- 允许的最大浓度
- 浓度单位（%或mg等）
- 成分类别

## 自定义数据

您可以通过编辑`otc_data.json`文件来添加或修改OTC活性成分数据。文件格式如下：

```json
{
  "ingredients": [
    {
      "name": "成分名称",
      "min_concentration": 最小浓度值,
      "max_concentration": 最大浓度值,
      "unit": "单位",
      "category": "类别"
    },
    ...
  ]
}
```

## 注意事项

- 此程序仅供参考，不应替代专业医疗或监管建议
- 数据可能不完整，建议定期更新以获取最新的FDA规定 