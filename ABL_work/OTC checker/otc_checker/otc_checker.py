#!/usr/bin/env python3
import json
import tkinter as tk
from tkinter import ttk, messagebox
import os
import sys

class OTCChecker:
    def __init__(self, root):
        self.root = root
        self.root.title("OTC Active Ingredient Checker")
        self.root.geometry("700x700")
        self.root.resizable(True, True)
        
        # 加载OTC数据
        self.load_otc_data()
        
        # 创建界面
        self.create_widgets()
        
    def load_otc_data(self):
        """加载OTC活性成分数据"""
        try:
            # 获取脚本所在目录，无论是直接运行还是打包后运行
            if getattr(sys, 'frozen', False):
                # 如果是打包后的应用程序
                script_dir = os.path.dirname(sys.executable)
            else:
                # 如果是直接运行的脚本
                script_dir = os.path.dirname(os.path.abspath(__file__))
            
            data_file = os.path.join(script_dir, "otc_data.json")
            
            with open(data_file, 'r', encoding='utf-8') as f:
                self.otc_data = json.load(f)
                
            # 提取所有成分名称用于下拉列表（中英文对照）
            self.ingredient_names = []
            for item in self.otc_data["ingredients"]:
                if "name_zh" in item:
                    display_name = f"{item['name']} ({item['name_zh']})"
                else:
                    display_name = item["name"]
                self.ingredient_names.append(display_name)
            self.ingredient_names.sort()
            
            # 提取所有类别（中英文对照）
            self.categories = set()
            for item in self.otc_data["ingredients"]:
                category = item["category"]
                if "categories" in self.otc_data and category in self.otc_data["categories"]:
                    display_category = f"{category} ({self.otc_data['categories'][category]})"
                else:
                    display_category = category
                self.categories.add(display_category)
            self.categories = sorted(list(self.categories))
            
        except Exception as e:
            messagebox.showerror("Error", f"无法加载OTC数据: {str(e)}")
            self.otc_data = {"ingredients": []}
            self.ingredient_names = []
            self.categories = []
    
    def create_widgets(self):
        """创建GUI界面元素"""
        # 创建主框架
        main_frame = ttk.Frame(self.root, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 标题
        title_label = ttk.Label(main_frame, text="OTC活性成分检查器", font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=3, pady=10, sticky="w")
        
        # 说明
        desc_label = ttk.Label(main_frame, text="检查成分是否为FDA认可的OTC活性成分，并查看允许的浓度范围")
        desc_label.grid(row=1, column=0, columnspan=3, pady=5, sticky="w")
        
        # 创建选项卡控件
        tab_control = ttk.Notebook(main_frame)
        tab_control.grid(row=2, column=0, columnspan=3, pady=10, sticky="nsew")
        
        # 选项卡1：直接检查
        tab1 = ttk.Frame(tab_control)
        tab_control.add(tab1, text="直接检查")
        
        # 选项卡2：按类别浏览
        tab2 = ttk.Frame(tab_control)
        tab_control.add(tab2, text="按类别浏览")
        
        # 选项卡3：搜索
        tab3 = ttk.Frame(tab_control)
        tab_control.add(tab3, text="搜索")
        
        # 选项卡4：配方批量检查
        tab4 = ttk.Frame(tab_control)
        tab_control.add(tab4, text="配方批量检查")
        
        # 设置主框架的行列权重，使选项卡可以扩展
        main_frame.columnconfigure(0, weight=1)
        main_frame.rowconfigure(2, weight=1)
        main_frame.rowconfigure(3, weight=2)
        
        # ===== 选项卡1：直接检查 =====
        # 成分选择
        ttk.Label(tab1, text="选择成分:").grid(row=0, column=0, pady=10, sticky="w")
        self.ingredient_var = tk.StringVar()
        self.ingredient_combo = ttk.Combobox(tab1, textvariable=self.ingredient_var, width=40)
        self.ingredient_combo['values'] = self.ingredient_names
        self.ingredient_combo.grid(row=0, column=1, pady=10, sticky="w")
        
        # 查看信息按钮
        view_info_button = ttk.Button(tab1, text="查看成分信息", command=self.view_ingredient_info)
        view_info_button.grid(row=0, column=2, pady=10, padx=5)
        
        # 分隔线
        ttk.Separator(tab1, orient='horizontal').grid(row=1, column=0, columnspan=3, sticky='ew', pady=10)
        
        # 浓度检查部分
        ttk.Label(tab1, text="浓度检查（可选）:").grid(row=2, column=0, pady=10, sticky="w")
        
        # 浓度输入
        ttk.Label(tab1, text="输入浓度:").grid(row=3, column=0, pady=10, sticky="w")
        self.concentration_var = tk.StringVar()
        concentration_entry = ttk.Entry(tab1, textvariable=self.concentration_var, width=10)
        concentration_entry.grid(row=3, column=1, pady=10, sticky="w")
        
        # 单位显示
        self.unit_var = tk.StringVar()
        unit_label = ttk.Label(tab1, textvariable=self.unit_var)
        unit_label.grid(row=3, column=2, pady=10, sticky="w")
        
        # 更新单位显示当选择成分变化时
        self.ingredient_combo.bind("<<ComboboxSelected>>", self.update_unit)
        
        # 检查按钮
        check_button = ttk.Button(tab1, text="检查浓度", command=self.check_ingredient)
        check_button.grid(row=4, column=0, columnspan=2, pady=20)
        
        # ===== 选项卡2：按类别浏览 =====
        # 类别选择
        ttk.Label(tab2, text="选择类别:").grid(row=0, column=0, pady=10, sticky="w")
        self.category_var = tk.StringVar()
        self.category_combo = ttk.Combobox(tab2, textvariable=self.category_var, width=40)
        self.category_combo['values'] = self.categories
        self.category_combo.grid(row=0, column=1, pady=10, sticky="w")
        
        # 类别成分列表框
        ttk.Label(tab2, text="该类别的成分:").grid(row=1, column=0, pady=5, sticky="nw")
        self.category_listbox = tk.Listbox(tab2, width=50, height=10)
        self.category_listbox.grid(row=1, column=1, pady=5, sticky="nsew")
        
        # 添加滚动条
        category_scrollbar = ttk.Scrollbar(tab2, command=self.category_listbox.yview)
        category_scrollbar.grid(row=1, column=2, sticky="ns")
        self.category_listbox.config(yscrollcommand=category_scrollbar.set)
        
        # 更新类别成分列表当选择类别变化时
        self.category_combo.bind("<<ComboboxSelected>>", self.update_category_ingredients)
        
        # 查看成分信息按钮
        view_cat_button = ttk.Button(tab2, text="查看成分信息", command=self.view_category_ingredient)
        view_cat_button.grid(row=2, column=1, pady=10)
        
        # ===== 选项卡3：搜索 =====
        # 搜索输入
        ttk.Label(tab3, text="搜索成分:").grid(row=0, column=0, pady=10, sticky="w")
        self.search_var = tk.StringVar()
        search_entry = ttk.Entry(tab3, textvariable=self.search_var, width=30)
        search_entry.grid(row=0, column=1, pady=10, sticky="w")
        
        # 搜索按钮
        search_button = ttk.Button(tab3, text="搜索", command=self.search_ingredients)
        search_button.grid(row=0, column=2, pady=10, padx=5)
        
        # 搜索结果列表框
        ttk.Label(tab3, text="搜索结果:").grid(row=1, column=0, pady=5, sticky="nw")
        self.search_listbox = tk.Listbox(tab3, width=50, height=10)
        self.search_listbox.grid(row=1, column=1, pady=5, sticky="nsew")
        
        # 添加滚动条
        search_scrollbar = ttk.Scrollbar(tab3, command=self.search_listbox.yview)
        search_scrollbar.grid(row=1, column=2, sticky="ns")
        self.search_listbox.config(yscrollcommand=search_scrollbar.set)
        
        # 查看成分信息按钮
        view_search_button = ttk.Button(tab3, text="查看成分信息", command=self.view_search_ingredient)
        view_search_button.grid(row=2, column=1, pady=10)
        
        # ===== 选项卡4：配方批量检查 =====
        # 说明标签
        ttk.Label(tab4, text="粘贴配方成分列表 (格式: 成分名称 浓度):", font=("Arial", 10, "bold")).grid(row=0, column=0, columnspan=3, pady=(10,5), sticky="w")
        ttk.Label(tab4, text="例如: GLYCERIN 3.6").grid(row=1, column=0, columnspan=3, pady=(0,10), sticky="w")
        
        # 配方输入文本框
        self.formula_text = tk.Text(tab4, wrap=tk.WORD, width=50, height=10)
        self.formula_text.grid(row=2, column=0, columnspan=3, pady=5, sticky="nsew")
        
        # 添加滚动条
        formula_scrollbar = ttk.Scrollbar(tab4, command=self.formula_text.yview)
        formula_scrollbar.grid(row=2, column=3, sticky="ns")
        self.formula_text.config(yscrollcommand=formula_scrollbar.set)
        
        # 分析按钮
        analyze_button = ttk.Button(tab4, text="分析配方", command=self.analyze_formula)
        analyze_button.grid(row=3, column=0, columnspan=3, pady=10)
        
        # 在新窗口查看结果按钮
        self.view_window_button = ttk.Button(tab4, text="在新窗口查看结果", command=self.view_results_in_new_window)
        self.view_window_button.grid(row=4, column=0, columnspan=3, pady=5)
        self.view_window_button.config(state=tk.DISABLED)  # 初始状态为禁用
        
        # 结果显示区域（所有选项卡共用）
        result_frame = ttk.LabelFrame(main_frame, text="成分信息")
        result_frame.grid(row=3, column=0, columnspan=3, pady=10, sticky="nsew")
        
        # 结果文本区域
        self.result_text = tk.Text(result_frame, wrap=tk.WORD, width=50, height=15)
        self.result_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 添加滚动条
        scrollbar = ttk.Scrollbar(self.result_text, command=self.result_text.yview)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.result_text.config(yscrollcommand=scrollbar.set)
        
    def update_unit(self, event=None):
        """更新单位显示"""
        selected = self.ingredient_var.get()
        # 提取英文名称（去掉括号中的中文）
        if "(" in selected:
            selected = selected.split(" (")[0]
            
        for ingredient in self.otc_data["ingredients"]:
            if ingredient["name"] == selected:
                self.unit_var.set(ingredient["unit"])
                return
        self.unit_var.set("")
        
    def update_category_ingredients(self, event=None):
        """更新类别成分列表"""
        selected_category = self.category_var.get()
        
        # 提取英文类别名称（去掉括号中的中文）
        if "(" in selected_category:
            selected_category = selected_category.split(" (")[0]
        
        # 清空列表框
        self.category_listbox.delete(0, tk.END)
        
        # 添加该类别的所有成分
        for ingredient in self.otc_data["ingredients"]:
            if ingredient["category"] == selected_category:
                if "name_zh" in ingredient:
                    display_name = f"{ingredient['name']} ({ingredient['name_zh']})"
                else:
                    display_name = ingredient["name"]
                self.category_listbox.insert(tk.END, display_name)
    
    def display_ingredient_info(self, ingredient):
        """在结果区域显示成分的详细信息"""
        self.result_text.delete(1.0, tk.END)
        
        # 显示中英文名称
        if "name_zh" in ingredient:
            self.result_text.insert(tk.END, f"成分 (Ingredient): {ingredient['name']} ({ingredient['name_zh']})\n")
        else:
            self.result_text.insert(tk.END, f"成分 (Ingredient): {ingredient['name']}\n")
        
        # 显示类别（中英文）
        category = ingredient['category']
        if "categories" in self.otc_data and category in self.otc_data["categories"]:
            category_display = f"{category} ({self.otc_data['categories'][category]})"
        else:
            category_display = category
        self.result_text.insert(tk.END, f"类别 (Category): {category_display}\n")
        
        # 显示浓度范围
        self.result_text.insert(tk.END, f"允许浓度范围 (Allowed Concentration Range): {ingredient['min_concentration']} - {ingredient['max_concentration']} {ingredient['unit']}\n")
        
        # 显示FDA命令引用
        if "order_ref" in ingredient:
            self.result_text.insert(tk.END, f"FDA命令引用 (FDA Order Reference): {ingredient['order_ref']}\n")
            
        self.result_text.insert(tk.END, f"FDA认可的OTC活性成分 (FDA Approved OTC Active Ingredient): ✅\n")
    
    def get_ingredient_by_display_name(self, display_name):
        """根据显示名称（可能包含中文）获取成分对象"""
        # 提取英文名称（去掉括号中的中文）
        if "(" in display_name:
            name = display_name.split(" (")[0]
        else:
            name = display_name
            
        for ingredient in self.otc_data["ingredients"]:
            if ingredient["name"] == name:
                return ingredient
        return None
    
    def view_ingredient_info(self):
        """查看直接检查选项卡中选择的成分信息"""
        display_name = self.ingredient_var.get()
        
        if not display_name:
            messagebox.showwarning("警告", "请先选择一个成分")
            return
            
        ingredient = self.get_ingredient_by_display_name(display_name)
        if ingredient:
            self.display_ingredient_info(ingredient)
        else:
            messagebox.showwarning("警告", f"{display_name} 不在FDA认可的OTC活性成分列表中")
    
    def view_category_ingredient(self):
        """查看类别浏览选项卡中选择的成分信息"""
        try:
            # 获取选中的索引
            selected_idx = self.category_listbox.curselection()[0]
            display_name = self.category_listbox.get(selected_idx)
            
            # 获取成分信息并显示
            ingredient = self.get_ingredient_by_display_name(display_name)
            if ingredient:
                self.display_ingredient_info(ingredient)
            
        except IndexError:
            messagebox.showwarning("警告", "请先选择一个成分")
    
    def view_search_ingredient(self):
        """查看搜索结果中选择的成分信息"""
        try:
            # 获取选中的索引
            selected_idx = self.search_listbox.curselection()[0]
            display_name = self.search_listbox.get(selected_idx)
            
            # 获取成分信息并显示
            ingredient = self.get_ingredient_by_display_name(display_name)
            if ingredient:
                self.display_ingredient_info(ingredient)
            
        except IndexError:
            messagebox.showwarning("警告", "请先选择一个成分")
    
    def search_ingredients(self):
        """搜索成分"""
        search_term = self.search_var.get().lower()
        
        if not search_term:
            messagebox.showwarning("警告", "请输入搜索词")
            return
            
        # 清空列表框
        self.search_listbox.delete(0, tk.END)
        
        # 搜索并添加匹配的成分
        found = False
        matches = []
        for ingredient in self.otc_data["ingredients"]:
            # 在英文名称中搜索
            english_match = search_term in ingredient["name"].lower()
            # 在中文名称中搜索（如果有）
            chinese_match = "name_zh" in ingredient and search_term in ingredient["name_zh"].lower()
            
            if english_match or chinese_match:
                if "name_zh" in ingredient:
                    display_name = f"{ingredient['name']} ({ingredient['name_zh']})"
                else:
                    display_name = ingredient["name"]
                    
                if "categories" in self.otc_data and ingredient["category"] in self.otc_data["categories"]:
                    category_display = f"{ingredient['category']} ({self.otc_data['categories'][ingredient['category']]})"
                else:
                    category_display = ingredient["category"]
                    
                self.search_listbox.insert(tk.END, f"{display_name} - {category_display}")
                matches.append(ingredient)
                found = True
                
        if not found:
            messagebox.showinfo("搜索结果", f"没有找到包含 '{search_term}' 的成分")
        elif len(matches) == 1:
            # 如果只找到一个匹配项，直接显示详细信息
            self.display_ingredient_info(matches[0])
            # 选中列表中的项目
            self.search_listbox.selection_set(0)
        
    def check_ingredient(self):
        """检查成分和浓度"""
        display_name = self.ingredient_var.get()
        concentration_str = self.concentration_var.get()
        
        # 清空结果区域
        self.result_text.delete(1.0, tk.END)
        
        # 验证输入
        if not display_name:
            self.result_text.insert(tk.END, "请选择一个成分 (Please select an ingredient)")
            return
            
        if not concentration_str:
            self.result_text.insert(tk.END, "请输入浓度值 (Please enter a concentration value)")
            return
            
        try:
            concentration = float(concentration_str)
        except ValueError:
            self.result_text.insert(tk.END, "浓度必须是一个数字 (Concentration must be a number)")
            return
            
        # 获取成分信息
        ingredient = self.get_ingredient_by_display_name(display_name)
        if ingredient:
            min_conc = ingredient["min_concentration"]
            max_conc = ingredient["max_concentration"]
            unit = ingredient["unit"]
            
            # 获取类别显示名称
            category = ingredient["category"]
            if "categories" in self.otc_data and category in self.otc_data["categories"]:
                category_display = f"{category} ({self.otc_data['categories'][category]})"
            else:
                category_display = category
            
            # 检查浓度是否在范围内
            in_range = min_conc <= concentration <= max_conc
            
            # 显示结果
            if "name_zh" in ingredient:
                self.result_text.insert(tk.END, f"成分 (Ingredient): {ingredient['name']} ({ingredient['name_zh']})\n")
            else:
                self.result_text.insert(tk.END, f"成分 (Ingredient): {ingredient['name']}\n")
                
            self.result_text.insert(tk.END, f"类别 (Category): {category_display}\n")
            self.result_text.insert(tk.END, f"允许浓度范围 (Allowed Concentration Range): {min_conc} - {max_conc} {unit}\n")
            self.result_text.insert(tk.END, f"您的浓度 (Your Concentration): {concentration} {unit}\n\n")
            
            # 显示FDA命令引用
            if "order_ref" in ingredient:
                self.result_text.insert(tk.END, f"FDA命令引用 (FDA Order Reference): {ingredient['order_ref']}\n\n")
            
            if in_range:
                self.result_text.insert(tk.END, "✅ 该成分是FDA认可的OTC活性成分，且浓度在允许范围内\n(This ingredient is an FDA approved OTC active ingredient, and the concentration is within the allowed range)")
            else:
                self.result_text.insert(tk.END, "❌ 该成分是FDA认可的OTC活性成分，但浓度不在允许范围内\n(This ingredient is an FDA approved OTC active ingredient, but the concentration is not within the allowed range)")
        else:
            self.result_text.insert(tk.END, f"❌ 该成分未被FDA归类为OTC活性成分，可按普通化妆品/护肤品成分标准生产和使用\n(This ingredient is not classified as an FDA OTC active ingredient and can be produced and used according to regular cosmetic/skincare ingredient standards)")
            
    def analyze_formula(self):
        """分析配方中的所有成分"""
        # 获取配方文本
        formula_text = self.formula_text.get(1.0, tk.END).strip()
        
        if not formula_text:
            messagebox.showwarning("警告", "请输入配方成分列表")
            return
            
        # 清空结果区域
        self.result_text.delete(1.0, tk.END)
        
        # 解析配方成分列表
        lines = formula_text.split('\n')
        ingredients_to_check = []
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
                
            # 尝试分割成分名称和浓度
            parts = line.split()
            if len(parts) < 2:
                self.result_text.insert(tk.END, f"错误: 无法解析行 '{line}', 格式应为 '成分名称 浓度'\n\n")
                continue
                
            # 最后一个部分是浓度，其余都是成分名称
            concentration_str = parts[-1]
            ingredient_name = ' '.join(parts[:-1])
            
            try:
                concentration = float(concentration_str)
                ingredients_to_check.append((ingredient_name, concentration))
            except ValueError:
                self.result_text.insert(tk.END, f"错误: '{concentration_str}' 不是有效的浓度值\n\n")
        
        # 检查每个成分
        otc_ingredients_found = []
        non_otc_ingredients = []
        otc_out_of_range = []
        
        for name, concentration in ingredients_to_check:
            # 在OTC数据中查找成分
            found = False
            for ingredient in self.otc_data["ingredients"]:
                if ingredient["name"].lower() == name.lower():
                    found = True
                    min_conc = ingredient["min_concentration"]
                    max_conc = ingredient["max_concentration"]
                    
                    # 检查浓度是否在范围内
                    if min_conc <= concentration <= max_conc:
                        otc_ingredients_found.append((ingredient, concentration))
                    else:
                        otc_out_of_range.append((ingredient, concentration))
                    break
            
            if not found:
                non_otc_ingredients.append((name, concentration))
        
        # 显示结果摘要
        self.result_text.insert(tk.END, "===== 配方分析结果 =====\n\n")
        
        total_ingredients = len(ingredients_to_check)
        self.result_text.insert(tk.END, f"总成分数: {total_ingredients}\n")
        self.result_text.insert(tk.END, f"FDA认可的OTC活性成分 (浓度在范围内): {len(otc_ingredients_found)}\n")
        self.result_text.insert(tk.END, f"FDA认可的OTC活性成分 (浓度超出范围): {len(otc_out_of_range)}\n")
        self.result_text.insert(tk.END, f"非OTC活性成分: {len(non_otc_ingredients)}\n\n")
        
        # 显示OTC成分详情 (浓度在范围内)
        if otc_ingredients_found:
            self.result_text.insert(tk.END, "===== FDA认可的OTC活性成分 (浓度在范围内) =====\n\n")
            for ingredient, concentration in otc_ingredients_found:
                if "name_zh" in ingredient:
                    self.result_text.insert(tk.END, f"✅ {ingredient['name']} ({ingredient['name_zh']}): {concentration}% - 允许范围: {ingredient['min_concentration']} - {ingredient['max_concentration']}%\n")
                else:
                    self.result_text.insert(tk.END, f"✅ {ingredient['name']}: {concentration}% - 允许范围: {ingredient['min_concentration']} - {ingredient['max_concentration']}%\n")
                
                # 显示类别
                category = ingredient["category"]
                if "categories" in self.otc_data and category in self.otc_data["categories"]:
                    category_display = f"{category} ({self.otc_data['categories'][category]})"
                else:
                    category_display = category
                self.result_text.insert(tk.END, f"   类别: {category_display}\n")
                
                # 显示FDA命令引用
                if "order_ref" in ingredient:
                    self.result_text.insert(tk.END, f"   FDA命令引用: {ingredient['order_ref']}\n")
                self.result_text.insert(tk.END, "\n")
        
        # 显示OTC成分详情 (浓度超出范围)
        if otc_out_of_range:
            self.result_text.insert(tk.END, "===== FDA认可的OTC活性成分 (浓度超出范围) =====\n\n")
            for ingredient, concentration in otc_out_of_range:
                if "name_zh" in ingredient:
                    self.result_text.insert(tk.END, f"❌ {ingredient['name']} ({ingredient['name_zh']}): {concentration}% - 允许范围: {ingredient['min_concentration']} - {ingredient['max_concentration']}%\n")
                else:
                    self.result_text.insert(tk.END, f"❌ {ingredient['name']}: {concentration}% - 允许范围: {ingredient['min_concentration']} - {ingredient['max_concentration']}%\n")
                
                # 显示类别
                category = ingredient["category"]
                if "categories" in self.otc_data and category in self.otc_data["categories"]:
                    category_display = f"{category} ({self.otc_data['categories'][category]})"
                else:
                    category_display = category
                self.result_text.insert(tk.END, f"   类别: {category_display}\n")
                
                # 显示FDA命令引用
                if "order_ref" in ingredient:
                    self.result_text.insert(tk.END, f"   FDA命令引用: {ingredient['order_ref']}\n")
                self.result_text.insert(tk.END, "\n")
        
        # 显示非OTC成分
        if non_otc_ingredients:
            self.result_text.insert(tk.END, "===== 非OTC活性成分 =====\n\n")
            for name, concentration in non_otc_ingredients:
                self.result_text.insert(tk.END, f"ℹ️ {name}: {concentration}% - 非FDA认可的OTC活性成分\n")
            self.result_text.insert(tk.END, "\n这些成分可按普通化妆品/护肤品成分标准生产和使用。\n")
            
        # 启用"在新窗口查看结果"按钮
        self.view_window_button.config(state=tk.NORMAL)
        
    def view_results_in_new_window(self):
        """在新窗口中查看分析结果"""
        # 获取当前结果文本
        result_content = self.result_text.get(1.0, tk.END).strip()
        
        if not result_content:
            messagebox.showinfo("提示", "没有可显示的结果")
            return
            
        # 创建新窗口
        results_window = tk.Toplevel(self.root)
        results_window.title("配方分析结果")
        results_window.geometry("900x700")  # 更大的窗口尺寸
        
        # 创建主框架
        main_frame = ttk.Frame(results_window, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 标题
        title_label = ttk.Label(main_frame, text="配方分析结果", font=("Arial", 16, "bold"))
        title_label.pack(pady=(0, 10))
        
        # 创建文本区域框架
        text_frame = ttk.Frame(main_frame)
        text_frame.pack(fill=tk.BOTH, expand=True)
        
        # 创建文本区域
        result_text = tk.Text(text_frame, wrap=tk.WORD, width=80, height=30, font=("Arial", 11))
        result_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        # 添加垂直滚动条
        scrollbar = ttk.Scrollbar(text_frame, command=result_text.yview)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        result_text.config(yscrollcommand=scrollbar.set)
        
        # 复制结果内容到新窗口
        result_text.insert(tk.END, result_content)
        result_text.config(state=tk.NORMAL)  # 允许复制
        
        # 添加按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(pady=10)
        
        # 添加导出按钮
        export_button = ttk.Button(button_frame, text="导出结果", 
                                  command=lambda: self.export_results(result_content))
        export_button.pack(side=tk.LEFT, padx=5)
        
        # 添加关闭按钮
        close_button = ttk.Button(button_frame, text="关闭窗口", 
                                 command=results_window.destroy)
        close_button.pack(side=tk.LEFT, padx=5)
        
        # 设置窗口为模态，用户必须先关闭此窗口才能返回主窗口
        results_window.transient(self.root)
        results_window.grab_set()
        self.root.wait_window(results_window)
        
    def export_results(self, content):
        """导出分析结果到文本文件"""
        from tkinter import filedialog
        import datetime
        
        # 获取当前日期时间作为默认文件名
        now = datetime.datetime.now()
        default_filename = f"OTC分析结果_{now.strftime('%Y%m%d_%H%M%S')}.txt"
        
        # 打开文件保存对话框
        file_path = filedialog.asksaveasfilename(
            defaultextension=".txt",
            filetypes=[("文本文件", "*.txt"), ("所有文件", "*.*")],
            initialfile=default_filename
        )
        
        if file_path:
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                messagebox.showinfo("成功", f"结果已保存到: {file_path}")
            except Exception as e:
                messagebox.showerror("错误", f"保存文件时出错: {str(e)}")

if __name__ == "__main__":
    root = tk.Tk()
    app = OTCChecker(root)
    root.mainloop() 