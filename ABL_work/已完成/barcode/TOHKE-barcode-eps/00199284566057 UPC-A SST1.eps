%!PS-Adobe-3.0 EPSF-3.0
%%BoundingBox: 0 0 110 74
%%Title: 00199284566057 UPCA SST1
%%Creator: GS1US.BarcodeGenerator/1.1.61+fcdcbb9d05
%%CreationDate: 2025-05-09T07:52:18.5290597Z
%%Pages: 0
%%EndComments
0 74 translate
0.06 -0.06 scale
newpath
698 1152 moveto
688 1148 680 1141 677 1132 curveto
673 1119 678 1104 691 1095 curveto
695 1091 lineto
690 1087 lineto
682 1079 678 1069 681 1060 curveto
683 1053 687 1048 695 1044 curveto
701 1041 703 1041 711 1041 curveto
719 1041 721 1041 726 1044 curveto
737 1049 742 1056 741 1068 curveto
741 1076 738 1081 731 1087 curveto
726 1091 lineto
731 1094 lineto
752 1111 752 1138 729 1150 curveto
721 1154 707 1155 698 1152 curveto
closepath
723 1137 moveto
728 1134 732 1130 732 1125 curveto
733 1117 726 1108 715 1101 curveto
711 1098 lineto
706 1101 lineto
693 1109 687 1120 691 1128 curveto
696 1138 711 1142 723 1137 curveto
closepath
718 1079 moveto
726 1074 730 1066 725 1061 curveto
722 1057 718 1055 711 1056 curveto
701 1056 695 1060 695 1066 curveto
694 1071 698 1076 704 1079 curveto
710 1083 711 1083 718 1079 curveto
closepath
fill
newpath
985 1152 moveto
983 1150 983 1148 983 1146 curveto
983 1141 985 1140 997 1139 curveto
1019 1137 1030 1128 1030 1113 curveto
1030 1104 1027 1099 1020 1096 curveto
1015 1093 1013 1093 1002 1093 curveto
986 1092 985 1092 986 1081 curveto
986 1076 986 1066 986 1060 curveto
988 1042 986 1043 1016 1043 curveto
1039 1043 lineto
1041 1045 lineto
1044 1048 1044 1051 1042 1055 curveto
1040 1057 1039 1057 1021 1057 curveto
999 1058 1001 1057 1001 1070 curveto
1001 1078 lineto
1009 1079 lineto
1030 1080 1043 1092 1044 1111 curveto
1046 1134 1027 1151 997 1153 curveto
989 1154 988 1154 985 1152 curveto
closepath
fill
newpath
1112 1152 moveto
1092 1147 1082 1128 1087 1103 curveto
1090 1089 1104 1067 1123 1047 curveto
1128 1043 1130 1042 1132 1042 curveto
1136 1042 1140 1045 1140 1049 curveto
1140 1050 1136 1055 1131 1060 curveto
1122 1070 1113 1081 1114 1082 curveto
1114 1082 1117 1082 1122 1082 curveto
1137 1082 1147 1088 1154 1100 curveto
1157 1107 1157 1107 1157 1117 curveto
1157 1126 1157 1129 1155 1133 curveto
1152 1140 1142 1149 1136 1151 curveto
1130 1153 1117 1154 1112 1152 curveto
closepath
1133 1136 moveto
1135 1135 1138 1133 1139 1131 curveto
1142 1128 1142 1126 1142 1118 curveto
1143 1111 1142 1109 1141 1106 curveto
1137 1100 1132 1097 1123 1097 curveto
1109 1096 1102 1102 1101 1117 curveto
1100 1124 1101 1128 1105 1133 curveto
1111 1139 1124 1141 1133 1136 curveto
closepath
fill
newpath
1219 1152 moveto
1199 1147 1189 1128 1194 1103 curveto
1197 1089 1211 1067 1230 1047 curveto
1235 1043 1237 1042 1239 1042 curveto
1243 1042 1247 1045 1247 1049 curveto
1247 1050 1243 1055 1238 1060 curveto
1229 1070 1220 1081 1221 1082 curveto
1221 1082 1224 1082 1229 1082 curveto
1244 1082 1254 1088 1261 1100 curveto
1264 1107 1264 1107 1264 1117 curveto
1264 1126 1264 1129 1262 1133 curveto
1259 1140 1249 1149 1243 1151 curveto
1237 1153 1224 1154 1219 1152 curveto
closepath
1240 1136 moveto
1242 1135 1245 1133 1246 1131 curveto
1249 1128 1249 1126 1249 1118 curveto
1250 1111 1249 1109 1248 1106 curveto
1244 1100 1239 1097 1230 1097 curveto
1216 1096 1209 1102 1208 1117 curveto
1207 1124 1208 1128 1212 1133 curveto
1218 1139 1231 1141 1240 1136 curveto
closepath
fill
newpath
1326 1153 moveto
1313 1149 1306 1140 1303 1125 curveto
1300 1114 1300 1078 1303 1068 curveto
1305 1059 1309 1053 1313 1048 curveto
1320 1042 1323 1041 1337 1041 curveto
1350 1041 1353 1042 1360 1049 curveto
1370 1057 1373 1073 1372 1103 curveto
1372 1126 1369 1135 1361 1144 curveto
1356 1150 1350 1153 1338 1153 curveto
1333 1153 1327 1153 1326 1153 curveto
closepath
1348 1137 moveto
1352 1134 1355 1128 1356 1121 curveto
1358 1113 1358 1080 1356 1073 curveto
1354 1059 1350 1056 1337 1056 curveto
1323 1056 1320 1059 1317 1072 curveto
1315 1081 1315 1113 1317 1121 curveto
1318 1128 1322 1135 1325 1137 curveto
1330 1139 1344 1139 1348 1137 curveto
closepath
fill
newpath
1414 1152 moveto
1412 1150 1412 1148 1412 1146 curveto
1412 1141 1414 1140 1426 1139 curveto
1448 1137 1459 1128 1459 1113 curveto
1459 1104 1456 1099 1449 1096 curveto
1444 1093 1442 1093 1431 1093 curveto
1415 1092 1414 1092 1415 1081 curveto
1415 1076 1415 1066 1415 1060 curveto
1417 1042 1415 1043 1445 1043 curveto
1468 1043 lineto
1470 1045 lineto
1473 1048 1473 1051 1471 1055 curveto
1469 1057 1468 1057 1450 1057 curveto
1428 1058 1430 1057 1430 1070 curveto
1430 1078 lineto
1438 1079 lineto
1459 1080 1472 1092 1473 1111 curveto
1475 1134 1456 1151 1426 1153 curveto
1418 1154 1417 1154 1414 1152 curveto
closepath
fill
newpath
1736 1152 moveto
1731 1150 1730 1145 1732 1129 curveto
1734 1110 1739 1097 1752 1081 curveto
1757 1075 1763 1067 1765 1064 curveto
1769 1058 lineto
1743 1058 lineto
1717 1057 lineto
1715 1054 lineto
1712 1051 1712 1048 1715 1045 curveto
1717 1043 lineto
1749 1043 lineto
1780 1043 lineto
1782 1045 lineto
1785 1047 1785 1048 1784 1053 curveto
1783 1061 1778 1071 1767 1085 curveto
1752 1105 1747 1115 1746 1137 curveto
1745 1146 1744 1149 1743 1150 curveto
1741 1153 1739 1153 1736 1152 curveto
closepath
fill
newpath
76 1151 moveto
74 1150 74 1148 74 1105 curveto
73 1060 lineto
64 1068 lineto
58 1073 53 1077 52 1077 curveto
46 1079 42 1073 43 1068 curveto
44 1067 51 1060 58 1054 curveto
73 1042 lineto
78 1042 lineto
82 1042 84 1042 86 1044 curveto
87 1045 88 1047 88 1097 curveto
88 1147 87 1149 86 1151 curveto
83 1153 79 1153 76 1151 curveto
closepath
fill
newpath
374 1151 moveto
371 1147 372 1144 380 1135 curveto
386 1128 399 1113 398 1112 curveto
398 1112 394 1112 389 1112 curveto
381 1112 379 1111 374 1109 curveto
363 1104 357 1095 355 1083 curveto
352 1067 361 1051 376 1044 curveto
381 1041 383 1041 391 1041 curveto
398 1041 400 1042 405 1044 curveto
428 1055 433 1082 415 1113 curveto
408 1125 400 1135 391 1144 curveto
384 1152 383 1153 379 1153 curveto
377 1153 375 1152 374 1151 curveto
closepath
402 1096 moveto
408 1094 410 1089 411 1081 curveto
412 1065 405 1056 390 1056 curveto
378 1055 370 1064 369 1076 curveto
368 1084 372 1092 378 1095 curveto
383 1098 396 1098 402 1096 curveto
closepath
fill
newpath
481 1151 moveto
478 1147 479 1144 487 1135 curveto
493 1128 506 1113 505 1112 curveto
505 1112 501 1112 496 1112 curveto
488 1112 486 1111 481 1109 curveto
470 1104 464 1095 462 1083 curveto
459 1067 468 1051 483 1044 curveto
488 1041 490 1041 498 1041 curveto
505 1041 507 1042 512 1044 curveto
535 1055 540 1082 522 1113 curveto
515 1125 507 1135 498 1144 curveto
491 1152 490 1153 486 1153 curveto
484 1153 482 1152 481 1151 curveto
closepath
509 1096 moveto
515 1094 517 1089 518 1081 curveto
519 1065 512 1056 497 1056 curveto
485 1055 477 1064 476 1076 curveto
475 1084 479 1092 485 1095 curveto
490 1098 503 1098 509 1096 curveto
closepath
fill
newpath
827 1151 moveto
826 1149 825 1146 825 1139 curveto
824 1129 lineto
807 1129 lineto
788 1129 784 1128 783 1123 curveto
781 1117 783 1111 799 1078 curveto
807 1060 815 1044 816 1043 curveto
820 1039 828 1042 828 1048 curveto
828 1049 821 1064 813 1081 curveto
805 1098 799 1112 799 1113 curveto
798 1113 804 1114 811 1114 curveto
824 1114 lineto
825 1104 lineto
826 1093 827 1091 832 1091 curveto
838 1091 840 1093 840 1104 curveto
840 1114 lineto
844 1114 lineto
850 1114 852 1115 854 1118 curveto
856 1124 851 1129 843 1129 curveto
840 1129 lineto
840 1139 lineto
840 1147 839 1149 838 1151 curveto
835 1153 829 1153 827 1151 curveto
closepath
fill
newpath
576 1150 moveto
572 1149 571 1143 572 1133 curveto
574 1117 580 1109 599 1097 curveto
605 1093 612 1088 614 1086 curveto
624 1076 624 1065 614 1058 curveto
612 1056 610 1056 602 1056 curveto
592 1056 591 1056 585 1059 curveto
578 1063 577 1063 575 1061 curveto
571 1060 570 1055 571 1052 curveto
573 1049 585 1043 594 1041 curveto
614 1038 631 1047 635 1064 curveto
639 1080 631 1093 608 1108 curveto
593 1117 587 1126 587 1135 curveto
587 1138 lineto
610 1138 lineto
631 1138 633 1138 635 1139 curveto
637 1142 637 1147 634 1149 curveto
631 1151 630 1151 605 1151 curveto
588 1151 577 1151 576 1150 curveto
closepath
fill
newpath
153 540 moveto
153 0 lineto
161 0 lineto
169 0 lineto
169 540 lineto
169 1080 lineto
161 1080 lineto
153 1080 lineto
153 540 lineto
closepath
fill
newpath
185 540 moveto
185 0 lineto
193 0 lineto
201 0 lineto
201 540 lineto
201 1080 lineto
193 1080 lineto
185 1080 lineto
185 540 lineto
closepath
fill
newpath
233 540 moveto
233 0 lineto
249 0 lineto
265 0 lineto
265 540 lineto
265 1080 lineto
249 1080 lineto
233 1080 lineto
233 540 lineto
closepath
fill
newpath
297 540 moveto
297 0 lineto
305 0 lineto
313 0 lineto
313 540 lineto
313 1080 lineto
305 1080 lineto
297 1080 lineto
297 540 lineto
closepath
fill
newpath
889 540 moveto
889 0 lineto
897 0 lineto
905 0 lineto
905 540 lineto
905 1080 lineto
897 1080 lineto
889 1080 lineto
889 540 lineto
closepath
fill
newpath
921 540 moveto
921 0 lineto
929 0 lineto
937 0 lineto
937 540 lineto
937 1080 lineto
929 1080 lineto
921 1080 lineto
921 540 lineto
closepath
fill
newpath
1513 540 moveto
1513 0 lineto
1521 0 lineto
1529 0 lineto
1529 540 lineto
1529 1080 lineto
1521 1080 lineto
1513 1080 lineto
1513 540 lineto
closepath
fill
newpath
1577 540 moveto
1577 0 lineto
1585 0 lineto
1593 0 lineto
1593 540 lineto
1593 1080 lineto
1585 1080 lineto
1577 1080 lineto
1577 540 lineto
closepath
fill
newpath
1625 540 moveto
1625 0 lineto
1633 0 lineto
1641 0 lineto
1641 540 lineto
1641 1080 lineto
1633 1080 lineto
1625 1080 lineto
1625 540 lineto
closepath
fill
newpath
1657 540 moveto
1657 0 lineto
1665 0 lineto
1673 0 lineto
1673 540 lineto
1673 1080 lineto
1665 1080 lineto
1657 1080 lineto
1657 540 lineto
closepath
fill
newpath
361 509 moveto
361 0 lineto
369 0 lineto
377 0 lineto
377 509 lineto
377 1018 lineto
369 1018 lineto
361 1018 lineto
361 509 lineto
closepath
fill
newpath
393 509 moveto
393 0 lineto
409 0 lineto
425 0 lineto
425 509 lineto
425 1018 lineto
409 1018 lineto
393 1018 lineto
393 509 lineto
closepath
fill
newpath
473 509 moveto
473 0 lineto
481 0 lineto
489 0 lineto
489 509 lineto
489 1018 lineto
481 1018 lineto
473 1018 lineto
473 509 lineto
closepath
fill
newpath
505 509 moveto
505 0 lineto
521 0 lineto
537 0 lineto
537 509 lineto
537 1018 lineto
521 1018 lineto
505 1018 lineto
505 509 lineto
closepath
fill
newpath
569 509 moveto
569 0 lineto
577 0 lineto
585 0 lineto
585 509 lineto
585 1018 lineto
577 1018 lineto
569 1018 lineto
569 509 lineto
closepath
fill
newpath
617 509 moveto
617 0 lineto
633 0 lineto
649 0 lineto
649 509 lineto
649 1018 lineto
633 1018 lineto
617 1018 lineto
617 509 lineto
closepath
fill
newpath
665 509 moveto
665 0 lineto
681 0 lineto
697 0 lineto
697 509 lineto
697 1018 lineto
681 1018 lineto
665 1018 lineto
665 509 lineto
closepath
fill
newpath
713 509 moveto
713 0 lineto
737 0 lineto
761 0 lineto
761 509 lineto
761 1018 lineto
737 1018 lineto
713 1018 lineto
713 509 lineto
closepath
fill
newpath
777 509 moveto
777 0 lineto
785 0 lineto
793 0 lineto
793 509 lineto
793 1018 lineto
785 1018 lineto
777 1018 lineto
777 509 lineto
closepath
fill
newpath
841 509 moveto
841 0 lineto
857 0 lineto
873 0 lineto
873 509 lineto
873 1018 lineto
857 1018 lineto
841 1018 lineto
841 509 lineto
closepath
fill
newpath
953 509 moveto
953 0 lineto
961 0 lineto
969 0 lineto
969 509 lineto
969 1018 lineto
961 1018 lineto
953 1018 lineto
953 509 lineto
closepath
fill
newpath
1001 509 moveto
1001 0 lineto
1025 0 lineto
1049 0 lineto
1049 509 lineto
1049 1018 lineto
1025 1018 lineto
1001 1018 lineto
1001 509 lineto
closepath
fill
newpath
1065 509 moveto
1065 0 lineto
1073 0 lineto
1081 0 lineto
1081 509 lineto
1081 1018 lineto
1073 1018 lineto
1065 1018 lineto
1065 509 lineto
closepath
fill
newpath
1097 509 moveto
1097 0 lineto
1105 0 lineto
1113 0 lineto
1113 509 lineto
1113 1018 lineto
1105 1018 lineto
1097 1018 lineto
1097 509 lineto
closepath
fill
newpath
1177 509 moveto
1177 0 lineto
1185 0 lineto
1193 0 lineto
1193 509 lineto
1193 1018 lineto
1185 1018 lineto
1177 1018 lineto
1177 509 lineto
closepath
fill
newpath
1209 509 moveto
1209 0 lineto
1217 0 lineto
1225 0 lineto
1225 509 lineto
1225 1018 lineto
1217 1018 lineto
1209 1018 lineto
1209 509 lineto
closepath
fill
newpath
1289 509 moveto
1289 0 lineto
1313 0 lineto
1337 0 lineto
1337 509 lineto
1337 1018 lineto
1313 1018 lineto
1289 1018 lineto
1289 509 lineto
closepath
fill
newpath
1369 509 moveto
1369 0 lineto
1377 0 lineto
1385 0 lineto
1385 509 lineto
1385 1018 lineto
1377 1018 lineto
1369 1018 lineto
1369 509 lineto
closepath
fill
newpath
1401 509 moveto
1401 0 lineto
1409 0 lineto
1417 0 lineto
1417 509 lineto
1417 1018 lineto
1409 1018 lineto
1401 1018 lineto
1401 509 lineto
closepath
fill
newpath
1449 509 moveto
1449 0 lineto
1473 0 lineto
1497 0 lineto
1497 509 lineto
1497 1018 lineto
1473 1018 lineto
1449 1018 lineto
1449 509 lineto
closepath
fill
showpage
%%EOF
