%!PS-Adobe-3.0 EPSF-3.0
%%BoundingBox: 0 0 110 74
%%Title: 00199284176676 UPCA SST1
%%Creator: GS1US.BarcodeGenerator/1.1.61+fcdcbb9d05
%%CreationDate: 2025-05-09T07:51:56.3162716Z
%%Pages: 0
%%EndComments
0 74 translate
0.06 -0.06 scale
newpath
698 1152 moveto
688 1148 680 1141 677 1132 curveto
673 1119 678 1104 691 1095 curveto
695 1091 lineto
690 1087 lineto
682 1079 678 1069 681 1060 curveto
683 1053 687 1048 695 1044 curveto
701 1041 703 1041 711 1041 curveto
719 1041 721 1041 726 1044 curveto
737 1049 742 1056 741 1068 curveto
741 1076 738 1081 731 1087 curveto
726 1091 lineto
731 1094 lineto
752 1111 752 1138 729 1150 curveto
721 1154 707 1155 698 1152 curveto
closepath
723 1137 moveto
728 1134 732 1130 732 1125 curveto
733 1117 726 1108 715 1101 curveto
711 1098 lineto
706 1101 lineto
693 1109 687 1120 691 1128 curveto
696 1138 711 1142 723 1137 curveto
closepath
718 1079 moveto
726 1074 730 1066 725 1061 curveto
722 1057 718 1055 711 1056 curveto
701 1056 695 1060 695 1066 curveto
694 1071 698 1076 704 1079 curveto
710 1083 711 1083 718 1079 curveto
closepath
fill
newpath
1109 1152 moveto
1104 1150 1103 1145 1105 1129 curveto
1107 1110 1112 1097 1125 1081 curveto
1130 1075 1136 1067 1138 1064 curveto
1142 1058 lineto
1116 1058 lineto
1090 1057 lineto
1088 1054 lineto
1085 1051 1085 1048 1088 1045 curveto
1090 1043 lineto
1122 1043 lineto
1153 1043 lineto
1155 1045 lineto
1158 1047 1158 1048 1157 1053 curveto
1156 1061 1151 1071 1140 1085 curveto
1125 1105 1120 1115 1119 1137 curveto
1118 1146 1117 1149 1116 1150 curveto
1114 1153 1112 1153 1109 1152 curveto
closepath
fill
newpath
1219 1152 moveto
1199 1147 1189 1128 1194 1103 curveto
1197 1089 1211 1067 1230 1047 curveto
1235 1043 1237 1042 1239 1042 curveto
1243 1042 1247 1045 1247 1049 curveto
1247 1050 1243 1055 1238 1060 curveto
1229 1070 1220 1081 1221 1082 curveto
1221 1082 1224 1082 1229 1082 curveto
1244 1082 1254 1088 1261 1100 curveto
1264 1107 1264 1107 1264 1117 curveto
1264 1126 1264 1129 1262 1133 curveto
1259 1140 1249 1149 1243 1151 curveto
1237 1153 1224 1154 1219 1152 curveto
closepath
1240 1136 moveto
1242 1135 1245 1133 1246 1131 curveto
1249 1128 1249 1126 1249 1118 curveto
1250 1111 1249 1109 1248 1106 curveto
1244 1100 1239 1097 1230 1097 curveto
1216 1096 1209 1102 1208 1117 curveto
1207 1124 1208 1128 1212 1133 curveto
1218 1139 1231 1141 1240 1136 curveto
closepath
fill
newpath
1327 1152 moveto
1307 1147 1297 1128 1302 1103 curveto
1305 1089 1319 1067 1338 1047 curveto
1343 1043 1345 1042 1347 1042 curveto
1351 1042 1355 1045 1355 1049 curveto
1355 1050 1351 1055 1346 1060 curveto
1337 1070 1328 1081 1329 1082 curveto
1329 1082 1332 1082 1337 1082 curveto
1352 1082 1362 1088 1369 1100 curveto
1372 1107 1372 1107 1372 1117 curveto
1372 1126 1372 1129 1370 1133 curveto
1367 1140 1357 1149 1351 1151 curveto
1345 1153 1332 1154 1327 1152 curveto
closepath
1348 1136 moveto
1350 1135 1353 1133 1354 1131 curveto
1357 1128 1357 1126 1357 1118 curveto
1358 1111 1357 1109 1356 1106 curveto
1352 1100 1347 1097 1338 1097 curveto
1324 1096 1317 1102 1316 1117 curveto
1315 1124 1316 1128 1320 1133 curveto
1326 1139 1339 1141 1348 1136 curveto
closepath
fill
newpath
1431 1152 moveto
1426 1150 1425 1145 1427 1129 curveto
1429 1110 1434 1097 1447 1081 curveto
1452 1075 1458 1067 1460 1064 curveto
1464 1058 lineto
1438 1058 lineto
1412 1057 lineto
1410 1054 lineto
1407 1051 1407 1048 1410 1045 curveto
1412 1043 lineto
1444 1043 lineto
1475 1043 lineto
1477 1045 lineto
1480 1047 1480 1048 1479 1053 curveto
1478 1061 1473 1071 1462 1085 curveto
1447 1105 1442 1115 1441 1137 curveto
1440 1146 1439 1149 1438 1150 curveto
1436 1153 1434 1153 1431 1152 curveto
closepath
fill
newpath
1739 1152 moveto
1719 1147 1709 1128 1714 1103 curveto
1717 1089 1731 1067 1750 1047 curveto
1755 1043 1757 1042 1759 1042 curveto
1763 1042 1767 1045 1767 1049 curveto
1767 1050 1763 1055 1758 1060 curveto
1749 1070 1740 1081 1741 1082 curveto
1741 1082 1744 1082 1749 1082 curveto
1764 1082 1774 1088 1781 1100 curveto
1784 1107 1784 1107 1784 1117 curveto
1784 1126 1784 1129 1782 1133 curveto
1779 1140 1769 1149 1763 1151 curveto
1757 1153 1744 1154 1739 1152 curveto
closepath
1760 1136 moveto
1762 1135 1765 1133 1766 1131 curveto
1769 1128 1769 1126 1769 1118 curveto
1770 1111 1769 1109 1768 1106 curveto
1764 1100 1759 1097 1750 1097 curveto
1736 1096 1729 1102 1728 1117 curveto
1727 1124 1728 1128 1732 1133 curveto
1738 1139 1751 1141 1760 1136 curveto
closepath
fill
newpath
76 1151 moveto
74 1150 74 1148 74 1105 curveto
73 1060 lineto
64 1068 lineto
58 1073 53 1077 52 1077 curveto
46 1079 42 1073 43 1068 curveto
44 1067 51 1060 58 1054 curveto
73 1042 lineto
78 1042 lineto
82 1042 84 1042 86 1044 curveto
87 1045 88 1047 88 1097 curveto
88 1147 87 1149 86 1151 curveto
83 1153 79 1153 76 1151 curveto
closepath
fill
newpath
374 1151 moveto
371 1147 372 1144 380 1135 curveto
386 1128 399 1113 398 1112 curveto
398 1112 394 1112 389 1112 curveto
381 1112 379 1111 374 1109 curveto
363 1104 357 1095 355 1083 curveto
352 1067 361 1051 376 1044 curveto
381 1041 383 1041 391 1041 curveto
398 1041 400 1042 405 1044 curveto
428 1055 433 1082 415 1113 curveto
408 1125 400 1135 391 1144 curveto
384 1152 383 1153 379 1153 curveto
377 1153 375 1152 374 1151 curveto
closepath
402 1096 moveto
408 1094 410 1089 411 1081 curveto
412 1065 405 1056 390 1056 curveto
378 1055 370 1064 369 1076 curveto
368 1084 372 1092 378 1095 curveto
383 1098 396 1098 402 1096 curveto
closepath
fill
newpath
481 1151 moveto
478 1147 479 1144 487 1135 curveto
493 1128 506 1113 505 1112 curveto
505 1112 501 1112 496 1112 curveto
488 1112 486 1111 481 1109 curveto
470 1104 464 1095 462 1083 curveto
459 1067 468 1051 483 1044 curveto
488 1041 490 1041 498 1041 curveto
505 1041 507 1042 512 1044 curveto
535 1055 540 1082 522 1113 curveto
515 1125 507 1135 498 1144 curveto
491 1152 490 1153 486 1153 curveto
484 1153 482 1152 481 1151 curveto
closepath
509 1096 moveto
515 1094 517 1089 518 1081 curveto
519 1065 512 1056 497 1056 curveto
485 1055 477 1064 476 1076 curveto
475 1084 479 1092 485 1095 curveto
490 1098 503 1098 509 1096 curveto
closepath
fill
newpath
827 1151 moveto
826 1149 825 1146 825 1139 curveto
824 1129 lineto
807 1129 lineto
788 1129 784 1128 783 1123 curveto
781 1117 783 1111 799 1078 curveto
807 1060 815 1044 816 1043 curveto
820 1039 828 1042 828 1048 curveto
828 1049 821 1064 813 1081 curveto
805 1098 799 1112 799 1113 curveto
798 1113 804 1114 811 1114 curveto
824 1114 lineto
825 1104 lineto
826 1093 827 1091 832 1091 curveto
838 1091 840 1093 840 1104 curveto
840 1114 lineto
844 1114 lineto
850 1114 852 1115 854 1118 curveto
856 1124 851 1129 843 1129 curveto
840 1129 lineto
840 1139 lineto
840 1147 839 1149 838 1151 curveto
835 1153 829 1153 827 1151 curveto
closepath
fill
newpath
1016 1151 moveto
1014 1150 1014 1148 1014 1105 curveto
1013 1060 lineto
1004 1068 lineto
998 1073 993 1077 992 1077 curveto
986 1079 982 1073 983 1068 curveto
984 1067 991 1060 998 1054 curveto
1013 1042 lineto
1018 1042 lineto
1022 1042 1024 1042 1026 1044 curveto
1027 1045 1028 1047 1028 1097 curveto
1028 1147 1027 1149 1026 1151 curveto
1023 1153 1019 1153 1016 1151 curveto
closepath
fill
newpath
576 1150 moveto
572 1149 571 1143 572 1133 curveto
574 1117 580 1109 599 1097 curveto
605 1093 612 1088 614 1086 curveto
624 1076 624 1065 614 1058 curveto
612 1056 610 1056 602 1056 curveto
592 1056 591 1056 585 1059 curveto
578 1063 577 1063 575 1061 curveto
571 1060 570 1055 571 1052 curveto
573 1049 585 1043 594 1041 curveto
614 1038 631 1047 635 1064 curveto
639 1080 631 1093 608 1108 curveto
593 1117 587 1126 587 1135 curveto
587 1138 lineto
610 1138 lineto
631 1138 633 1138 635 1139 curveto
637 1142 637 1147 634 1149 curveto
631 1151 630 1151 605 1151 curveto
588 1151 577 1151 576 1150 curveto
closepath
fill
newpath
153 540 moveto
153 0 lineto
161 0 lineto
169 0 lineto
169 540 lineto
169 1080 lineto
161 1080 lineto
153 1080 lineto
153 540 lineto
closepath
fill
newpath
185 540 moveto
185 0 lineto
193 0 lineto
201 0 lineto
201 540 lineto
201 1080 lineto
193 1080 lineto
185 1080 lineto
185 540 lineto
closepath
fill
newpath
233 540 moveto
233 0 lineto
249 0 lineto
265 0 lineto
265 540 lineto
265 1080 lineto
249 1080 lineto
233 1080 lineto
233 540 lineto
closepath
fill
newpath
297 540 moveto
297 0 lineto
305 0 lineto
313 0 lineto
313 540 lineto
313 1080 lineto
305 1080 lineto
297 1080 lineto
297 540 lineto
closepath
fill
newpath
889 540 moveto
889 0 lineto
897 0 lineto
905 0 lineto
905 540 lineto
905 1080 lineto
897 1080 lineto
889 1080 lineto
889 540 lineto
closepath
fill
newpath
921 540 moveto
921 0 lineto
929 0 lineto
937 0 lineto
937 540 lineto
937 1080 lineto
929 1080 lineto
921 1080 lineto
921 540 lineto
closepath
fill
newpath
1513 540 moveto
1513 0 lineto
1521 0 lineto
1529 0 lineto
1529 540 lineto
1529 1080 lineto
1521 1080 lineto
1513 1080 lineto
1513 540 lineto
closepath
fill
newpath
1545 540 moveto
1545 0 lineto
1553 0 lineto
1561 0 lineto
1561 540 lineto
1561 1080 lineto
1553 1080 lineto
1545 1080 lineto
1545 540 lineto
closepath
fill
newpath
1625 540 moveto
1625 0 lineto
1633 0 lineto
1641 0 lineto
1641 540 lineto
1641 1080 lineto
1633 1080 lineto
1625 1080 lineto
1625 540 lineto
closepath
fill
newpath
1657 540 moveto
1657 0 lineto
1665 0 lineto
1673 0 lineto
1673 540 lineto
1673 1080 lineto
1665 1080 lineto
1657 1080 lineto
1657 540 lineto
closepath
fill
newpath
361 509 moveto
361 0 lineto
369 0 lineto
377 0 lineto
377 509 lineto
377 1018 lineto
369 1018 lineto
361 1018 lineto
361 509 lineto
closepath
fill
newpath
393 509 moveto
393 0 lineto
409 0 lineto
425 0 lineto
425 509 lineto
425 1018 lineto
409 1018 lineto
393 1018 lineto
393 509 lineto
closepath
fill
newpath
473 509 moveto
473 0 lineto
481 0 lineto
489 0 lineto
489 509 lineto
489 1018 lineto
481 1018 lineto
473 1018 lineto
473 509 lineto
closepath
fill
newpath
505 509 moveto
505 0 lineto
521 0 lineto
537 0 lineto
537 509 lineto
537 1018 lineto
521 1018 lineto
505 1018 lineto
505 509 lineto
closepath
fill
newpath
569 509 moveto
569 0 lineto
577 0 lineto
585 0 lineto
585 509 lineto
585 1018 lineto
577 1018 lineto
569 1018 lineto
569 509 lineto
closepath
fill
newpath
617 509 moveto
617 0 lineto
633 0 lineto
649 0 lineto
649 509 lineto
649 1018 lineto
633 1018 lineto
617 1018 lineto
617 509 lineto
closepath
fill
newpath
665 509 moveto
665 0 lineto
681 0 lineto
697 0 lineto
697 509 lineto
697 1018 lineto
681 1018 lineto
665 1018 lineto
665 509 lineto
closepath
fill
newpath
713 509 moveto
713 0 lineto
737 0 lineto
761 0 lineto
761 509 lineto
761 1018 lineto
737 1018 lineto
713 1018 lineto
713 509 lineto
closepath
fill
newpath
777 509 moveto
777 0 lineto
785 0 lineto
793 0 lineto
793 509 lineto
793 1018 lineto
785 1018 lineto
777 1018 lineto
777 509 lineto
closepath
fill
newpath
841 509 moveto
841 0 lineto
857 0 lineto
873 0 lineto
873 509 lineto
873 1018 lineto
857 1018 lineto
841 1018 lineto
841 509 lineto
closepath
fill
newpath
953 509 moveto
953 0 lineto
969 0 lineto
985 0 lineto
985 509 lineto
985 1018 lineto
969 1018 lineto
953 1018 lineto
953 509 lineto
closepath
fill
newpath
1017 509 moveto
1017 0 lineto
1033 0 lineto
1049 0 lineto
1049 509 lineto
1049 1018 lineto
1033 1018 lineto
1017 1018 lineto
1017 509 lineto
closepath
fill
newpath
1065 509 moveto
1065 0 lineto
1073 0 lineto
1081 0 lineto
1081 509 lineto
1081 1018 lineto
1073 1018 lineto
1065 1018 lineto
1065 509 lineto
closepath
fill
newpath
1129 509 moveto
1129 0 lineto
1137 0 lineto
1145 0 lineto
1145 509 lineto
1145 1018 lineto
1137 1018 lineto
1129 1018 lineto
1129 509 lineto
closepath
fill
newpath
1177 509 moveto
1177 0 lineto
1185 0 lineto
1193 0 lineto
1193 509 lineto
1193 1018 lineto
1185 1018 lineto
1177 1018 lineto
1177 509 lineto
closepath
fill
newpath
1209 509 moveto
1209 0 lineto
1217 0 lineto
1225 0 lineto
1225 509 lineto
1225 1018 lineto
1217 1018 lineto
1209 1018 lineto
1209 509 lineto
closepath
fill
newpath
1289 509 moveto
1289 0 lineto
1297 0 lineto
1305 0 lineto
1305 509 lineto
1305 1018 lineto
1297 1018 lineto
1289 1018 lineto
1289 509 lineto
closepath
fill
newpath
1321 509 moveto
1321 0 lineto
1329 0 lineto
1337 0 lineto
1337 509 lineto
1337 1018 lineto
1329 1018 lineto
1321 1018 lineto
1321 509 lineto
closepath
fill
newpath
1401 509 moveto
1401 0 lineto
1409 0 lineto
1417 0 lineto
1417 509 lineto
1417 1018 lineto
1409 1018 lineto
1401 1018 lineto
1401 509 lineto
closepath
fill
newpath
1465 509 moveto
1465 0 lineto
1473 0 lineto
1481 0 lineto
1481 509 lineto
1481 1018 lineto
1473 1018 lineto
1465 1018 lineto
1465 509 lineto
closepath
fill
showpage
%%EOF
