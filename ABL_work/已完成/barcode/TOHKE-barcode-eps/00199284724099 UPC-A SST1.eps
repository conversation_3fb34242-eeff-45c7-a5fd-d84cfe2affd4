%!PS-Adobe-3.0 EPSF-3.0
%%BoundingBox: 0 0 110 74
%%Title: 00199284724099 UPCA SST1
%%Creator: GS1US.BarcodeGenerator/1.1.61+fcdcbb9d05
%%CreationDate: 2025-05-09T07:51:17.8205791Z
%%Pages: 0
%%EndComments
0 74 translate
0.06 -0.06 scale
newpath
698 1152 moveto
688 1148 680 1141 677 1132 curveto
673 1119 678 1104 691 1095 curveto
695 1091 lineto
690 1087 lineto
682 1079 678 1069 681 1060 curveto
683 1053 687 1048 695 1044 curveto
701 1041 703 1041 711 1041 curveto
719 1041 721 1041 726 1044 curveto
737 1049 742 1056 741 1068 curveto
741 1076 738 1081 731 1087 curveto
726 1091 lineto
731 1094 lineto
752 1111 752 1138 729 1150 curveto
721 1154 707 1155 698 1152 curveto
closepath
723 1137 moveto
728 1134 732 1130 732 1125 curveto
733 1117 726 1108 715 1101 curveto
711 1098 lineto
706 1101 lineto
693 1109 687 1120 691 1128 curveto
696 1138 711 1142 723 1137 curveto
closepath
718 1079 moveto
726 1074 730 1066 725 1061 curveto
722 1057 718 1055 711 1056 curveto
701 1056 695 1060 695 1066 curveto
694 1071 698 1076 704 1079 curveto
710 1083 711 1083 718 1079 curveto
closepath
fill
newpath
1002 1152 moveto
997 1150 996 1145 998 1129 curveto
1000 1110 1005 1097 1018 1081 curveto
1023 1075 1029 1067 1031 1064 curveto
1035 1058 lineto
1009 1058 lineto
983 1057 lineto
981 1054 lineto
978 1051 978 1048 981 1045 curveto
983 1043 lineto
1015 1043 lineto
1046 1043 lineto
1048 1045 lineto
1051 1047 1051 1048 1050 1053 curveto
1049 1061 1044 1071 1033 1085 curveto
1018 1105 1013 1115 1012 1137 curveto
1011 1146 1010 1149 1009 1150 curveto
1007 1153 1005 1153 1002 1152 curveto
closepath
fill
newpath
1326 1153 moveto
1313 1149 1306 1140 1303 1125 curveto
1300 1114 1300 1078 1303 1068 curveto
1305 1059 1309 1053 1313 1048 curveto
1320 1042 1323 1041 1337 1041 curveto
1350 1041 1353 1042 1360 1049 curveto
1370 1057 1373 1073 1372 1103 curveto
1372 1126 1369 1135 1361 1144 curveto
1356 1150 1350 1153 1338 1153 curveto
1333 1153 1327 1153 1326 1153 curveto
closepath
1348 1137 moveto
1352 1134 1355 1128 1356 1121 curveto
1358 1113 1358 1080 1356 1073 curveto
1354 1059 1350 1056 1337 1056 curveto
1323 1056 1320 1059 1317 1072 curveto
1315 1081 1315 1113 1317 1121 curveto
1318 1128 1322 1135 1325 1137 curveto
1330 1139 1344 1139 1348 1137 curveto
closepath
fill
newpath
76 1151 moveto
74 1150 74 1148 74 1105 curveto
73 1060 lineto
64 1068 lineto
58 1073 53 1077 52 1077 curveto
46 1079 42 1073 43 1068 curveto
44 1067 51 1060 58 1054 curveto
73 1042 lineto
78 1042 lineto
82 1042 84 1042 86 1044 curveto
87 1045 88 1047 88 1097 curveto
88 1147 87 1149 86 1151 curveto
83 1153 79 1153 76 1151 curveto
closepath
fill
newpath
374 1151 moveto
371 1147 372 1144 380 1135 curveto
386 1128 399 1113 398 1112 curveto
398 1112 394 1112 389 1112 curveto
381 1112 379 1111 374 1109 curveto
363 1104 357 1095 355 1083 curveto
352 1067 361 1051 376 1044 curveto
381 1041 383 1041 391 1041 curveto
398 1041 400 1042 405 1044 curveto
428 1055 433 1082 415 1113 curveto
408 1125 400 1135 391 1144 curveto
384 1152 383 1153 379 1153 curveto
377 1153 375 1152 374 1151 curveto
closepath
402 1096 moveto
408 1094 410 1089 411 1081 curveto
412 1065 405 1056 390 1056 curveto
378 1055 370 1064 369 1076 curveto
368 1084 372 1092 378 1095 curveto
383 1098 396 1098 402 1096 curveto
closepath
fill
newpath
481 1151 moveto
478 1147 479 1144 487 1135 curveto
493 1128 506 1113 505 1112 curveto
505 1112 501 1112 496 1112 curveto
488 1112 486 1111 481 1109 curveto
470 1104 464 1095 462 1083 curveto
459 1067 468 1051 483 1044 curveto
488 1041 490 1041 498 1041 curveto
505 1041 507 1042 512 1044 curveto
535 1055 540 1082 522 1113 curveto
515 1125 507 1135 498 1144 curveto
491 1152 490 1153 486 1153 curveto
484 1153 482 1152 481 1151 curveto
closepath
509 1096 moveto
515 1094 517 1089 518 1081 curveto
519 1065 512 1056 497 1056 curveto
485 1055 477 1064 476 1076 curveto
475 1084 479 1092 485 1095 curveto
490 1098 503 1098 509 1096 curveto
closepath
fill
newpath
827 1151 moveto
826 1149 825 1146 825 1139 curveto
824 1129 lineto
807 1129 lineto
788 1129 784 1128 783 1123 curveto
781 1117 783 1111 799 1078 curveto
807 1060 815 1044 816 1043 curveto
820 1039 828 1042 828 1048 curveto
828 1049 821 1064 813 1081 curveto
805 1098 799 1112 799 1113 curveto
798 1113 804 1114 811 1114 curveto
824 1114 lineto
825 1104 lineto
826 1093 827 1091 832 1091 curveto
838 1091 840 1093 840 1104 curveto
840 1114 lineto
844 1114 lineto
850 1114 852 1115 854 1118 curveto
856 1124 851 1129 843 1129 curveto
840 1129 lineto
840 1139 lineto
840 1147 839 1149 838 1151 curveto
835 1153 829 1153 827 1151 curveto
closepath
fill
newpath
1238 1151 moveto
1237 1149 1236 1146 1236 1139 curveto
1235 1129 lineto
1218 1129 lineto
1199 1129 1195 1128 1194 1123 curveto
1192 1117 1194 1111 1210 1078 curveto
1218 1060 1226 1044 1227 1043 curveto
1231 1039 1239 1042 1239 1048 curveto
1239 1049 1232 1064 1224 1081 curveto
1216 1098 1210 1112 1210 1113 curveto
1209 1113 1215 1114 1222 1114 curveto
1235 1114 lineto
1236 1104 lineto
1237 1093 1238 1091 1243 1091 curveto
1249 1091 1251 1093 1251 1104 curveto
1251 1114 lineto
1255 1114 lineto
1261 1114 1263 1115 1265 1118 curveto
1267 1124 1262 1129 1254 1129 curveto
1251 1129 lineto
1251 1139 lineto
1251 1147 1250 1149 1249 1151 curveto
1246 1153 1240 1153 1238 1151 curveto
closepath
fill
newpath
1427 1151 moveto
1424 1147 1425 1144 1433 1135 curveto
1439 1128 1452 1113 1451 1112 curveto
1451 1112 1447 1112 1442 1112 curveto
1434 1112 1432 1111 1427 1109 curveto
1416 1104 1410 1095 1408 1083 curveto
1405 1067 1414 1051 1429 1044 curveto
1434 1041 1436 1041 1444 1041 curveto
1451 1041 1453 1042 1458 1044 curveto
1481 1055 1486 1082 1468 1113 curveto
1461 1125 1453 1135 1444 1144 curveto
1437 1152 1436 1153 1432 1153 curveto
1430 1153 1428 1152 1427 1151 curveto
closepath
1455 1096 moveto
1461 1094 1463 1089 1464 1081 curveto
1465 1065 1458 1056 1443 1056 curveto
1431 1055 1423 1064 1422 1076 curveto
1421 1084 1425 1092 1431 1095 curveto
1436 1098 1449 1098 1455 1096 curveto
closepath
fill
newpath
1733 1151 moveto
1730 1147 1731 1144 1739 1135 curveto
1745 1128 1758 1113 1757 1112 curveto
1757 1112 1753 1112 1748 1112 curveto
1740 1112 1738 1111 1733 1109 curveto
1722 1104 1716 1095 1714 1083 curveto
1711 1067 1720 1051 1735 1044 curveto
1740 1041 1742 1041 1750 1041 curveto
1757 1041 1759 1042 1764 1044 curveto
1787 1055 1792 1082 1774 1113 curveto
1767 1125 1759 1135 1750 1144 curveto
1743 1152 1742 1153 1738 1153 curveto
1736 1153 1734 1152 1733 1151 curveto
closepath
1761 1096 moveto
1767 1094 1769 1089 1770 1081 curveto
1771 1065 1764 1056 1749 1056 curveto
1737 1055 1729 1064 1728 1076 curveto
1727 1084 1731 1092 1737 1095 curveto
1742 1098 1755 1098 1761 1096 curveto
closepath
fill
newpath
576 1150 moveto
572 1149 571 1143 572 1133 curveto
574 1117 580 1109 599 1097 curveto
605 1093 612 1088 614 1086 curveto
624 1076 624 1065 614 1058 curveto
612 1056 610 1056 602 1056 curveto
592 1056 591 1056 585 1059 curveto
578 1063 577 1063 575 1061 curveto
571 1060 570 1055 571 1052 curveto
573 1049 585 1043 594 1041 curveto
614 1038 631 1047 635 1064 curveto
639 1080 631 1093 608 1108 curveto
593 1117 587 1126 587 1135 curveto
587 1138 lineto
610 1138 lineto
631 1138 633 1138 635 1139 curveto
637 1142 637 1147 634 1149 curveto
631 1151 630 1151 605 1151 curveto
588 1151 577 1151 576 1150 curveto
closepath
fill
newpath
1094 1150 moveto
1090 1149 1089 1143 1090 1133 curveto
1092 1117 1098 1109 1117 1097 curveto
1123 1093 1130 1088 1132 1086 curveto
1142 1076 1142 1065 1132 1058 curveto
1130 1056 1128 1056 1120 1056 curveto
1110 1056 1109 1056 1103 1059 curveto
1096 1063 1095 1063 1093 1061 curveto
1089 1060 1088 1055 1089 1052 curveto
1091 1049 1103 1043 1112 1041 curveto
1132 1038 1149 1047 1153 1064 curveto
1157 1080 1149 1093 1126 1108 curveto
1111 1117 1105 1126 1105 1135 curveto
1105 1138 lineto
1128 1138 lineto
1149 1138 1151 1138 1153 1139 curveto
1155 1142 1155 1147 1152 1149 curveto
1149 1151 1148 1151 1123 1151 curveto
1106 1151 1095 1151 1094 1150 curveto
closepath
fill
newpath
153 540 moveto
153 0 lineto
161 0 lineto
169 0 lineto
169 540 lineto
169 1080 lineto
161 1080 lineto
153 1080 lineto
153 540 lineto
closepath
fill
newpath
185 540 moveto
185 0 lineto
193 0 lineto
201 0 lineto
201 540 lineto
201 1080 lineto
193 1080 lineto
185 1080 lineto
185 540 lineto
closepath
fill
newpath
233 540 moveto
233 0 lineto
249 0 lineto
265 0 lineto
265 540 lineto
265 1080 lineto
249 1080 lineto
233 1080 lineto
233 540 lineto
closepath
fill
newpath
297 540 moveto
297 0 lineto
305 0 lineto
313 0 lineto
313 540 lineto
313 1080 lineto
305 1080 lineto
297 1080 lineto
297 540 lineto
closepath
fill
newpath
889 540 moveto
889 0 lineto
897 0 lineto
905 0 lineto
905 540 lineto
905 1080 lineto
897 1080 lineto
889 1080 lineto
889 540 lineto
closepath
fill
newpath
921 540 moveto
921 0 lineto
929 0 lineto
937 0 lineto
937 540 lineto
937 1080 lineto
929 1080 lineto
921 1080 lineto
921 540 lineto
closepath
fill
newpath
1513 540 moveto
1513 0 lineto
1537 0 lineto
1561 0 lineto
1561 540 lineto
1561 1080 lineto
1537 1080 lineto
1513 1080 lineto
1513 540 lineto
closepath
fill
newpath
1577 540 moveto
1577 0 lineto
1585 0 lineto
1593 0 lineto
1593 540 lineto
1593 1080 lineto
1585 1080 lineto
1577 1080 lineto
1577 540 lineto
closepath
fill
newpath
1625 540 moveto
1625 0 lineto
1633 0 lineto
1641 0 lineto
1641 540 lineto
1641 1080 lineto
1633 1080 lineto
1625 1080 lineto
1625 540 lineto
closepath
fill
newpath
1657 540 moveto
1657 0 lineto
1665 0 lineto
1673 0 lineto
1673 540 lineto
1673 1080 lineto
1665 1080 lineto
1657 1080 lineto
1657 540 lineto
closepath
fill
newpath
361 509 moveto
361 0 lineto
369 0 lineto
377 0 lineto
377 509 lineto
377 1018 lineto
369 1018 lineto
361 1018 lineto
361 509 lineto
closepath
fill
newpath
393 509 moveto
393 0 lineto
409 0 lineto
425 0 lineto
425 509 lineto
425 1018 lineto
409 1018 lineto
393 1018 lineto
393 509 lineto
closepath
fill
newpath
473 509 moveto
473 0 lineto
481 0 lineto
489 0 lineto
489 509 lineto
489 1018 lineto
481 1018 lineto
473 1018 lineto
473 509 lineto
closepath
fill
newpath
505 509 moveto
505 0 lineto
521 0 lineto
537 0 lineto
537 509 lineto
537 1018 lineto
521 1018 lineto
505 1018 lineto
505 509 lineto
closepath
fill
newpath
569 509 moveto
569 0 lineto
577 0 lineto
585 0 lineto
585 509 lineto
585 1018 lineto
577 1018 lineto
569 1018 lineto
569 509 lineto
closepath
fill
newpath
617 509 moveto
617 0 lineto
633 0 lineto
649 0 lineto
649 509 lineto
649 1018 lineto
633 1018 lineto
617 1018 lineto
617 509 lineto
closepath
fill
newpath
665 509 moveto
665 0 lineto
681 0 lineto
697 0 lineto
697 509 lineto
697 1018 lineto
681 1018 lineto
665 1018 lineto
665 509 lineto
closepath
fill
newpath
713 509 moveto
713 0 lineto
737 0 lineto
761 0 lineto
761 509 lineto
761 1018 lineto
737 1018 lineto
713 1018 lineto
713 509 lineto
closepath
fill
newpath
777 509 moveto
777 0 lineto
785 0 lineto
793 0 lineto
793 509 lineto
793 1018 lineto
785 1018 lineto
777 1018 lineto
777 509 lineto
closepath
fill
newpath
841 509 moveto
841 0 lineto
857 0 lineto
873 0 lineto
873 509 lineto
873 1018 lineto
857 1018 lineto
841 1018 lineto
841 509 lineto
closepath
fill
newpath
953 509 moveto
953 0 lineto
961 0 lineto
969 0 lineto
969 509 lineto
969 1018 lineto
961 1018 lineto
953 1018 lineto
953 509 lineto
closepath
fill
newpath
1017 509 moveto
1017 0 lineto
1025 0 lineto
1033 0 lineto
1033 509 lineto
1033 1018 lineto
1025 1018 lineto
1017 1018 lineto
1017 509 lineto
closepath
fill
newpath
1065 509 moveto
1065 0 lineto
1081 0 lineto
1097 0 lineto
1097 509 lineto
1097 1018 lineto
1081 1018 lineto
1065 1018 lineto
1065 509 lineto
closepath
fill
newpath
1113 509 moveto
1113 0 lineto
1129 0 lineto
1145 0 lineto
1145 509 lineto
1145 1018 lineto
1129 1018 lineto
1113 1018 lineto
1113 509 lineto
closepath
fill
newpath
1177 509 moveto
1177 0 lineto
1185 0 lineto
1193 0 lineto
1193 509 lineto
1193 1018 lineto
1185 1018 lineto
1177 1018 lineto
1177 509 lineto
closepath
fill
newpath
1209 509 moveto
1209 0 lineto
1233 0 lineto
1257 0 lineto
1257 509 lineto
1257 1018 lineto
1233 1018 lineto
1209 1018 lineto
1209 509 lineto
closepath
fill
newpath
1289 509 moveto
1289 0 lineto
1313 0 lineto
1337 0 lineto
1337 509 lineto
1337 1018 lineto
1313 1018 lineto
1289 1018 lineto
1289 509 lineto
closepath
fill
newpath
1369 509 moveto
1369 0 lineto
1377 0 lineto
1385 0 lineto
1385 509 lineto
1385 1018 lineto
1377 1018 lineto
1369 1018 lineto
1369 509 lineto
closepath
fill
newpath
1401 509 moveto
1401 0 lineto
1425 0 lineto
1449 0 lineto
1449 509 lineto
1449 1018 lineto
1425 1018 lineto
1401 1018 lineto
1401 509 lineto
closepath
fill
newpath
1465 509 moveto
1465 0 lineto
1473 0 lineto
1481 0 lineto
1481 509 lineto
1481 1018 lineto
1473 1018 lineto
1465 1018 lineto
1465 509 lineto
closepath
fill
showpage
%%EOF
