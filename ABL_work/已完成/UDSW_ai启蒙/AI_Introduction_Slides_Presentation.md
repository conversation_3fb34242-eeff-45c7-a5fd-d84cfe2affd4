---
marp: true
theme: default
paginate: true
backgroundColor: #f8f6f0
color: #000000
---

# AI技术全景介绍
## 从模型到应用的完整指南

---

## 课程大纲

- 🤖 **了解AI工具** - 主流模型对比
- 📝 **掌握Markdown** - AI时代必备技能  
- 💡 **学会提示词** - 与AI高效对话
- 🔍 **建立验证思维** - 理性使用AI
- 🚀 **用AI学AI** - 实践方法

---

## AI发展时间线

```mermaid
%%{init: {
  'theme': 'base',
  'themeVariables': {
    'primaryColor': '#e8ddd4',
    'primaryTextColor': '#000000',
    'primaryBorderColor': '#e8ddd4',
    'lineColor': '#e8ddd4',
    'sectionBkgColor': '#f8f6f0',
    'altSectionBkgColor': '#f8f6f0',
    'gridColor': '#e8ddd4',
    'secondaryColor': '#e8ddd4',
    'tertiaryColor': '#e8ddd4'
  }
}}%%
graph LR
    A[2017年<br/>Transformer架构<br/>注意力机制] --> B[2018年<br/>GPT-1<br/>BERT发布]
    A --> C[2019年<br/>GPT-2发布<br/>文本生成突破]
    
    B --> D[2020年<br/>GPT-3发布<br/>大模型时代]
    C --> D
    
    D --> E[2021年<br/>GitHub Copilot<br/>AI编程助手]
    
    E --> F1[2022年-对话AI<br/>ChatGPT发布<br/>AI对话爆发]
    E --> F2[2022年-图像AI<br/>Stable Diffusion<br/>Midjourney兴起]
    
    F1 --> G1[2023年-文本<br/>GPT-4发布<br/>Claude 3发布]
    F2 --> G2[2023年-多模态<br/>Gemini发布<br/>技术成熟]
    
    G1 --> H[2024年<br/>GPT-4o发布<br/>工具普及]
    G2 --> H
    
    style A fill:#f8f6f0,stroke:#e8ddd4,stroke-width:2px,color:#000000
    style B fill:#f8f6f0,stroke:#e8ddd4,stroke-width:2px,color:#000000
    style C fill:#f8f6f0,stroke:#e8ddd4,stroke-width:2px,color:#000000
    style D fill:#f8f6f0,stroke:#e8ddd4,stroke-width:2px,color:#000000
    style E fill:#f8f6f0,stroke:#e8ddd4,stroke-width:2px,color:#000000
    style F1 fill:#f8f6f0,stroke:#e8ddd4,stroke-width:2px,color:#000000
    style F2 fill:#f8f6f0,stroke:#e8ddd4,stroke-width:2px,color:#000000
    style G1 fill:#f8f6f0,stroke:#e8ddd4,stroke-width:2px,color:#000000
    style G2 fill:#f8f6f0,stroke:#e8ddd4,stroke-width:2px,color:#000000
    style H fill:#f8f6f0,stroke:#e8ddd4,stroke-width:2px,color:#000000
```

---

## 主流AI模型对比

| 厂商 | 主要模型 | 核心特点 | 擅长场景 |
|------|----------|----------|----------|
| **OpenAI** | GPT-4, ChatGPT | 综合能力强、多模态 | 写作助手、创意brainstorming、学习辅导 |
| **Google** | Gemini, Bard | 信息整合、生态集成 | 研究资料整理、多语言交流、图片分析 |
| **Anthropic** | Claude 4 | 安全可靠、长文本 | 文档分析、专业咨询、内容审核 |

---

## DeepSeek：国产AI新星

| 特点 | 说明 |
|------|------|
| **模型** | DeepSeek-R1（推理）、DeepSeek-V3（基础） |
| **优势** | 深度思考、中文能力最强 |
| **擅长** | 数学推理、代码编程、商业决策、技术难题 |
| **特色** | 层层递进、逻辑清晰、多角度分析 |

**适合场景：** 技术难题、商业策略、工厂生产问题

---

## 图像生成AI工具

1. **OpenAI DALL·E 3** - 自然语言描述、高质量输出
2. **Midjourney** - 艺术风格突出、社区活跃  
3. **Stable Diffusion** - 开源、高度可定制
4. **Flux AI** - 开源模型、快速生成
5. **Adobe Firefly** - 商业安全、版权友好

---

## Flux AI 实际案例

**能力特点：** 简单内容质量很高，复杂场景能力有限

**成功案例（简单内容）：**
<img src="软胶囊-成功.png" alt="软胶囊成功案例" width="300">

---

## Flux AI 局限性展示

**失败案例（复杂场景）：**
<img src="泡罩-失败.png" alt="泡罩失败案例" width="300">

**文字处理局限性：**
<img src="图像可以文字不行.png" alt="图像生成可以但文字处理不行" width="500">

---

## 为什么要学Markdown？

### 🔥 关键点：AI回复都是Markdown格式！

- ChatGPT、Claude、文心一言等AI都使用Markdown语法
- 理解Markdown = 看懂AI回复的"格式密码"
- 学习成本低（30分钟掌握基础）
- 与AI协作的必备技能

---

## Markdown vs Word文档

| 特性 | Word文档 | Markdown |
|------|----------|----------|
| **学习难度** | 中等（需要熟悉界面） | 简单（几个符号） |
| **AI兼容性** | ❌ AI回复需要转换 | ✅ AI回复就是此格式 |
| **文件大小** | 较大（包含格式信息） | 很小（纯文本） |
| **版本控制** | 困难（二进制格式） | 容易（文本格式） |

---

## Markdown基础语法

```markdown
# 一级标题
## 二级标题

**粗体文本**
*斜体文本*

- 无序列表项1
- 无序列表项2

[链接文本](https://example.com)
> 引用文本
```

---

## 推荐工具

1. **Notion** 📝
   - 支持Markdown语法，可直接粘贴AI内容

2. **GitHub** 🐙  
   - 全面支持Markdown，业界标准

3. **Cursor** 🎯
   - AI原生编辑器，特别适合AI辅助写作

---

## 提示词工程流程

```mermaid
%%{init: {
  'theme': 'base',
  'themeVariables': {
    'fontSize': '12px',
    'fontFamily': 'Arial'
  },
  'flowchart': {
    'htmlLabels': true,
    'curve': 'basis',
    'padding': 5
  }
}}%%
flowchart TD
    A[明确任务目标] --> B[选择提示词策略]
    B --> C{任务类型}
    
    C -->|简单问答| D[直接提示]
    C -->|复杂任务| E[分步提示]
    C -->|创意生成| F[角色扮演提示]
    C -->|代码生成| G[技术规范提示]
    
    D --> H[添加上下文]
    E --> I[任务分解]
    F --> J[设定角色背景]
    G --> K[指定编程语言和要求]
    
    H --> L[执行测试]
    I --> L
    J --> L
    K --> L
    
    L --> M{结果满意?}
    M -->|否| N[优化提示词]
    M -->|是| O[保存模板]
    
    N --> P[分析问题]
    P --> Q[调整策略]
    Q --> B
    
    O --> R[建立提示词库]
    
    style A fill:#f8f6f0,stroke:#8b7d6b,stroke-width:1px,color:#000000
    style O fill:#f8f6f0,stroke:#8b7d6b,stroke-width:1px,color:#000000
    style R fill:#f8f6f0,stroke:#8b7d6b,stroke-width:1px,color:#000000
```

---

## 提示词工程核心技巧

1. **清晰具体** - 避免模糊表达，提供充分上下文
2. **角色设定** - "你是一个资深软件工程师..."
3. **格式指定** - 要求特定输出格式
4. **示例引导** - 提供输入输出示例
5. **分步思考** - "请一步步分析"
6. **约束条件** - 字数限制、特定要求

---

## AI验证流程

```mermaid
%%{init: {
  'theme': 'base',
  'themeVariables': {
    'fontSize': '12px',
    'fontFamily': 'Arial'
  }
}}%%
flowchart TD
    A[获得AI回答] --> B{是否涉及事实?}
    
    B -->|是| C[验证事实准确性]
    B -->|否| D[评估逻辑合理性]
    
    C --> E[多源验证]
    E --> F[搜索引擎查证]
    E --> G[官方资料确认]
    E --> H[专业人士咨询]
    
    F --> I{信息一致?}
    G --> I
    H --> I
    
    D --> J[常识判断]
    J --> K[逻辑分析]
    K --> L{推理合理?}
    
    I -->|是| M[可信度高]
    I -->|否| N[存在争议]
    L -->|是| M
    L -->|否| O[逻辑有误]
    
    M --> P[谨慎采用]
    N --> Q[深入调研]
    O --> R[重新询问AI]
    
    Q --> S[获取更多信息]
    R --> T[调整提示词]
    
    S --> A
    T --> A
    
    P --> U[形成最终判断]
    
    style A fill:#f8f6f0,stroke:#8b7d6b,stroke-width:1px,color:#000000
    style U fill:#f8f6f0,stroke:#8b7d6b,stroke-width:1px,color:#000000
    style M fill:#e8f5e8,stroke:#4caf50,stroke-width:2px,color:#000000
    style N fill:#fff3cd,stroke:#ffc107,stroke-width:2px,color:#000000
    style O fill:#f8d7da,stroke:#dc3545,stroke-width:2px,color:#000000
```

---

## AI验证的重要性

### 为什么要验证AI？
- AI会"胡说八道"：看起来很对，实际可能是错的
- AI有局限性：知识有截止时间，可能过时  
- AI太自信：不确定的事也说得很肯定

### 正确心态
- AI是助手，不是老师
- 最终决定权在你
- 保持怀疑，但不要害怕使用

---

## 简单验证方法

- **多问几个AI** - 看看答案是否一致
- **搜索验证** - 用搜索引擎查证关键信息
- **要求AI提供证据** - 让AI给出文章链接、网站来源
- **查看官方资料** - 找官方网站、官方文档确认
- **常识判断** - 用你的生活经验判断是否合理

---

## AI学习路径

```mermaid
%%{init: {
  'theme': 'base',
  'themeVariables': {
    'fontSize': '12px',
    'fontFamily': 'Arial'
  }
}}%%
flowchart TD
    A[开始AI学习之旅] --> B[选择AI工具]
    
    B --> C[基础对话练习]
    C --> D[学习Markdown语法]
    D --> E[掌握提示词技巧]
    
    E --> F{学习方式选择}
    
    F -->|对话式| G[不懂就问<br/>要求类比<br/>追问细节]
    F -->|实验式| H[先试后学<br/>对比效果<br/>让AI分析]
    F -->|项目式| I[实际项目<br/>解决问题<br/>总结经验]
    
    G --> J[建立知识体系]
    H --> J
    I --> J
    
    J --> K[验证所学知识]
    K --> L{掌握程度}
    
    L -->|初级| M[继续基础练习]
    L -->|中级| N[尝试复杂任务]
    L -->|高级| O[教授他人]
    
    M --> E
    N --> P[深入专业领域]
    O --> Q[成为AI应用专家]
    
    P --> R[持续学习新技术]
    Q --> R
    
    R --> S[终身学习]
    
    style A fill:#f8f6f0,stroke:#8b7d6b,stroke-width:1px,color:#000000
    style S fill:#f8f6f0,stroke:#8b7d6b,stroke-width:1px,color:#000000
    style Q fill:#e8f5e8,stroke:#4caf50,stroke-width:2px,color:#000000
```

---

## 用AI学AI的方法

### 🎯 把AI当作你的学习伙伴

1. **对话式学习** - 不懂就问，要求类比，追问细节
2. **实验式学习** - 先试后学，对比效果，让AI分析
3. **验证式思维** - 交叉验证，要求证据，承认局限

### 三个关键心态
- **好奇心** - 像小孩一样不停地问"为什么"
- **实验精神** - 不怕试错，多尝试不同方法  
- **批判思维** - 不盲信，要验证和思考

---

## 免费AI学习资源

### 🚀 国内免费工具
- **DeepSeek** (chat.deepseek.com)
  - 开源模型、推理能力强、支持代码生成
  - 适合：编程学习、数学解题、逻辑推理
- **腾讯元宝** (集成DeepSeek技术)

### 🌍 海外工具
- ChatGPT、Claude、Google Gemini

---

## 总结

### 记住：
AI是强大的助手，但人类的创造力、批判思维和最终决策权始终是不可替代的。

### 行动建议：
1. 选择适合的AI工具开始实践
2. 学会Markdown基础语法
3. 掌握提示词技巧
4. 建立验证思维
5. 保持学习和探索的心态

**让我们一起在AI时代中智慧前行！** 